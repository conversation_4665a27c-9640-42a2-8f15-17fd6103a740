-- PostgreSQL Database Schema for My Group Application
-- Run this script after creating the my_group_db database

-- Enable UUID extension for generating unique IDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create my_group_app table
CREATE TABLE my_group_app (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    logo TEXT,
    url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create my_group_member table
CREATE TABLE my_group_member (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create my_group_user table
CREATE TABLE my_group_user (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL, -- Will store hashed passwords
    my_group_app_id UUID REFERENCES my_group_app(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL CHECK (role IN ('SUPER_ADMIN', 'CORPORATE', 'BRANCH', 'USER')),
    created_by UUID REFERENCES my_group_user(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create my_group_user_group table (junction table for user-member relationships)
CREATE TABLE my_group_user_group (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES my_group_user(id) ON DELETE CASCADE,
    my_group_member_id UUID NOT NULL REFERENCES my_group_member(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, my_group_member_id)
);

-- Create my_group_user_details table
CREATE TABLE my_group_user_details (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    my_group_user_id UUID UNIQUE NOT NULL REFERENCES my_group_user(id) ON DELETE CASCADE,
    full_name VARCHAR(255),
    display_name VARCHAR(100),
    email_id VARCHAR(255) UNIQUE,
    gender VARCHAR(20) CHECK (gender IN ('Male', 'Female', 'Other', 'Prefer not to say')),
    martial_status VARCHAR(20) CHECK (martial_status IN ('Single', 'Married', 'Divorced', 'Widowed')),
    country_id VARCHAR(10),
    state_id VARCHAR(10),
    district_id VARCHAR(10),
    nationality VARCHAR(100),
    education VARCHAR(255),
    profession VARCHAR(255),
    photo TEXT, -- Base64 encoded image or file path
    date_of_birth DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_my_group_user_username ON my_group_user(username);
CREATE INDEX idx_my_group_user_role ON my_group_user(role);
CREATE INDEX idx_my_group_user_app_id ON my_group_user(my_group_app_id);
CREATE INDEX idx_my_group_user_created_by ON my_group_user(created_by);
CREATE INDEX idx_my_group_user_details_email ON my_group_user_details(email_id);
CREATE INDEX idx_my_group_user_group_user_id ON my_group_user_group(user_id);
CREATE INDEX idx_my_group_user_group_member_id ON my_group_user_group(my_group_member_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_my_group_app_updated_at BEFORE UPDATE ON my_group_app FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_my_group_member_updated_at BEFORE UPDATE ON my_group_member FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_my_group_user_updated_at BEFORE UPDATE ON my_group_user FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_my_group_user_details_updated_at BEFORE UPDATE ON my_group_user_details FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
