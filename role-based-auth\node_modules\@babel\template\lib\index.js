"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.statements = exports.statement = exports.smart = exports.program = exports.expression = exports.default = void 0;
var formatters = require("./formatters.js");
var _builder = require("./builder.js");
const smart = exports.smart = (0, _builder.default)(formatters.smart);
const statement = exports.statement = (0, _builder.default)(formatters.statement);
const statements = exports.statements = (0, _builder.default)(formatters.statements);
const expression = exports.expression = (0, _builder.default)(formatters.expression);
const program = exports.program = (0, _builder.default)(formatters.program);
var _default = exports.default = Object.assign(smart.bind(undefined), {
  smart,
  statement,
  statements,
  expression,
  program,
  ast: smart.ast
});

//# sourceMappingURL=index.js.map
