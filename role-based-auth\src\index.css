:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
  margin: 0 auto;
  text-align: center;
}

.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dashboard-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.navbar-brand {
  font-weight: bold;
}

.card-shadow {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-custom {
  border-radius: 25px;
  padding: 10px 30px;
  font-weight: 500;
}

.user-card {
  transition: transform 0.2s;
}

.user-card:hover {
  transform: translateY(-2px);
}

/* Default Dashboard Styles */
.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.user-avatar .avatar-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
}

.stat-card {
  transition: transform 0.2s ease;
  border-radius: 12px;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 2rem;
}

.action-card {
  transition: all 0.2s ease;
  border-radius: 12px;
  cursor: pointer;
}

.action-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.action-icon {
  transition: transform 0.2s ease;
}

.action-card:hover .action-icon {
  transform: scale(1.1);
}

.activity-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.activity-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.sidebar-menu .menu-item {
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.sidebar-menu .menu-item:hover {
  background-color: #f8f9fa;
}

.info-item strong {
  color: #495057;
  font-size: 0.9rem;
}

.cursor-pointer {
  cursor: pointer;
}

/* Dark mode styles */
.dark-mode {
  background-color: #1a1a1a;
  color: #ffffff;
}

.dark-mode .card {
  background-color: #2d2d2d;
  border-color: #404040;
  color: #ffffff;
}

.dark-mode .text-muted {
  color: #adb5bd !important;
}

.dark-mode .sidebar-menu .menu-item:hover {
  background-color: #404040;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .user-avatar .avatar-circle {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .stat-icon {
    font-size: 1.5rem;
  }

  .action-icon {
    font-size: 1.5rem;
  }
}
