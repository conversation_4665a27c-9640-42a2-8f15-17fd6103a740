:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
  margin: 0 auto;
  text-align: center;
}

.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dashboard-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.navbar-brand {
  font-weight: bold;
}

.card-shadow {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-custom {
  border-radius: 25px;
  padding: 10px 30px;
  font-weight: 500;
}

.user-card {
  transition: transform 0.2s;
}

.user-card:hover {
  transform: translateY(-2px);
}

/* Default Dashboard Styles */
.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.user-avatar .avatar-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
}

.stat-card {
  transition: transform 0.2s ease;
  border-radius: 12px;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 2rem;
}

.action-card {
  transition: all 0.2s ease;
  border-radius: 12px;
  cursor: pointer;
}

.action-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.action-icon {
  transition: transform 0.2s ease;
}

.action-card:hover .action-icon {
  transform: scale(1.1);
}

.activity-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.activity-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.sidebar-menu .menu-item {
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.sidebar-menu .menu-item:hover {
  background-color: #f8f9fa;
}

.info-item strong {
  color: #495057;
  font-size: 0.9rem;
}

.cursor-pointer {
  cursor: pointer;
}

/* Dark mode styles */
.dark-mode {
  background-color: #1a1a1a;
  color: #ffffff;
}

.dark-mode .card {
  background-color: #2d2d2d;
  border-color: #404040;
  color: #ffffff;
}

.dark-mode .text-muted {
  color: #adb5bd !important;
}

.dark-mode .sidebar-menu .menu-item:hover {
  background-color: #404040;
}

/* Auth Page Styles */
.auth-page-container {
  min-height: 100vh;
}

.auth-background {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.auth-card {
  border-radius: 20px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.brand-logo .logo-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  font-weight: bold;
  margin: 0 auto;
}

.brand-title {
  font-weight: 600;
  margin-bottom: 0;
}

.auth-tabs .nav-link {
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  background: transparent;
  color: #6c757d;
}

.auth-tabs .nav-link.active {
  background: #007bff;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.auth-tabs .nav-link:hover:not(.active) {
  background: #f8f9fa;
  color: #007bff;
}

/* Admin Login Styles */
.admin-login-container {
  min-height: 100vh;
}

.admin-background {
  background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
  min-height: 100vh;
}

.admin-card {
  border-radius: 20px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.admin-logo .logo-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  margin: 0 auto;
}

.demo-account {
  transition: all 0.2s ease;
  cursor: pointer;
}

.demo-account:hover {
  background-color: #e9ecef !important;
  transform: translateY(-1px);
}

/* Dashboard Background for Registration */
.dashboard-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

.auth-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  pointer-events: none;
}

.auth-overlay .modal {
  pointer-events: all;
}

/* Two-step registration styles */
.registration-step-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.step-circle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10px;
  font-weight: bold;
  color: white;
}

.step-circle.active {
  background-color: #007bff;
}

.step-circle.completed {
  background-color: #28a745;
}

.step-circle.pending {
  background-color: #6c757d;
}

.step-line {
  width: 50px;
  height: 2px;
  background-color: #dee2e6;
  margin-top: 14px;
}

.step-line.completed {
  background-color: #28a745;
}

/* Form Enhancements */
.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary, .btn-info, .btn-danger, .btn-success {
  border-radius: 25px;
  font-weight: 500;
  padding: 12px 24px;
  transition: all 0.3s ease;
}

.btn-primary:hover, .btn-info:hover, .btn-danger:hover, .btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Modal enhancements */
.modal-content {
  border-radius: 15px;
  border: none;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
  border-bottom: 1px solid #e9ecef;
  border-radius: 15px 15px 0 0;
}

.modal-body {
  padding: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .user-avatar .avatar-circle {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .stat-icon {
    font-size: 1.5rem;
  }

  .action-icon {
    font-size: 1.5rem;
  }

  .auth-card, .admin-card {
    margin: 20px;
  }

  .brand-logo .logo-circle, .admin-logo .logo-circle {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }
}

/* Home Screen Styles */
.home-screen {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.top-nav {
  background-color: #2c5aa0;
  color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2px;
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.grid-dot {
  width: 6px;
  height: 6px;
  background-color: white;
  border-radius: 1px;
}

.nav-label {
  font-size: 10px;
  color: white;
}

.top-apps-row {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.top-app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.top-app-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-bottom: 4px;
}

.top-app-label {
  font-size: 10px;
  color: white;
  text-align: center;
}

.profile-section {
  display: flex;
  justify-content: center;
}

.profile-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #ff6b6b;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 12px;
}

.main-content {
  padding-top: 2rem;
}

.apps-section {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.apps-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.app-button {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.app-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.app-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.app-name {
  color: white;
  font-size: 18px;
  font-weight: 500;
}
