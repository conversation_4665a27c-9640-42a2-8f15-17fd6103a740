import React from 'react';
import { Alert } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';

const DatabaseStatus = () => {
  const { dbConnected } = useAuth();

  if (dbConnected) {
    return (
      <Alert variant="success" className="mb-3">
        <i className="bi bi-check-circle-fill me-2"></i>
        Connected to PostgreSQL database
      </Alert>
    );
  }

  return (
    <Alert variant="warning" className="mb-3">
      <i className="bi bi-exclamation-triangle-fill me-2"></i>
      Database not connected - using fallback mode. 
      <br />
      <small>
        To connect to PostgreSQL:
        <br />
        1. Install PostgreSQL and create 'my_group_db' database
        <br />
        2. Run the schema.sql and sample_data.sql scripts
        <br />
        3. Update .env.local with your database credentials
      </small>
    </Alert>
  );
};

export default DatabaseStatus;
