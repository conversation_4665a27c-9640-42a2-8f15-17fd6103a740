import React, { useState } from 'react';
import { Container, Row, Col, Card, Nav, Tab } from 'react-bootstrap';
import Login from './Login';
import Register from './Register';

const AuthPage = () => {
  const [activeTab, setActiveTab] = useState('login');

  return (
    <div className="auth-page-container">
      <div className="auth-background">
        <Container className="d-flex align-items-center justify-content-center min-vh-100">
          <Row className="w-100 justify-content-center">
            <Col md={6} lg={5} xl={4}>
              <Card className="auth-card shadow-lg border-0">
                <Card.Body className="p-4">
                  {/* Logo/Brand Section */}
                  <div className="text-center mb-4">
                    <div className="brand-logo mb-3">
                      <div className="logo-circle bg-primary text-white">
                        MG
                      </div>
                    </div>
                    <h4 className="brand-title text-primary">My Group</h4>
                    <p className="text-muted">Welcome to our community</p>
                  </div>

                  {/* Tab Navigation */}
                  <Tab.Container activeKey={activeTab} onSelect={setActiveTab}>
                    <Nav variant="pills" className="auth-tabs mb-4" fill>
                      <Nav.Item>
                        <Nav.Link 
                          eventKey="login" 
                          className={`auth-tab ${activeTab === 'login' ? 'active' : ''}`}
                        >
                          Login
                        </Nav.Link>
                      </Nav.Item>
                      <Nav.Item>
                        <Nav.Link 
                          eventKey="register"
                          className={`auth-tab ${activeTab === 'register' ? 'active' : ''}`}
                        >
                          Register
                        </Nav.Link>
                      </Nav.Item>
                    </Nav>

                    <Tab.Content>
                      <Tab.Pane eventKey="login">
                        <Login />
                      </Tab.Pane>
                      <Tab.Pane eventKey="register">
                        <Register />
                      </Tab.Pane>
                    </Tab.Content>
                  </Tab.Container>

                  {/* Footer */}
                  <div className="text-center mt-4">
                    <small className="text-muted">
                      By continuing, you agree to our Terms of Service and Privacy Policy
                    </small>
                  </div>
                </Card.Body>
              </Card>

              {/* Admin Login Link */}
              <div className="text-center mt-3">
                <small>
                  <a href="/admin" className="text-muted text-decoration-none">
                    🔐 Admin Login
                  </a>
                </small>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </div>
  );
};

export default AuthPage;
