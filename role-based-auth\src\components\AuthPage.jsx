import React, { useState } from 'react';
import { Container, Row, Col, Card, Nav, Tab, Modal } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import Login from './Login';
import Register from './Register';
import DefaultDashboard from './DefaultDashboard';

const AuthPage = () => {
  const [activeTab, setActiveTab] = useState('login');
  const [showRegisterModal, setShowRegisterModal] = useState(false);
  const { currentUser } = useAuth();

  // If user is logged in, show dashboard
  if (currentUser) {
    return <DefaultDashboard />;
  }

  return (
    <div className="auth-page-container">
      <div className="auth-background">
        <Container className="d-flex align-items-center justify-content-center min-vh-100">
          <Row className="w-100 justify-content-center">
            <Col md={6} lg={5} xl={4}>
              <Card className="auth-card shadow-lg border-0">
                <Card.Body className="p-4">
                  {/* Logo/Brand Section */}
                  <div className="text-center mb-4">
                    <div className="brand-logo mb-3">
                      <div className="logo-circle bg-primary text-white">
                        MG
                      </div>
                    </div>
                    <h4 className="brand-title text-primary">My Group</h4>
                    <p className="text-muted">Welcome to our community</p>
                  </div>

                  {/* Tab Navigation */}
                  <Nav variant="pills" className="auth-tabs mb-4" fill>
                    <Nav.Item>
                      <Nav.Link
                        active={activeTab === 'login'}
                        onClick={() => setActiveTab('login')}
                        className={`auth-tab ${activeTab === 'login' ? 'active' : ''}`}
                      >
                        Login
                      </Nav.Link>
                    </Nav.Item>
                    <Nav.Item>
                      <Nav.Link
                        active={activeTab === 'register'}
                        onClick={() => {
                          setActiveTab('register');
                          setShowRegisterModal(true);
                        }}
                        className={`auth-tab ${activeTab === 'register' ? 'active' : ''}`}
                      >
                        Register
                      </Nav.Link>
                    </Nav.Item>
                  </Nav>

                  {/* Only show Login form */}
                  {activeTab === 'login' && <Login />}

                  {/* Footer */}
                  <div className="text-center mt-4">
                    <small className="text-muted">
                      By continuing, you agree to our Terms of Service and Privacy Policy
                    </small>
                  </div>
                </Card.Body>
              </Card>

              {/* Admin Login Link */}
              <div className="text-center mt-3">
                <small>
                  <a href="/admin" className="text-muted text-decoration-none">
                    🔐 Admin Login
                  </a>
                </small>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      {/* Registration Modal */}
      <Modal
        show={showRegisterModal}
        onHide={() => {
          setShowRegisterModal(false);
          setActiveTab('login');
        }}
        size="lg"
        centered
        backdrop="static"
      >
        <Modal.Header closeButton>
          <Modal.Title>Create Your Account</Modal.Title>
        </Modal.Header>
        <Modal.Body className="p-0">
          <Register onRegistrationComplete={() => {
            setShowRegisterModal(false);
            setActiveTab('login');
          }} />
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default AuthPage;
