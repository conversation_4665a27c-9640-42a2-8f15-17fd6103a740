import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON>, InputGroup } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const Register = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    mobileNumber: '',
    password: '',
    confirmPassword: ''
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { createUser } = useAuth();
  const navigate = useNavigate();

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const validateForm = () => {
    if (!formData.fullName.trim()) {
      setError('Full name is required');
      return false;
    }
    if (!formData.mobileNumber.trim()) {
      setError('Mobile number is required');
      return false;
    }
    if (formData.mobileNumber.length < 10) {
      setError('Mobile number must be at least 10 digits');
      return false;
    }
    if (!formData.password) {
      setError('Password is required');
      return false;
    }
    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters');
      return false;
    }
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Create user with default role as 'USER'
      const userData = {
        username: formData.mobileNumber, // Use mobile number as username
        password: formData.password,
        name: formData.fullName,
        email: `${formData.mobileNumber}@mygroup.com`, // Generate email from mobile
        role: 'USER', // Default role for registered users
        mobile: formData.mobileNumber
      };

      await createUser(userData);
      setSuccess('Registration successful! You can now login with your mobile number.');
      
      // Clear form
      setFormData({
        fullName: '',
        mobileNumber: '',
        password: '',
        confirmPassword: ''
      });

      // Redirect to login after 2 seconds
      setTimeout(() => {
        navigate('/');
      }, 2000);

    } catch (err) {
      setError('Registration failed: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form onSubmit={handleSubmit}>
      {error && <Alert variant="danger">{error}</Alert>}
      {success && <Alert variant="success">{success}</Alert>}

      <Form.Group className="mb-3">
        <Form.Label>
          Full Name <span className="text-danger">*</span>
        </Form.Label>
        <Form.Control
          type="text"
          name="fullName"
          placeholder="Full Name"
          value={formData.fullName}
          onChange={handleInputChange}
          disabled={loading}
          required
        />
      </Form.Group>

      <Form.Group className="mb-3">
        <Form.Label>
          Mobile Number <span className="text-danger">*</span>
        </Form.Label>
        <InputGroup>
          <InputGroup.Text>+91</InputGroup.Text>
          <Form.Control
            type="tel"
            name="mobileNumber"
            placeholder="Mobile Number"
            value={formData.mobileNumber}
            onChange={handleInputChange}
            disabled={loading}
            maxLength="10"
            pattern="[0-9]{10}"
            required
          />
        </InputGroup>
      </Form.Group>

      <Form.Group className="mb-3">
        <Form.Label>
          Password <span className="text-danger">*</span>
        </Form.Label>
        <InputGroup>
          <Form.Control
            type={showPassword ? "text" : "password"}
            name="password"
            placeholder="Password"
            value={formData.password}
            onChange={handleInputChange}
            disabled={loading}
            required
          />
          <Button
            variant="outline-secondary"
            onClick={() => setShowPassword(!showPassword)}
            disabled={loading}
          >
            {showPassword ? '🙈' : '👁️'}
          </Button>
        </InputGroup>
      </Form.Group>

      <Form.Group className="mb-4">
        <Form.Label>
          Confirm Password <span className="text-danger">*</span>
        </Form.Label>
        <InputGroup>
          <Form.Control
            type={showConfirmPassword ? "text" : "password"}
            name="confirmPassword"
            placeholder="Confirm Password"
            value={formData.confirmPassword}
            onChange={handleInputChange}
            disabled={loading}
            required
          />
          <Button
            variant="outline-secondary"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            disabled={loading}
          >
            {showConfirmPassword ? '🙈' : '👁️'}
          </Button>
        </InputGroup>
      </Form.Group>

      <div className="d-grid">
        <Button
          variant="info"
          type="submit"
          size="lg"
          disabled={loading}
          className="rounded-pill"
        >
          {loading ? (
            <>
              <Spinner
                as="span"
                animation="border"
                size="sm"
                role="status"
                aria-hidden="true"
                className="me-2"
              />
              Registering...
            </>
          ) : (
            'Register'
          )}
        </Button>
      </div>
    </Form>
  );
};

export default Register;
