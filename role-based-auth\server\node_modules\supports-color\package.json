{"name": "supports-color", "version": "5.5.0", "description": "Detect whether a terminal supports color", "license": "MIT", "repository": "chalk/supports-color", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "browser.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "dependencies": {"has-flag": "^3.0.0"}, "devDependencies": {"ava": "^0.25.0", "import-fresh": "^2.0.0", "xo": "^0.20.0"}, "browser": "browser.js"}