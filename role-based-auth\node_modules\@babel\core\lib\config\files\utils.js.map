{"version": 3, "names": ["_caching", "require", "fs", "_fs2", "data", "makeStaticFileCache", "fn", "makeStrongCache", "filepath", "cache", "cached", "invalidate", "fileMtime", "readFile", "nodeFs", "existsSync", "statSync", "mtime", "e", "code"], "sources": ["../../../src/config/files/utils.ts"], "sourcesContent": ["import type { <PERSON><PERSON> } from \"gensync\";\n\nimport { makeStrongCache } from \"../caching.ts\";\nimport type { CacheConfigurator } from \"../caching.ts\";\nimport * as fs from \"../../gensync-utils/fs.ts\";\nimport nodeFs from \"node:fs\";\n\nexport function makeStaticFileCache<T>(\n  fn: (filepath: string, contents: string) => T,\n) {\n  return makeStrongCache(function* (\n    filepath: string,\n    cache: CacheConfigurator<void>,\n  ): Handler<null | T> {\n    const cached = cache.invalidate(() => fileMtime(filepath));\n\n    if (cached === null) {\n      return null;\n    }\n\n    return fn(filepath, yield* fs.readFile(filepath, \"utf8\"));\n  });\n}\n\nfunction fileMtime(filepath: string): number | null {\n  if (!nodeFs.existsSync(filepath)) return null;\n\n  try {\n    return +nodeFs.statSync(filepath).mtime;\n  } catch (e) {\n    if (e.code !== \"ENOENT\" && e.code !== \"ENOTDIR\") throw e;\n  }\n\n  return null;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,EAAA,GAAAD,OAAA;AACA,SAAAE,KAAA;EAAA,MAAAC,IAAA,GAAAH,OAAA;EAAAE,IAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,SAASC,mBAAmBA,CACjCC,EAA6C,EAC7C;EACA,OAAO,IAAAC,wBAAe,EAAC,WACrBC,QAAgB,EAChBC,KAA8B,EACX;IACnB,MAAMC,MAAM,GAAGD,KAAK,CAACE,UAAU,CAAC,MAAMC,SAAS,CAACJ,QAAQ,CAAC,CAAC;IAE1D,IAAIE,MAAM,KAAK,IAAI,EAAE;MACnB,OAAO,IAAI;IACb;IAEA,OAAOJ,EAAE,CAACE,QAAQ,EAAE,OAAON,EAAE,CAACW,QAAQ,CAACL,QAAQ,EAAE,MAAM,CAAC,CAAC;EAC3D,CAAC,CAAC;AACJ;AAEA,SAASI,SAASA,CAACJ,QAAgB,EAAiB;EAClD,IAAI,CAACM,KAAKA,CAAC,CAACC,UAAU,CAACP,QAAQ,CAAC,EAAE,OAAO,IAAI;EAE7C,IAAI;IACF,OAAO,CAACM,KAAKA,CAAC,CAACE,QAAQ,CAACR,QAAQ,CAAC,CAACS,KAAK;EACzC,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,IAAIA,CAAC,CAACC,IAAI,KAAK,QAAQ,IAAID,CAAC,CAACC,IAAI,KAAK,SAAS,EAAE,MAAMD,CAAC;EAC1D;EAEA,OAAO,IAAI;AACb;AAAC", "ignoreList": []}