/* Admin Dashboard Styles */
.admin-dashboard {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.admin-sidebar {
  width: 250px;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  min-height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: 20px;
  background: rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h4 {
  color: #fff;
  margin: 0;
  font-weight: 600;
  font-size: 1.5rem;
}

.sidebar-menu {
  padding: 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #ecf0f1;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  position: relative;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-left-color: #3498db;
}

.menu-item.active {
  background: rgba(52, 152, 219, 0.2);
  border-left-color: #3498db;
  color: #fff;
}

.menu-icon {
  font-size: 1.2rem;
  margin-right: 12px;
  width: 20px;
  text-align: center;
}

.menu-label {
  flex: 1;
  font-weight: 500;
}

.submenu-arrow {
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.submenu {
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.submenu-item {
  padding: 12px 20px 12px 50px;
  color: #bdc3c7;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.submenu-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #ecf0f1;
  border-left-color: #e74c3c;
}

.submenu-item.active {
  background: rgba(231, 76, 60, 0.2);
  border-left-color: #e74c3c;
  color: #fff;
}

.submenu-label {
  font-weight: 400;
  font-size: 0.9rem;
}

.admin-content {
  margin-left: 250px;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.content-header {
  background: #fff;
  padding: 15px 30px;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content-header span {
  color: #6c757d;
  font-size: 0.9rem;
}

.content-body {
  padding: 30px;
}

.content-body h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-weight: 600;
}

/* Card Styles */
.card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-radius: 10px 10px 0 0 !important;
  border: none;
  padding: 15px 20px;
}

.card-header h5 {
  margin: 0;
  font-weight: 600;
}

.card-body {
  padding: 20px;
}

/* Table Styles */
.table {
  margin-bottom: 0;
}

.table th {
  background-color: #f8f9fa;
  border-top: none;
  font-weight: 600;
  color: #495057;
  padding: 15px 12px;
}

.table td {
  padding: 15px 12px;
  vertical-align: middle;
  border-top: 1px solid #e9ecef;
}

.table tbody tr:hover {
  background-color: #f8f9fa;
}

/* Button Styles */
.btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
}

.btn-outline-warning {
  color: #f39c12;
  border-color: #f39c12;
}

.btn-outline-warning:hover {
  background-color: #f39c12;
  border-color: #f39c12;
}

.btn-outline-danger {
  color: #e74c3c;
  border-color: #e74c3c;
}

.btn-outline-danger:hover {
  background-color: #e74c3c;
  border-color: #e74c3c;
}

/* Form Styles */
.form-control {
  border-radius: 6px;
  border: 1px solid #ced4da;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-select {
  border-radius: 6px;
  border: 1px solid #ced4da;
}

.form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Switch Styles */
.form-check-input:checked {
  background-color: #28a745;
  border-color: #28a745;
}

/* Modal Styles */
.modal-content {
  border-radius: 10px;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-radius: 10px 10px 0 0;
  border-bottom: none;
}

.modal-title {
  font-weight: 600;
}

.btn-close {
  filter: invert(1);
}

/* Alert Styles */
.alert {
  border-radius: 8px;
  border: none;
  font-weight: 500;
}

.alert-success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
}

/* Dashboard Stats Cards */
.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-radius: 15px;
  padding: 25px;
  text-align: center;
  margin-bottom: 20px;
}

.stats-card h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
}

.stats-card p {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-sidebar {
    width: 200px;
  }
  
  .admin-content {
    margin-left: 200px;
  }
  
  .content-body {
    padding: 20px;
  }
}

@media (max-width: 576px) {
  .admin-sidebar {
    width: 100%;
    position: relative;
    min-height: auto;
  }
  
  .admin-content {
    margin-left: 0;
  }
  
  .content-body {
    padding: 15px;
  }
  
  .table-responsive {
    font-size: 0.9rem;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-body > * {
  animation: fadeIn 0.5s ease-out;
}
