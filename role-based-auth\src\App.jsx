import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import Login from './components/Login';
import SuperAdminDashboard from './components/SuperAdminDashboard';
import CorporateDashboard from './components/CorporateDashboard';
import BranchDashboard from './components/BranchDashboard';
import ProtectedRoute from './components/ProtectedRoute';

// Component to handle root route redirection
const RootRedirect = () => {
  const { currentUser } = useAuth();

  if (!currentUser) {
    return <Login />;
  }

  // Redirect based on user role
  switch (currentUser.role) {
    case 'SUPER_ADMIN':
      return <Navigate to="/super-admin" replace />;
    case 'CORPORATE':
      return <Navigate to="/corporate" replace />;
    case 'BRANCH':
      return <Navigate to="/branch" replace />;
    default:
      return <Login />;
  }
};

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            {/* Root route */}
            <Route path="/" element={<RootRedirect />} />
            
            {/* Super Admin Dashboard */}
            <Route 
              path="/super-admin" 
              element={
                <ProtectedRoute allowedRoles={['SUPER_ADMIN']}>
                  <SuperAdminDashboard />
                </ProtectedRoute>
              } 
            />
            
            {/* Corporate Dashboard */}
            <Route 
              path="/corporate" 
              element={
                <ProtectedRoute allowedRoles={['CORPORATE']}>
                  <CorporateDashboard />
                </ProtectedRoute>
              } 
            />
            
            {/* Branch Dashboard */}
            <Route 
              path="/branch" 
              element={
                <ProtectedRoute allowedRoles={['BRANCH']}>
                  <BranchDashboard />
                </ProtectedRoute>
              } 
            />
            
            {/* Catch all route - redirect to root */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
