import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Mock user database - in a real app, this would be in a backend
const mockUsers = [
  {
    id: 1,
    username: 'superadmin',
    password: 'admin123',
    role: 'SUPER_ADMIN',
    name: 'Super Administrator'
  },
  {
    id: 2,
    username: 'corporate1',
    password: 'corp123',
    role: 'CORPORATE',
    name: 'Corporate User 1',
    createdBy: 1
  },
  {
    id: 3,
    username: 'branch1',
    password: 'branch123',
    role: 'BRANCH',
    name: 'Branch User 1',
    createdBy: 2
  }
];

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [users, setUsers] = useState(mockUsers);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in from localStorage
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
      setCurrentUser(JSON.parse(savedUser));
    }
    setLoading(false);
  }, []);

  const login = async (username, password) => {
    const user = users.find(u => u.username === username && u.password === password);
    if (user) {
      setCurrentUser(user);
      localStorage.setItem('currentUser', JSON.stringify(user));
      return { success: true, user };
    }
    return { success: false, error: 'Invalid credentials' };
  };

  const logout = () => {
    setCurrentUser(null);
    localStorage.removeItem('currentUser');
  };

  const createUser = (userData) => {
    const newUser = {
      id: users.length + 1,
      ...userData,
      createdBy: currentUser.id
    };
    setUsers(prev => [...prev, newUser]);
    return newUser;
  };

  const getUsersByCreator = (creatorId) => {
    return users.filter(user => user.createdBy === creatorId);
  };

  const value = {
    currentUser,
    users,
    login,
    logout,
    createUser,
    getUsersByCreator,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
