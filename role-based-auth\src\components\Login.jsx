import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON> } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const Login = () => {
  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleInputChange = (e) => {
    setCredentials({
      ...credentials,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!credentials.username || !credentials.password) {
      setError('Please fill in all fields');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const result = await login(credentials.username, credentials.password);
      if (result.success) {
        // All users go to default dashboard first
        navigate('/');
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError('An error occurred during login');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form onSubmit={handleSubmit}>
      {error && <Alert variant="danger">{error}</Alert>}

      <Form.Group className="mb-3">
        <Form.Label>Username / Mobile Number</Form.Label>
        <Form.Control
          type="text"
          name="username"
          placeholder="Enter username or mobile number"
          value={credentials.username}
          onChange={handleInputChange}
          disabled={loading}
          required
        />
      </Form.Group>

      <Form.Group className="mb-4">
        <Form.Label>Password</Form.Label>
        <Form.Control
          type="password"
          name="password"
          placeholder="Enter password"
          value={credentials.password}
          onChange={handleInputChange}
          disabled={loading}
          required
        />
      </Form.Group>

      <div className="d-grid">
        <Button
          variant="primary"
          type="submit"
          size="lg"
          disabled={loading}
          className="rounded-pill"
        >
          {loading ? (
            <>
              <Spinner
                as="span"
                animation="border"
                size="sm"
                role="status"
                aria-hidden="true"
                className="me-2"
              />
              Logging in...
            </>
          ) : (
            'Login'
          )}
        </Button>
      </div>

      {/* Demo accounts for testing */}
      <div className="mt-4">
        <h6 className="text-center text-muted mb-3">Quick Login (Demo)</h6>
        <div className="d-flex flex-wrap gap-2 justify-content-center">
          <Button
            variant="outline-primary"
            size="sm"
            onClick={() => setCredentials({ username: 'superadmin', password: 'password123' })}
            disabled={loading}
          >
            Super Admin
          </Button>
          <Button
            variant="outline-info"
            size="sm"
            onClick={() => setCredentials({ username: 'corporate1', password: 'password123' })}
            disabled={loading}
          >
            Corporate
          </Button>
          <Button
            variant="outline-warning"
            size="sm"
            onClick={() => setCredentials({ username: 'branch1', password: 'password123' })}
            disabled={loading}
          >
            Branch
          </Button>
        </div>
        <small className="text-muted d-block text-center mt-2">
          Password: <code>password123</code>
        </small>
      </div>
    </Form>
  );
};

export default Login;
