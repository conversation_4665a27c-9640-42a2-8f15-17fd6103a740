import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { testConnection, authenticateUser, createUser, getUsersByCreator } from './database.js';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/api/health', async (req, res) => {
  const dbConnected = await testConnection();
  res.json({ 
    success: true, 
    message: 'API is running',
    database: dbConnected ? 'connected' : 'disconnected'
  });
});

// Authentication endpoints
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ 
        success: false, 
        error: 'Username and password are required' 
      });
    }
    
    const result = await authenticateUser(username, password);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(401).json(result);
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
});

// User management endpoints
app.post('/api/users', async (req, res) => {
  try {
    const userData = req.body;
    const createdBy = userData.created_by;
    
    if (!userData.username || !userData.password || !userData.role) {
      return res.status(400).json({ 
        success: false, 
        error: 'Username, password, and role are required' 
      });
    }
    
    const result = await createUser(userData, createdBy);
    
    if (result.success) {
      res.status(201).json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
});

app.get('/api/users/created-by/:creatorId', async (req, res) => {
  try {
    const { creatorId } = req.params;
    const users = await getUsersByCreator(creatorId);
    
    res.json({ 
      success: true, 
      users 
    });
  } catch (error) {
    console.error('Get users by creator error:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    success: false, 
    error: 'Something went wrong!' 
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    success: false, 
    error: 'Endpoint not found' 
  });
});

// Start server
app.listen(PORT, async () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  
  // Test database connection on startup
  const dbConnected = await testConnection();
  if (dbConnected) {
    console.log('✅ Database connection established');
  } else {
    console.log('❌ Database connection failed');
  }
});
