{"version": 3, "sources": ["browser-external:events", "../../postgres-array/index.js", "../../pg-types/lib/arrayParser.js", "../../postgres-date/index.js", "../../xtend/mutable.js", "../../postgres-interval/index.js", "../../postgres-bytea/index.js", "../../pg-types/lib/textParsers.js", "../../pg-int8/index.js", "../../pg-types/lib/binaryParsers.js", "../../pg-types/lib/builtins.js", "../../pg-types/index.js", "../../pg/lib/defaults.js", "browser-external:util", "../../pg/lib/utils.js", "../../pg/lib/crypto/utils-legacy.js", "../../pg/lib/crypto/utils-webcrypto.js", "../../pg/lib/crypto/utils.js", "../../pg/lib/crypto/cert-signatures.js", "../../pg/lib/crypto/sasl.js", "../../pg/lib/type-overrides.js", "browser-external:dns", "browser-external:fs", "../../pg-connection-string/index.js", "../../pg/lib/connection-parameters.js", "../../pg/lib/result.js", "../../pg/lib/query.js", "../../pg-protocol/src/messages.ts", "../../pg-protocol/src/buffer-writer.ts", "../../pg-protocol/src/serializer.ts", "../../pg-protocol/src/buffer-reader.ts", "../../pg-protocol/src/parser.ts", "../../pg-protocol/src/index.ts", "browser-external:net", "browser-external:tls", "../../pg-cloudflare/src/empty.ts", "../../pg/lib/stream.js", "../../pg/lib/connection.js", "browser-external:path", "browser-external:stream", "browser-external:string_decoder", "../../split2/index.js", "../../pgpass/lib/helper.js", "../../pgpass/lib/index.js", "../../pg/lib/client.js", "../../pg-pool/index.js", "optional-peer-dep:__vite-optional-peer-dep:pg-native:pg", "../../pg/lib/native/query.js", "../../pg/lib/native/client.js", "../../pg/lib/native/index.js", "../../pg/lib/index.js", "../../pg/esm/index.mjs"], "sourcesContent": ["module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"events\" has been externalized for browser compatibility. Cannot access \"events.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "'use strict'\n\nexports.parse = function (source, transform) {\n  return new ArrayParser(source, transform).parse()\n}\n\nclass ArrayParser {\n  constructor (source, transform) {\n    this.source = source\n    this.transform = transform || identity\n    this.position = 0\n    this.entries = []\n    this.recorded = []\n    this.dimension = 0\n  }\n\n  isEof () {\n    return this.position >= this.source.length\n  }\n\n  nextCharacter () {\n    var character = this.source[this.position++]\n    if (character === '\\\\') {\n      return {\n        value: this.source[this.position++],\n        escaped: true\n      }\n    }\n    return {\n      value: character,\n      escaped: false\n    }\n  }\n\n  record (character) {\n    this.recorded.push(character)\n  }\n\n  newEntry (includeEmpty) {\n    var entry\n    if (this.recorded.length > 0 || includeEmpty) {\n      entry = this.recorded.join('')\n      if (entry === 'NULL' && !includeEmpty) {\n        entry = null\n      }\n      if (entry !== null) entry = this.transform(entry)\n      this.entries.push(entry)\n      this.recorded = []\n    }\n  }\n\n  consumeDimensions () {\n    if (this.source[0] === '[') {\n      while (!this.isEof()) {\n        var char = this.nextCharacter()\n        if (char.value === '=') break\n      }\n    }\n  }\n\n  parse (nested) {\n    var character, parser, quote\n    this.consumeDimensions()\n    while (!this.isEof()) {\n      character = this.nextCharacter()\n      if (character.value === '{' && !quote) {\n        this.dimension++\n        if (this.dimension > 1) {\n          parser = new ArrayParser(this.source.substr(this.position - 1), this.transform)\n          this.entries.push(parser.parse(true))\n          this.position += parser.position - 2\n        }\n      } else if (character.value === '}' && !quote) {\n        this.dimension--\n        if (!this.dimension) {\n          this.newEntry()\n          if (nested) return this.entries\n        }\n      } else if (character.value === '\"' && !character.escaped) {\n        if (quote) this.newEntry(true)\n        quote = !quote\n      } else if (character.value === ',' && !quote) {\n        this.newEntry()\n      } else {\n        this.record(character.value)\n      }\n    }\n    if (this.dimension !== 0) {\n      throw new Error('array dimension not balanced')\n    }\n    return this.entries\n  }\n}\n\nfunction identity (value) {\n  return value\n}\n", "var array = require('postgres-array');\n\nmodule.exports = {\n  create: function (source, transform) {\n    return {\n      parse: function() {\n        return array.parse(source, transform);\n      }\n    };\n  }\n};\n", "'use strict'\n\nvar DATE_TIME = /(\\d{1,})-(\\d{2})-(\\d{2}) (\\d{2}):(\\d{2}):(\\d{2})(\\.\\d{1,})?.*?( BC)?$/\nvar DATE = /^(\\d{1,})-(\\d{2})-(\\d{2})( BC)?$/\nvar TIME_ZONE = /([Z+-])(\\d{2})?:?(\\d{2})?:?(\\d{2})?/\nvar INFINITY = /^-?infinity$/\n\nmodule.exports = function parseDate (isoDate) {\n  if (INFINITY.test(isoDate)) {\n    // Capitalize to Infinity before passing to Number\n    return Number(isoDate.replace('i', 'I'))\n  }\n  var matches = DATE_TIME.exec(isoDate)\n\n  if (!matches) {\n    // Force YYYY-MM-DD dates to be parsed as local time\n    return getDate(isoDate) || null\n  }\n\n  var isBC = !!matches[8]\n  var year = parseInt(matches[1], 10)\n  if (isBC) {\n    year = bcYearToNegativeYear(year)\n  }\n\n  var month = parseInt(matches[2], 10) - 1\n  var day = matches[3]\n  var hour = parseInt(matches[4], 10)\n  var minute = parseInt(matches[5], 10)\n  var second = parseInt(matches[6], 10)\n\n  var ms = matches[7]\n  ms = ms ? 1000 * parseFloat(ms) : 0\n\n  var date\n  var offset = timeZoneOffset(isoDate)\n  if (offset != null) {\n    date = new Date(Date.UTC(year, month, day, hour, minute, second, ms))\n\n    // Account for years from 0 to 99 being interpreted as 1900-1999\n    // by Date.UTC / the multi-argument form of the Date constructor\n    if (is0To99(year)) {\n      date.setUTCFullYear(year)\n    }\n\n    if (offset !== 0) {\n      date.setTime(date.getTime() - offset)\n    }\n  } else {\n    date = new Date(year, month, day, hour, minute, second, ms)\n\n    if (is0To99(year)) {\n      date.setFullYear(year)\n    }\n  }\n\n  return date\n}\n\nfunction getDate (isoDate) {\n  var matches = DATE.exec(isoDate)\n  if (!matches) {\n    return\n  }\n\n  var year = parseInt(matches[1], 10)\n  var isBC = !!matches[4]\n  if (isBC) {\n    year = bcYearToNegativeYear(year)\n  }\n\n  var month = parseInt(matches[2], 10) - 1\n  var day = matches[3]\n  // YYYY-MM-DD will be parsed as local time\n  var date = new Date(year, month, day)\n\n  if (is0To99(year)) {\n    date.setFullYear(year)\n  }\n\n  return date\n}\n\n// match timezones:\n// Z (UTC)\n// -05\n// +06:30\nfunction timeZoneOffset (isoDate) {\n  if (isoDate.endsWith('+00')) {\n    return 0\n  }\n\n  var zone = TIME_ZONE.exec(isoDate.split(' ')[1])\n  if (!zone) return\n  var type = zone[1]\n\n  if (type === 'Z') {\n    return 0\n  }\n  var sign = type === '-' ? -1 : 1\n  var offset = parseInt(zone[2], 10) * 3600 +\n    parseInt(zone[3] || 0, 10) * 60 +\n    parseInt(zone[4] || 0, 10)\n\n  return offset * sign * 1000\n}\n\nfunction bcYearToNegativeYear (year) {\n  // Account for numerical difference between representations of BC years\n  // See: https://github.com/bendrucker/postgres-date/issues/5\n  return -(year - 1)\n}\n\nfunction is0To99 (num) {\n  return num >= 0 && num < 100\n}\n", "module.exports = extend\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nfunction extend(target) {\n    for (var i = 1; i < arguments.length; i++) {\n        var source = arguments[i]\n\n        for (var key in source) {\n            if (hasOwnProperty.call(source, key)) {\n                target[key] = source[key]\n            }\n        }\n    }\n\n    return target\n}\n", "'use strict'\n\nvar extend = require('xtend/mutable')\n\nmodule.exports = PostgresInterval\n\nfunction PostgresInterval (raw) {\n  if (!(this instanceof PostgresInterval)) {\n    return new PostgresInterval(raw)\n  }\n  extend(this, parse(raw))\n}\nvar properties = ['seconds', 'minutes', 'hours', 'days', 'months', 'years']\nPostgresInterval.prototype.toPostgres = function () {\n  var filtered = properties.filter(this.hasOwnProperty, this)\n\n  // In addition to `properties`, we need to account for fractions of seconds.\n  if (this.milliseconds && filtered.indexOf('seconds') < 0) {\n    filtered.push('seconds')\n  }\n\n  if (filtered.length === 0) return '0'\n  return filtered\n    .map(function (property) {\n      var value = this[property] || 0\n\n      // Account for fractional part of seconds,\n      // remove trailing zeroes.\n      if (property === 'seconds' && this.milliseconds) {\n        value = (value + this.milliseconds / 1000).toFixed(6).replace(/\\.?0+$/, '')\n      }\n\n      return value + ' ' + property\n    }, this)\n    .join(' ')\n}\n\nvar propertiesISOEquivalent = {\n  years: 'Y',\n  months: 'M',\n  days: 'D',\n  hours: 'H',\n  minutes: 'M',\n  seconds: 'S'\n}\nvar dateProperties = ['years', 'months', 'days']\nvar timeProperties = ['hours', 'minutes', 'seconds']\n// according to ISO 8601\nPostgresInterval.prototype.toISOString = PostgresInterval.prototype.toISO = function () {\n  var datePart = dateProperties\n    .map(buildProperty, this)\n    .join('')\n\n  var timePart = timeProperties\n    .map(buildProperty, this)\n    .join('')\n\n  return 'P' + datePart + 'T' + timePart\n\n  function buildProperty (property) {\n    var value = this[property] || 0\n\n    // Account for fractional part of seconds,\n    // remove trailing zeroes.\n    if (property === 'seconds' && this.milliseconds) {\n      value = (value + this.milliseconds / 1000).toFixed(6).replace(/0+$/, '')\n    }\n\n    return value + propertiesISOEquivalent[property]\n  }\n}\n\nvar NUMBER = '([+-]?\\\\d+)'\nvar YEAR = NUMBER + '\\\\s+years?'\nvar MONTH = NUMBER + '\\\\s+mons?'\nvar DAY = NUMBER + '\\\\s+days?'\nvar TIME = '([+-])?([\\\\d]*):(\\\\d\\\\d):(\\\\d\\\\d)\\\\.?(\\\\d{1,6})?'\nvar INTERVAL = new RegExp([YEAR, MONTH, DAY, TIME].map(function (regexString) {\n  return '(' + regexString + ')?'\n})\n  .join('\\\\s*'))\n\n// Positions of values in regex match\nvar positions = {\n  years: 2,\n  months: 4,\n  days: 6,\n  hours: 9,\n  minutes: 10,\n  seconds: 11,\n  milliseconds: 12\n}\n// We can use negative time\nvar negatives = ['hours', 'minutes', 'seconds', 'milliseconds']\n\nfunction parseMilliseconds (fraction) {\n  // add omitted zeroes\n  var microseconds = fraction + '000000'.slice(fraction.length)\n  return parseInt(microseconds, 10) / 1000\n}\n\nfunction parse (interval) {\n  if (!interval) return {}\n  var matches = INTERVAL.exec(interval)\n  var isNegative = matches[8] === '-'\n  return Object.keys(positions)\n    .reduce(function (parsed, property) {\n      var position = positions[property]\n      var value = matches[position]\n      // no empty string\n      if (!value) return parsed\n      // milliseconds are actually microseconds (up to 6 digits)\n      // with omitted trailing zeroes.\n      value = property === 'milliseconds'\n        ? parseMilliseconds(value)\n        : parseInt(value, 10)\n      // no zeros\n      if (!value) return parsed\n      if (isNegative && ~negatives.indexOf(property)) {\n        value *= -1\n      }\n      parsed[property] = value\n      return parsed\n    }, {})\n}\n", "'use strict'\n\nmodule.exports = function parseBytea (input) {\n  if (/^\\\\x/.test(input)) {\n    // new 'hex' style response (pg >9.0)\n    return new Buffer(input.substr(2), 'hex')\n  }\n  var output = ''\n  var i = 0\n  while (i < input.length) {\n    if (input[i] !== '\\\\') {\n      output += input[i]\n      ++i\n    } else {\n      if (/[0-7]{3}/.test(input.substr(i + 1, 3))) {\n        output += String.fromCharCode(parseInt(input.substr(i + 1, 3), 8))\n        i += 4\n      } else {\n        var backslashes = 1\n        while (i + backslashes < input.length && input[i + backslashes] === '\\\\') {\n          backslashes++\n        }\n        for (var k = 0; k < Math.floor(backslashes / 2); ++k) {\n          output += '\\\\'\n        }\n        i += Math.floor(backslashes / 2) * 2\n      }\n    }\n  }\n  return new Buffer(output, 'binary')\n}\n", "var array = require('postgres-array')\nvar arrayParser = require('./arrayParser');\nvar parseDate = require('postgres-date');\nvar parseInterval = require('postgres-interval');\nvar parseByteA = require('postgres-bytea');\n\nfunction allowNull (fn) {\n  return function nullAllowed (value) {\n    if (value === null) return value\n    return fn(value)\n  }\n}\n\nfunction parseBool (value) {\n  if (value === null) return value\n  return value === 'TRUE' ||\n    value === 't' ||\n    value === 'true' ||\n    value === 'y' ||\n    value === 'yes' ||\n    value === 'on' ||\n    value === '1';\n}\n\nfunction parseBoolArray (value) {\n  if (!value) return null\n  return array.parse(value, parseBool)\n}\n\nfunction parseBaseTenInt (string) {\n  return parseInt(string, 10)\n}\n\nfunction parseIntegerArray (value) {\n  if (!value) return null\n  return array.parse(value, allowNull(parseBaseTenInt))\n}\n\nfunction parseBigIntegerArray (value) {\n  if (!value) return null\n  return array.parse(value, allowNull(function (entry) {\n    return parseBigInteger(entry).trim()\n  }))\n}\n\nvar parsePointArray = function(value) {\n  if(!value) { return null; }\n  var p = arrayParser.create(value, function(entry) {\n    if(entry !== null) {\n      entry = parsePoint(entry);\n    }\n    return entry;\n  });\n\n  return p.parse();\n};\n\nvar parseFloatArray = function(value) {\n  if(!value) { return null; }\n  var p = arrayParser.create(value, function(entry) {\n    if(entry !== null) {\n      entry = parseFloat(entry);\n    }\n    return entry;\n  });\n\n  return p.parse();\n};\n\nvar parseStringArray = function(value) {\n  if(!value) { return null; }\n\n  var p = arrayParser.create(value);\n  return p.parse();\n};\n\nvar parseDateArray = function(value) {\n  if (!value) { return null; }\n\n  var p = arrayParser.create(value, function(entry) {\n    if (entry !== null) {\n      entry = parseDate(entry);\n    }\n    return entry;\n  });\n\n  return p.parse();\n};\n\nvar parseIntervalArray = function(value) {\n  if (!value) { return null; }\n\n  var p = arrayParser.create(value, function(entry) {\n    if (entry !== null) {\n      entry = parseInterval(entry);\n    }\n    return entry;\n  });\n\n  return p.parse();\n};\n\nvar parseByteAArray = function(value) {\n  if (!value) { return null; }\n\n  return array.parse(value, allowNull(parseByteA));\n};\n\nvar parseInteger = function(value) {\n  return parseInt(value, 10);\n};\n\nvar parseBigInteger = function(value) {\n  var valStr = String(value);\n  if (/^\\d+$/.test(valStr)) { return valStr; }\n  return value;\n};\n\nvar parseJsonArray = function(value) {\n  if (!value) { return null; }\n\n  return array.parse(value, allowNull(JSON.parse));\n};\n\nvar parsePoint = function(value) {\n  if (value[0] !== '(') { return null; }\n\n  value = value.substring( 1, value.length - 1 ).split(',');\n\n  return {\n    x: parseFloat(value[0])\n  , y: parseFloat(value[1])\n  };\n};\n\nvar parseCircle = function(value) {\n  if (value[0] !== '<' && value[1] !== '(') { return null; }\n\n  var point = '(';\n  var radius = '';\n  var pointParsed = false;\n  for (var i = 2; i < value.length - 1; i++){\n    if (!pointParsed) {\n      point += value[i];\n    }\n\n    if (value[i] === ')') {\n      pointParsed = true;\n      continue;\n    } else if (!pointParsed) {\n      continue;\n    }\n\n    if (value[i] === ','){\n      continue;\n    }\n\n    radius += value[i];\n  }\n  var result = parsePoint(point);\n  result.radius = parseFloat(radius);\n\n  return result;\n};\n\nvar init = function(register) {\n  register(20, parseBigInteger); // int8\n  register(21, parseInteger); // int2\n  register(23, parseInteger); // int4\n  register(26, parseInteger); // oid\n  register(700, parseFloat); // float4/real\n  register(701, parseFloat); // float8/double\n  register(16, parseBool);\n  register(1082, parseDate); // date\n  register(1114, parseDate); // timestamp without timezone\n  register(1184, parseDate); // timestamp\n  register(600, parsePoint); // point\n  register(651, parseStringArray); // cidr[]\n  register(718, parseCircle); // circle\n  register(1000, parseBoolArray);\n  register(1001, parseByteAArray);\n  register(1005, parseIntegerArray); // _int2\n  register(1007, parseIntegerArray); // _int4\n  register(1028, parseIntegerArray); // oid[]\n  register(1016, parseBigIntegerArray); // _int8\n  register(1017, parsePointArray); // point[]\n  register(1021, parseFloatArray); // _float4\n  register(1022, parseFloatArray); // _float8\n  register(1231, parseFloatArray); // _numeric\n  register(1014, parseStringArray); //char\n  register(1015, parseStringArray); //varchar\n  register(1008, parseStringArray);\n  register(1009, parseStringArray);\n  register(1040, parseStringArray); // macaddr[]\n  register(1041, parseStringArray); // inet[]\n  register(1115, parseDateArray); // timestamp without time zone[]\n  register(1182, parseDateArray); // _date\n  register(1185, parseDateArray); // timestamp with time zone[]\n  register(1186, parseInterval);\n  register(1187, parseIntervalArray);\n  register(17, parseByteA);\n  register(114, JSON.parse.bind(JSON)); // json\n  register(3802, JSON.parse.bind(JSON)); // jsonb\n  register(199, parseJsonArray); // json[]\n  register(3807, parseJsonArray); // jsonb[]\n  register(3907, parseStringArray); // numrange[]\n  register(2951, parseStringArray); // uuid[]\n  register(791, parseStringArray); // money[]\n  register(1183, parseStringArray); // time[]\n  register(1270, parseStringArray); // timetz[]\n};\n\nmodule.exports = {\n  init: init\n};\n", "'use strict';\n\n// selected so (BASE - 1) * 0x100000000 + 0xffffffff is a safe integer\nvar BASE = 1000000;\n\nfunction readInt8(buffer) {\n\tvar high = buffer.readInt32BE(0);\n\tvar low = buffer.readUInt32BE(4);\n\tvar sign = '';\n\n\tif (high < 0) {\n\t\thigh = ~high + (low === 0);\n\t\tlow = (~low + 1) >>> 0;\n\t\tsign = '-';\n\t}\n\n\tvar result = '';\n\tvar carry;\n\tvar t;\n\tvar digits;\n\tvar pad;\n\tvar l;\n\tvar i;\n\n\t{\n\t\tcarry = high % BASE;\n\t\thigh = high / BASE >>> 0;\n\n\t\tt = 0x100000000 * carry + low;\n\t\tlow = t / BASE >>> 0;\n\t\tdigits = '' + (t - BASE * low);\n\n\t\tif (low === 0 && high === 0) {\n\t\t\treturn sign + digits + result;\n\t\t}\n\n\t\tpad = '';\n\t\tl = 6 - digits.length;\n\n\t\tfor (i = 0; i < l; i++) {\n\t\t\tpad += '0';\n\t\t}\n\n\t\tresult = pad + digits + result;\n\t}\n\n\t{\n\t\tcarry = high % BASE;\n\t\thigh = high / BASE >>> 0;\n\n\t\tt = 0x100000000 * carry + low;\n\t\tlow = t / BASE >>> 0;\n\t\tdigits = '' + (t - BASE * low);\n\n\t\tif (low === 0 && high === 0) {\n\t\t\treturn sign + digits + result;\n\t\t}\n\n\t\tpad = '';\n\t\tl = 6 - digits.length;\n\n\t\tfor (i = 0; i < l; i++) {\n\t\t\tpad += '0';\n\t\t}\n\n\t\tresult = pad + digits + result;\n\t}\n\n\t{\n\t\tcarry = high % BASE;\n\t\thigh = high / BASE >>> 0;\n\n\t\tt = 0x100000000 * carry + low;\n\t\tlow = t / BASE >>> 0;\n\t\tdigits = '' + (t - BASE * low);\n\n\t\tif (low === 0 && high === 0) {\n\t\t\treturn sign + digits + result;\n\t\t}\n\n\t\tpad = '';\n\t\tl = 6 - digits.length;\n\n\t\tfor (i = 0; i < l; i++) {\n\t\t\tpad += '0';\n\t\t}\n\n\t\tresult = pad + digits + result;\n\t}\n\n\t{\n\t\tcarry = high % BASE;\n\t\tt = 0x100000000 * carry + low;\n\t\tdigits = '' + t % BASE;\n\n\t\treturn sign + digits + result;\n\t}\n}\n\nmodule.exports = readInt8;\n", "var parseInt64 = require('pg-int8');\n\nvar parseBits = function(data, bits, offset, invert, callback) {\n  offset = offset || 0;\n  invert = invert || false;\n  callback = callback || function(lastValue, newValue, bits) { return (lastValue * Math.pow(2, bits)) + newValue; };\n  var offsetBytes = offset >> 3;\n\n  var inv = function(value) {\n    if (invert) {\n      return ~value & 0xff;\n    }\n\n    return value;\n  };\n\n  // read first (maybe partial) byte\n  var mask = 0xff;\n  var firstBits = 8 - (offset % 8);\n  if (bits < firstBits) {\n    mask = (0xff << (8 - bits)) & 0xff;\n    firstBits = bits;\n  }\n\n  if (offset) {\n    mask = mask >> (offset % 8);\n  }\n\n  var result = 0;\n  if ((offset % 8) + bits >= 8) {\n    result = callback(0, inv(data[offsetBytes]) & mask, firstBits);\n  }\n\n  // read bytes\n  var bytes = (bits + offset) >> 3;\n  for (var i = offsetBytes + 1; i < bytes; i++) {\n    result = callback(result, inv(data[i]), 8);\n  }\n\n  // bits to read, that are not a complete byte\n  var lastBits = (bits + offset) % 8;\n  if (lastBits > 0) {\n    result = callback(result, inv(data[bytes]) >> (8 - lastBits), lastBits);\n  }\n\n  return result;\n};\n\nvar parseFloatFromBits = function(data, precisionBits, exponentBits) {\n  var bias = Math.pow(2, exponentBits - 1) - 1;\n  var sign = parseBits(data, 1);\n  var exponent = parseBits(data, exponentBits, 1);\n\n  if (exponent === 0) {\n    return 0;\n  }\n\n  // parse mantissa\n  var precisionBitsCounter = 1;\n  var parsePrecisionBits = function(lastValue, newValue, bits) {\n    if (lastValue === 0) {\n      lastValue = 1;\n    }\n\n    for (var i = 1; i <= bits; i++) {\n      precisionBitsCounter /= 2;\n      if ((newValue & (0x1 << (bits - i))) > 0) {\n        lastValue += precisionBitsCounter;\n      }\n    }\n\n    return lastValue;\n  };\n\n  var mantissa = parseBits(data, precisionBits, exponentBits + 1, false, parsePrecisionBits);\n\n  // special cases\n  if (exponent == (Math.pow(2, exponentBits + 1) - 1)) {\n    if (mantissa === 0) {\n      return (sign === 0) ? Infinity : -Infinity;\n    }\n\n    return NaN;\n  }\n\n  // normale number\n  return ((sign === 0) ? 1 : -1) * Math.pow(2, exponent - bias) * mantissa;\n};\n\nvar parseInt16 = function(value) {\n  if (parseBits(value, 1) == 1) {\n    return -1 * (parseBits(value, 15, 1, true) + 1);\n  }\n\n  return parseBits(value, 15, 1);\n};\n\nvar parseInt32 = function(value) {\n  if (parseBits(value, 1) == 1) {\n    return -1 * (parseBits(value, 31, 1, true) + 1);\n  }\n\n  return parseBits(value, 31, 1);\n};\n\nvar parseFloat32 = function(value) {\n  return parseFloatFromBits(value, 23, 8);\n};\n\nvar parseFloat64 = function(value) {\n  return parseFloatFromBits(value, 52, 11);\n};\n\nvar parseNumeric = function(value) {\n  var sign = parseBits(value, 16, 32);\n  if (sign == 0xc000) {\n    return NaN;\n  }\n\n  var weight = Math.pow(10000, parseBits(value, 16, 16));\n  var result = 0;\n\n  var digits = [];\n  var ndigits = parseBits(value, 16);\n  for (var i = 0; i < ndigits; i++) {\n    result += parseBits(value, 16, 64 + (16 * i)) * weight;\n    weight /= 10000;\n  }\n\n  var scale = Math.pow(10, parseBits(value, 16, 48));\n  return ((sign === 0) ? 1 : -1) * Math.round(result * scale) / scale;\n};\n\nvar parseDate = function(isUTC, value) {\n  var sign = parseBits(value, 1);\n  var rawValue = parseBits(value, 63, 1);\n\n  // discard usecs and shift from 2000 to 1970\n  var result = new Date((((sign === 0) ? 1 : -1) * rawValue / 1000) + 946684800000);\n\n  if (!isUTC) {\n    result.setTime(result.getTime() + result.getTimezoneOffset() * 60000);\n  }\n\n  // add microseconds to the date\n  result.usec = rawValue % 1000;\n  result.getMicroSeconds = function() {\n    return this.usec;\n  };\n  result.setMicroSeconds = function(value) {\n    this.usec = value;\n  };\n  result.getUTCMicroSeconds = function() {\n    return this.usec;\n  };\n\n  return result;\n};\n\nvar parseArray = function(value) {\n  var dim = parseBits(value, 32);\n\n  var flags = parseBits(value, 32, 32);\n  var elementType = parseBits(value, 32, 64);\n\n  var offset = 96;\n  var dims = [];\n  for (var i = 0; i < dim; i++) {\n    // parse dimension\n    dims[i] = parseBits(value, 32, offset);\n    offset += 32;\n\n    // ignore lower bounds\n    offset += 32;\n  }\n\n  var parseElement = function(elementType) {\n    // parse content length\n    var length = parseBits(value, 32, offset);\n    offset += 32;\n\n    // parse null values\n    if (length == 0xffffffff) {\n      return null;\n    }\n\n    var result;\n    if ((elementType == 0x17) || (elementType == 0x14)) {\n      // int/bigint\n      result = parseBits(value, length * 8, offset);\n      offset += length * 8;\n      return result;\n    }\n    else if (elementType == 0x19) {\n      // string\n      result = value.toString(this.encoding, offset >> 3, (offset += (length << 3)) >> 3);\n      return result;\n    }\n    else {\n      console.log(\"ERROR: ElementType not implemented: \" + elementType);\n    }\n  };\n\n  var parse = function(dimension, elementType) {\n    var array = [];\n    var i;\n\n    if (dimension.length > 1) {\n      var count = dimension.shift();\n      for (i = 0; i < count; i++) {\n        array[i] = parse(dimension, elementType);\n      }\n      dimension.unshift(count);\n    }\n    else {\n      for (i = 0; i < dimension[0]; i++) {\n        array[i] = parseElement(elementType);\n      }\n    }\n\n    return array;\n  };\n\n  return parse(dims, elementType);\n};\n\nvar parseText = function(value) {\n  return value.toString('utf8');\n};\n\nvar parseBool = function(value) {\n  if(value === null) return null;\n  return (parseBits(value, 8) > 0);\n};\n\nvar init = function(register) {\n  register(20, parseInt64);\n  register(21, parseInt16);\n  register(23, parseInt32);\n  register(26, parseInt32);\n  register(1700, parseNumeric);\n  register(700, parseFloat32);\n  register(701, parseFloat64);\n  register(16, parseBool);\n  register(1114, parseDate.bind(null, false));\n  register(1184, parseDate.bind(null, true));\n  register(1000, parseArray);\n  register(1007, parseArray);\n  register(1016, parseArray);\n  register(1008, parseArray);\n  register(1009, parseArray);\n  register(25, parseText);\n};\n\nmodule.exports = {\n  init: init\n};\n", "/**\n * Following query was used to generate this file:\n\n SELECT json_object_agg(UPPER(PT.typname), PT.oid::int4 ORDER BY pt.oid)\n FROM pg_type PT\n WHERE typnamespace = (SELECT pgn.oid FROM pg_namespace pgn WHERE nspname = 'pg_catalog') -- Take only builting Postgres types with stable OID (extension types are not guaranted to be stable)\n AND typtype = 'b' -- Only basic types\n AND typelem = 0 -- Ignore aliases\n AND typisdefined -- Ignore undefined types\n */\n\nmodule.exports = {\n    BOOL: 16,\n    BYTEA: 17,\n    CHAR: 18,\n    INT8: 20,\n    INT2: 21,\n    INT4: 23,\n    REGPROC: 24,\n    TEXT: 25,\n    OID: 26,\n    TID: 27,\n    XID: 28,\n    CID: 29,\n    JSON: 114,\n    XML: 142,\n    PG_NODE_TREE: 194,\n    SMGR: 210,\n    PATH: 602,\n    POLYGON: 604,\n    CIDR: 650,\n    FLOAT4: 700,\n    FLOAT8: 701,\n    ABSTIME: 702,\n    RELTIME: 703,\n    TINTERVAL: 704,\n    CIRCLE: 718,\n    MACADDR8: 774,\n    MONEY: 790,\n    MACADDR: 829,\n    INET: 869,\n    ACLITEM: 1033,\n    BPCHAR: 1042,\n    VARCHAR: 1043,\n    DATE: 1082,\n    TIME: 1083,\n    TIMESTAMP: 1114,\n    TIMESTAMPTZ: 1184,\n    INTERVAL: 1186,\n    TIMETZ: 1266,\n    BIT: 1560,\n    VARBIT: 1562,\n    NUMERIC: 1700,\n    REFCURSOR: 1790,\n    REGPROCEDURE: 2202,\n    REGOPER: 2203,\n    REGOPERATOR: 2204,\n    REGCLASS: 2205,\n    REGTYPE: 2206,\n    UUID: 2950,\n    TXID_SNAPSHOT: 2970,\n    PG_LSN: 3220,\n    PG_NDISTINCT: 3361,\n    PG_DEPENDENCIES: 3402,\n    TSVECTOR: 3614,\n    TSQUERY: 3615,\n    GTSVECTOR: 3642,\n    REGCONFIG: 3734,\n    REGDICTIONARY: 3769,\n    JSONB: 3802,\n    REGNAMESPACE: 4089,\n    REGROLE: 4096\n};\n", "var textParsers = require('./lib/textParsers');\nvar binaryParsers = require('./lib/binaryParsers');\nvar arrayParser = require('./lib/arrayParser');\nvar builtinTypes = require('./lib/builtins');\n\nexports.getTypeParser = getTypeParser;\nexports.setTypeParser = setTypeParser;\nexports.arrayParser = arrayParser;\nexports.builtins = builtinTypes;\n\nvar typeParsers = {\n  text: {},\n  binary: {}\n};\n\n//the empty parse function\nfunction noParse (val) {\n  return String(val);\n};\n\n//returns a function used to convert a specific type (specified by\n//oid) into a result javascript type\n//note: the oid can be obtained via the following sql query:\n//SELECT oid FROM pg_type WHERE typname = 'TYPE_NAME_HERE';\nfunction getTypeParser (oid, format) {\n  format = format || 'text';\n  if (!typeParsers[format]) {\n    return noParse;\n  }\n  return typeParsers[format][oid] || noParse;\n};\n\nfunction setTypeParser (oid, format, parseFn) {\n  if(typeof format == 'function') {\n    parseFn = format;\n    format = 'text';\n  }\n  typeParsers[format][oid] = parseFn;\n};\n\ntextParsers.init(function(oid, converter) {\n  typeParsers.text[oid] = converter;\n});\n\nbinaryParsers.init(function(oid, converter) {\n  typeParsers.binary[oid] = converter;\n});\n", "'use strict'\n\nmodule.exports = {\n  // database host. defaults to localhost\n  host: 'localhost',\n\n  // database user's name\n  user: process.platform === 'win32' ? process.env.USERNAME : process.env.USER,\n\n  // name of database to connect\n  database: undefined,\n\n  // database user's password\n  password: null,\n\n  // a Postgres connection string to be used instead of setting individual connection items\n  // NOTE:  Setting this value will cause it to override any other value (such as database or user) defined\n  // in the defaults object.\n  connectionString: undefined,\n\n  // database port\n  port: 5432,\n\n  // number of rows to return at a time from a prepared statement's\n  // portal. 0 will return all rows at once\n  rows: 0,\n\n  // binary result mode\n  binary: false,\n\n  // Connection pool options - see https://github.com/brianc/node-pg-pool\n\n  // number of connections to use in connection pool\n  // 0 will disable connection pooling\n  max: 10,\n\n  // max milliseconds a client can go unused before it is removed\n  // from the pool and destroyed\n  idleTimeoutMillis: 30000,\n\n  client_encoding: '',\n\n  ssl: false,\n\n  application_name: undefined,\n\n  fallback_application_name: undefined,\n\n  options: undefined,\n\n  parseInputDatesAsUTC: false,\n\n  // max milliseconds any query using this connection will execute for before timing out in error.\n  // false=unlimited\n  statement_timeout: false,\n\n  // Abort any statement that waits longer than the specified duration in milliseconds while attempting to acquire a lock.\n  // false=unlimited\n  lock_timeout: false,\n\n  // Terminate any session with an open transaction that has been idle for longer than the specified duration in milliseconds\n  // false=unlimited\n  idle_in_transaction_session_timeout: false,\n\n  // max milliseconds to wait for query to complete (client side)\n  query_timeout: false,\n\n  connect_timeout: 0,\n\n  keepalives: 1,\n\n  keepalives_idle: 0,\n}\n\nconst pgTypes = require('pg-types')\n// save default parsers\nconst parseBigInteger = pgTypes.getTypeParser(20, 'text')\nconst parseBigIntegerArray = pgTypes.getTypeParser(1016, 'text')\n\n// parse int8 so you can get your count values as actual numbers\nmodule.exports.__defineSetter__('parseInt8', function (val) {\n  pgTypes.setTypeParser(20, 'text', val ? pgTypes.getTypeParser(23, 'text') : parseBigInteger)\n  pgTypes.setTypeParser(1016, 'text', val ? pgTypes.getTypeParser(1007, 'text') : parseBigIntegerArray)\n})\n", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"util\" has been externalized for browser compatibility. Cannot access \"util.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "'use strict'\n\nconst defaults = require('./defaults')\n\nconst util = require('util')\nconst { isDate } = util.types || util // Node 8 doesn't have `util.types`\n\nfunction escapeElement(elementRepresentation) {\n  const escaped = elementRepresentation.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"')\n\n  return '\"' + escaped + '\"'\n}\n\n// convert a JS array to a postgres array literal\n// uses comma separator so won't work for types like box that use\n// a different array separator.\nfunction arrayString(val) {\n  let result = '{'\n  for (let i = 0; i < val.length; i++) {\n    if (i > 0) {\n      result = result + ','\n    }\n    if (val[i] === null || typeof val[i] === 'undefined') {\n      result = result + 'NULL'\n    } else if (Array.isArray(val[i])) {\n      result = result + arrayString(val[i])\n    } else if (ArrayBuffer.isView(val[i])) {\n      let item = val[i]\n      if (!(item instanceof Buffer)) {\n        const buf = Buffer.from(item.buffer, item.byteOffset, item.byteLength)\n        if (buf.length === item.byteLength) {\n          item = buf\n        } else {\n          item = buf.slice(item.byteOffset, item.byteOffset + item.byteLength)\n        }\n      }\n      result += '\\\\\\\\x' + item.toString('hex')\n    } else {\n      result += escapeElement(prepareValue(val[i]))\n    }\n  }\n  result = result + '}'\n  return result\n}\n\n// converts values from javascript types\n// to their 'raw' counterparts for use as a postgres parameter\n// note: you can override this function to provide your own conversion mechanism\n// for complex types, etc...\nconst prepareValue = function (val, seen) {\n  // null and undefined are both null for postgres\n  if (val == null) {\n    return null\n  }\n  if (typeof val === 'object') {\n    if (val instanceof Buffer) {\n      return val\n    }\n    if (ArrayBuffer.isView(val)) {\n      const buf = Buffer.from(val.buffer, val.byteOffset, val.byteLength)\n      if (buf.length === val.byteLength) {\n        return buf\n      }\n      return buf.slice(val.byteOffset, val.byteOffset + val.byteLength) // Node.js v4 does not support those Buffer.from params\n    }\n    if (isDate(val)) {\n      if (defaults.parseInputDatesAsUTC) {\n        return dateToStringUTC(val)\n      } else {\n        return dateToString(val)\n      }\n    }\n    if (Array.isArray(val)) {\n      return arrayString(val)\n    }\n\n    return prepareObject(val, seen)\n  }\n  return val.toString()\n}\n\nfunction prepareObject(val, seen) {\n  if (val && typeof val.toPostgres === 'function') {\n    seen = seen || []\n    if (seen.indexOf(val) !== -1) {\n      throw new Error('circular reference detected while preparing \"' + val + '\" for query')\n    }\n    seen.push(val)\n\n    return prepareValue(val.toPostgres(prepareValue), seen)\n  }\n  return JSON.stringify(val)\n}\n\nfunction dateToString(date) {\n  let offset = -date.getTimezoneOffset()\n\n  let year = date.getFullYear()\n  const isBCYear = year < 1\n  if (isBCYear) year = Math.abs(year) + 1 // negative years are 1 off their BC representation\n\n  let ret =\n    String(year).padStart(4, '0') +\n    '-' +\n    String(date.getMonth() + 1).padStart(2, '0') +\n    '-' +\n    String(date.getDate()).padStart(2, '0') +\n    'T' +\n    String(date.getHours()).padStart(2, '0') +\n    ':' +\n    String(date.getMinutes()).padStart(2, '0') +\n    ':' +\n    String(date.getSeconds()).padStart(2, '0') +\n    '.' +\n    String(date.getMilliseconds()).padStart(3, '0')\n\n  if (offset < 0) {\n    ret += '-'\n    offset *= -1\n  } else {\n    ret += '+'\n  }\n\n  ret += String(Math.floor(offset / 60)).padStart(2, '0') + ':' + String(offset % 60).padStart(2, '0')\n  if (isBCYear) ret += ' BC'\n  return ret\n}\n\nfunction dateToStringUTC(date) {\n  let year = date.getUTCFullYear()\n  const isBCYear = year < 1\n  if (isBCYear) year = Math.abs(year) + 1 // negative years are 1 off their BC representation\n\n  let ret =\n    String(year).padStart(4, '0') +\n    '-' +\n    String(date.getUTCMonth() + 1).padStart(2, '0') +\n    '-' +\n    String(date.getUTCDate()).padStart(2, '0') +\n    'T' +\n    String(date.getUTCHours()).padStart(2, '0') +\n    ':' +\n    String(date.getUTCMinutes()).padStart(2, '0') +\n    ':' +\n    String(date.getUTCSeconds()).padStart(2, '0') +\n    '.' +\n    String(date.getUTCMilliseconds()).padStart(3, '0')\n\n  ret += '+00:00'\n  if (isBCYear) ret += ' BC'\n  return ret\n}\n\nfunction normalizeQueryConfig(config, values, callback) {\n  // can take in strings or config objects\n  config = typeof config === 'string' ? { text: config } : config\n  if (values) {\n    if (typeof values === 'function') {\n      config.callback = values\n    } else {\n      config.values = values\n    }\n  }\n  if (callback) {\n    config.callback = callback\n  }\n  return config\n}\n\n// Ported from PostgreSQL 9.2.4 source code in src/interfaces/libpq/fe-exec.c\nconst escapeIdentifier = function (str) {\n  return '\"' + str.replace(/\"/g, '\"\"') + '\"'\n}\n\nconst escapeLiteral = function (str) {\n  let hasBackslash = false\n  let escaped = \"'\"\n\n  if (str == null) {\n    return \"''\"\n  }\n\n  if (typeof str !== 'string') {\n    return \"''\"\n  }\n\n  for (let i = 0; i < str.length; i++) {\n    const c = str[i]\n    if (c === \"'\") {\n      escaped += c + c\n    } else if (c === '\\\\') {\n      escaped += c + c\n      hasBackslash = true\n    } else {\n      escaped += c\n    }\n  }\n\n  escaped += \"'\"\n\n  if (hasBackslash === true) {\n    escaped = ' E' + escaped\n  }\n\n  return escaped\n}\n\nmodule.exports = {\n  prepareValue: function prepareValueWrapper(value) {\n    // this ensures that extra arguments do not get passed into prepareValue\n    // by accident, eg: from calling values.map(utils.prepareValue)\n    return prepareValue(value)\n  },\n  normalizeQueryConfig,\n  escapeIdentifier,\n  escapeLiteral,\n}\n", "'use strict'\n// This file contains crypto utility functions for versions of Node.js < 15.0.0,\n// which does not support the WebCrypto.subtle API.\n\nconst nodeCrypto = require('crypto')\n\nfunction md5(string) {\n  return nodeCrypto.createHash('md5').update(string, 'utf-8').digest('hex')\n}\n\n// See AuthenticationMD5Password at https://www.postgresql.org/docs/current/static/protocol-flow.html\nfunction postgresMd5PasswordHash(user, password, salt) {\n  const inner = md5(password + user)\n  const outer = md5(Buffer.concat([Buffer.from(inner), salt]))\n  return 'md5' + outer\n}\n\nfunction sha256(text) {\n  return nodeCrypto.createHash('sha256').update(text).digest()\n}\n\nfunction hashByName(hashName, text) {\n  hashName = hashName.replace(/(\\D)-/, '$1') // e.g. SHA-256 -> SHA256\n  return nodeCrypto.createHash(hashName).update(text).digest()\n}\n\nfunction hmacSha256(key, msg) {\n  return nodeCrypto.createHmac('sha256', key).update(msg).digest()\n}\n\nasync function deriveKey(password, salt, iterations) {\n  return nodeCrypto.pbkdf2Sync(password, salt, iterations, 32, 'sha256')\n}\n\nmodule.exports = {\n  postgresMd5PasswordHash,\n  randomBytes: nodeCrypto.randomBytes,\n  deriveKey,\n  sha256,\n  hashByName,\n  hmacSha256,\n  md5,\n}\n", "const nodeCrypto = require('crypto')\n\nmodule.exports = {\n  postgresMd5PasswordHash,\n  randomBytes,\n  deriveKey,\n  sha256,\n  hashByName,\n  hmacSha256,\n  md5,\n}\n\n/**\n * The Web Crypto API - grabbed from the Node.js library or the global\n * @type Crypto\n */\n// eslint-disable-next-line no-undef\nconst webCrypto = nodeCrypto.webcrypto || globalThis.crypto\n/**\n * The SubtleCrypto API for low level crypto operations.\n * @type SubtleCrypto\n */\nconst subtleCrypto = webCrypto.subtle\nconst textEncoder = new TextEncoder()\n\n/**\n *\n * @param {*} length\n * @returns\n */\nfunction randomBytes(length) {\n  return webCrypto.getRandomValues(Buffer.alloc(length))\n}\n\nasync function md5(string) {\n  try {\n    return nodeCrypto.createHash('md5').update(string, 'utf-8').digest('hex')\n  } catch (e) {\n    // `createHash()` failed so we are probably not in Node.js, use the WebCrypto API instead.\n    // Note that the MD5 algorithm on WebCrypto is not available in Node.js.\n    // This is why we cannot just use WebCrypto in all environments.\n    const data = typeof string === 'string' ? textEncoder.encode(string) : string\n    const hash = await subtleCrypto.digest('MD5', data)\n    return Array.from(new Uint8Array(hash))\n      .map((b) => b.toString(16).padStart(2, '0'))\n      .join('')\n  }\n}\n\n// See AuthenticationMD5Password at https://www.postgresql.org/docs/current/static/protocol-flow.html\nasync function postgresMd5PasswordHash(user, password, salt) {\n  const inner = await md5(password + user)\n  const outer = await md5(Buffer.concat([Buffer.from(inner), salt]))\n  return 'md5' + outer\n}\n\n/**\n * Create a SHA-256 digest of the given data\n * @param {Buffer} data\n */\nasync function sha256(text) {\n  return await subtleCrypto.digest('SHA-256', text)\n}\n\nasync function hashByName(hashName, text) {\n  return await subtleCrypto.digest(hashName, text)\n}\n\n/**\n * Sign the message with the given key\n * @param {ArrayBuffer} keyBuffer\n * @param {string} msg\n */\nasync function hmacSha256(keyBuffer, msg) {\n  const key = await subtleCrypto.importKey('raw', keyBuffer, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign'])\n  return await subtleCrypto.sign('HMAC', key, textEncoder.encode(msg))\n}\n\n/**\n * Derive a key from the password and salt\n * @param {string} password\n * @param {Uint8Array} salt\n * @param {number} iterations\n */\nasync function deriveKey(password, salt, iterations) {\n  const key = await subtleCrypto.importKey('raw', textEncoder.encode(password), 'PBKDF2', false, ['deriveBits'])\n  const params = { name: 'PBKDF2', hash: 'SHA-256', salt: salt, iterations: iterations }\n  return await subtleCrypto.deriveBits(params, key, 32 * 8, ['deriveBits'])\n}\n", "'use strict'\n\nconst useLegacyCrypto = parseInt(process.versions && process.versions.node && process.versions.node.split('.')[0]) < 15\nif (useLegacyCrypto) {\n  // We are on an old version of Node.js that requires legacy crypto utilities.\n  module.exports = require('./utils-legacy')\n} else {\n  module.exports = require('./utils-webcrypto')\n}\n", "function x509Error(msg, cert) {\n  return new Error('SASL channel binding: ' + msg + ' when parsing public certificate ' + cert.toString('base64'))\n}\n\nfunction readASN1Length(data, index) {\n  let length = data[index++]\n  if (length < 0x80) return { length, index }\n\n  const lengthBytes = length & 0x7f\n  if (lengthBytes > 4) throw x509Error('bad length', data)\n\n  length = 0\n  for (let i = 0; i < lengthBytes; i++) {\n    length = (length << 8) | data[index++]\n  }\n\n  return { length, index }\n}\n\nfunction readASN1OID(data, index) {\n  if (data[index++] !== 0x6) throw x509Error('non-OID data', data) // 6 = OID\n\n  const { length: OIDLength, index: indexAfterOIDLength } = readASN1Length(data, index)\n  index = indexAfterOIDLength\n  const lastIndex = index + OIDLength\n\n  const byte1 = data[index++]\n  let oid = ((byte1 / 40) >> 0) + '.' + (byte1 % 40)\n\n  while (index < lastIndex) {\n    // loop over numbers in OID\n    let value = 0\n    while (index < lastIndex) {\n      // loop over bytes in number\n      const nextByte = data[index++]\n      value = (value << 7) | (nextByte & 0x7f)\n      if (nextByte < 0x80) break\n    }\n    oid += '.' + value\n  }\n\n  return { oid, index }\n}\n\nfunction expectASN1Seq(data, index) {\n  if (data[index++] !== 0x30) throw x509Error('non-sequence data', data) // 30 = Sequence\n  return readASN1Length(data, index)\n}\n\nfunction signatureAlgorithmHashFromCertificate(data, index) {\n  // read this thread: https://www.postgresql.org/message-id/17760-b6c61e752ec07060%40postgresql.org\n  if (index === undefined) index = 0\n  index = expectASN1Seq(data, index).index\n  const { length: certInfoLength, index: indexAfterCertInfoLength } = expectASN1Seq(data, index)\n  index = indexAfterCertInfoLength + certInfoLength // skip over certificate info\n  index = expectASN1Seq(data, index).index // skip over signature length field\n  const { oid, index: indexAfterOID } = readASN1OID(data, index)\n  switch (oid) {\n    // RSA\n    case '1.2.840.113549.1.1.4':\n      return 'MD5'\n    case '1.2.840.113549.1.1.5':\n      return 'SHA-1'\n    case '1.2.840.113549.1.1.11':\n      return 'SHA-256'\n    case '1.2.840.113549.1.1.12':\n      return 'SHA-384'\n    case '1.2.840.113549.1.1.13':\n      return 'SHA-512'\n    case '1.2.840.113549.1.1.14':\n      return 'SHA-224'\n    case '1.2.840.113549.1.1.15':\n      return 'SHA512-224'\n    case '1.2.840.113549.1.1.16':\n      return 'SHA512-256'\n    // ECDSA\n    case '1.2.840.10045.4.1':\n      return 'SHA-1'\n    case '1.2.840.10045.4.3.1':\n      return 'SHA-224'\n    case '1.2.840.10045.4.3.2':\n      return 'SHA-256'\n    case '1.2.840.10045.4.3.3':\n      return 'SHA-384'\n    case '1.2.840.10045.4.3.4':\n      return 'SHA-512'\n    // RSASSA-PSS: hash is indicated separately\n    case '1.2.840.113549.1.1.10': {\n      index = indexAfterOID\n      index = expectASN1Seq(data, index).index\n      if (data[index++] !== 0xa0) throw x509Error('non-tag data', data) // a0 = constructed tag 0\n      index = readASN1Length(data, index).index // skip over tag length field\n      index = expectASN1Seq(data, index).index // skip over sequence length field\n      const { oid: hashOID } = readASN1OID(data, index)\n      switch (hashOID) {\n        // standalone hash OIDs\n        case '1.2.840.113549.2.5':\n          return 'MD5'\n        case '********.2.26':\n          return 'SHA-1'\n        case '2.16.840.*********.2.1':\n          return 'SHA-256'\n        case '2.16.840.*********.2.2':\n          return 'SHA-384'\n        case '2.16.840.*********.2.3':\n          return 'SHA-512'\n      }\n      throw x509Error('unknown hash OID ' + hashOID, data)\n    }\n    // Ed25519 -- see https: return//github.com/openssl/openssl/issues/15477\n    case '***********':\n    case '***********': // ph\n      return 'SHA-512'\n    // Ed448 -- still not in pg 17.2 (if supported, digest would be SHAKE256 x 64 bytes)\n    case '***********':\n    case '***********': // ph\n      throw x509Error('Ed448 certificate channel binding is not currently supported by Postgres')\n  }\n  throw x509Error('unknown OID ' + oid, data)\n}\n\nmodule.exports = { signatureAlgorithmHashFromCertificate }\n", "'use strict'\nconst crypto = require('./utils')\nconst { signatureAlgorithmHashFromCertificate } = require('./cert-signatures')\n\nfunction startSession(mechanisms, stream) {\n  const candidates = ['SCRAM-SHA-256']\n  if (stream) candidates.unshift('SCRAM-SHA-256-PLUS') // higher-priority, so placed first\n\n  const mechanism = candidates.find((candidate) => mechanisms.includes(candidate))\n\n  if (!mechanism) {\n    throw new Error('SASL: Only mechanism(s) ' + candidates.join(' and ') + ' are supported')\n  }\n\n  if (mechanism === 'SCRAM-SHA-256-PLUS' && typeof stream.getPeerCertificate !== 'function') {\n    // this should never happen if we are really talking to a Postgres server\n    throw new Error('SASL: Mechanism SCRAM-SHA-256-PLUS requires a certificate')\n  }\n\n  const clientNonce = crypto.randomBytes(18).toString('base64')\n  const gs2Header = mechanism === 'SCRAM-SHA-256-PLUS' ? 'p=tls-server-end-point' : stream ? 'y' : 'n'\n\n  return {\n    mechanism,\n    clientNonce,\n    response: gs2Header + ',,n=*,r=' + clientNonce,\n    message: 'SASLInitialResponse',\n  }\n}\n\nasync function continueSession(session, password, serverData, stream) {\n  if (session.message !== 'SASLInitialResponse') {\n    throw new Error('SASL: Last message was not SASLInitialResponse')\n  }\n  if (typeof password !== 'string') {\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string')\n  }\n  if (password === '') {\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a non-empty string')\n  }\n  if (typeof serverData !== 'string') {\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: serverData must be a string')\n  }\n\n  const sv = parseServerFirstMessage(serverData)\n\n  if (!sv.nonce.startsWith(session.clientNonce)) {\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce does not start with client nonce')\n  } else if (sv.nonce.length === session.clientNonce.length) {\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce is too short')\n  }\n\n  const clientFirstMessageBare = 'n=*,r=' + session.clientNonce\n  const serverFirstMessage = 'r=' + sv.nonce + ',s=' + sv.salt + ',i=' + sv.iteration\n\n  // without channel binding:\n  let channelBinding = stream ? 'eSws' : 'biws' // 'y,,' or 'n,,', base64-encoded\n\n  // override if channel binding is in use:\n  if (session.mechanism === 'SCRAM-SHA-256-PLUS') {\n    const peerCert = stream.getPeerCertificate().raw\n    let hashName = signatureAlgorithmHashFromCertificate(peerCert)\n    if (hashName === 'MD5' || hashName === 'SHA-1') hashName = 'SHA-256'\n    const certHash = await crypto.hashByName(hashName, peerCert)\n    const bindingData = Buffer.concat([Buffer.from('p=tls-server-end-point,,'), Buffer.from(certHash)])\n    channelBinding = bindingData.toString('base64')\n  }\n\n  const clientFinalMessageWithoutProof = 'c=' + channelBinding + ',r=' + sv.nonce\n  const authMessage = clientFirstMessageBare + ',' + serverFirstMessage + ',' + clientFinalMessageWithoutProof\n\n  const saltBytes = Buffer.from(sv.salt, 'base64')\n  const saltedPassword = await crypto.deriveKey(password, saltBytes, sv.iteration)\n  const clientKey = await crypto.hmacSha256(saltedPassword, 'Client Key')\n  const storedKey = await crypto.sha256(clientKey)\n  const clientSignature = await crypto.hmacSha256(storedKey, authMessage)\n  const clientProof = xorBuffers(Buffer.from(clientKey), Buffer.from(clientSignature)).toString('base64')\n  const serverKey = await crypto.hmacSha256(saltedPassword, 'Server Key')\n  const serverSignatureBytes = await crypto.hmacSha256(serverKey, authMessage)\n\n  session.message = 'SASLResponse'\n  session.serverSignature = Buffer.from(serverSignatureBytes).toString('base64')\n  session.response = clientFinalMessageWithoutProof + ',p=' + clientProof\n}\n\nfunction finalizeSession(session, serverData) {\n  if (session.message !== 'SASLResponse') {\n    throw new Error('SASL: Last message was not SASLResponse')\n  }\n  if (typeof serverData !== 'string') {\n    throw new Error('SASL: SCRAM-SERVER-FINAL-MESSAGE: serverData must be a string')\n  }\n\n  const { serverSignature } = parseServerFinalMessage(serverData)\n\n  if (serverSignature !== session.serverSignature) {\n    throw new Error('SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature does not match')\n  }\n}\n\n/**\n * printable       = %x21-2B / %x2D-7E\n *                   ;; Printable ASCII except \",\".\n *                   ;; Note that any \"printable\" is also\n *                   ;; a valid \"value\".\n */\nfunction isPrintableChars(text) {\n  if (typeof text !== 'string') {\n    throw new TypeError('SASL: text must be a string')\n  }\n  return text\n    .split('')\n    .map((_, i) => text.charCodeAt(i))\n    .every((c) => (c >= 0x21 && c <= 0x2b) || (c >= 0x2d && c <= 0x7e))\n}\n\n/**\n * base64-char     = ALPHA / DIGIT / \"/\" / \"+\"\n *\n * base64-4        = 4base64-char\n *\n * base64-3        = 3base64-char \"=\"\n *\n * base64-2        = 2base64-char \"==\"\n *\n * base64          = *base64-4 [base64-3 / base64-2]\n */\nfunction isBase64(text) {\n  return /^(?:[a-zA-Z0-9+/]{4})*(?:[a-zA-Z0-9+/]{2}==|[a-zA-Z0-9+/]{3}=)?$/.test(text)\n}\n\nfunction parseAttributePairs(text) {\n  if (typeof text !== 'string') {\n    throw new TypeError('SASL: attribute pairs text must be a string')\n  }\n\n  return new Map(\n    text.split(',').map((attrValue) => {\n      if (!/^.=/.test(attrValue)) {\n        throw new Error('SASL: Invalid attribute pair entry')\n      }\n      const name = attrValue[0]\n      const value = attrValue.substring(2)\n      return [name, value]\n    })\n  )\n}\n\nfunction parseServerFirstMessage(data) {\n  const attrPairs = parseAttributePairs(data)\n\n  const nonce = attrPairs.get('r')\n  if (!nonce) {\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: nonce missing')\n  } else if (!isPrintableChars(nonce)) {\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: nonce must only contain printable characters')\n  }\n  const salt = attrPairs.get('s')\n  if (!salt) {\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: salt missing')\n  } else if (!isBase64(salt)) {\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: salt must be base64')\n  }\n  const iterationText = attrPairs.get('i')\n  if (!iterationText) {\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: iteration missing')\n  } else if (!/^[1-9][0-9]*$/.test(iterationText)) {\n    throw new Error('SASL: SCRAM-SERVER-FIRST-MESSAGE: invalid iteration count')\n  }\n  const iteration = parseInt(iterationText, 10)\n\n  return {\n    nonce,\n    salt,\n    iteration,\n  }\n}\n\nfunction parseServerFinalMessage(serverData) {\n  const attrPairs = parseAttributePairs(serverData)\n  const serverSignature = attrPairs.get('v')\n  if (!serverSignature) {\n    throw new Error('SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature is missing')\n  } else if (!isBase64(serverSignature)) {\n    throw new Error('SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature must be base64')\n  }\n  return {\n    serverSignature,\n  }\n}\n\nfunction xorBuffers(a, b) {\n  if (!Buffer.isBuffer(a)) {\n    throw new TypeError('first argument must be a Buffer')\n  }\n  if (!Buffer.isBuffer(b)) {\n    throw new TypeError('second argument must be a Buffer')\n  }\n  if (a.length !== b.length) {\n    throw new Error('Buffer lengths must match')\n  }\n  if (a.length === 0) {\n    throw new Error('Buffers cannot be empty')\n  }\n  return Buffer.from(a.map((_, i) => a[i] ^ b[i]))\n}\n\nmodule.exports = {\n  startSession,\n  continueSession,\n  finalizeSession,\n}\n", "'use strict'\n\nconst types = require('pg-types')\n\nfunction TypeOverrides(userTypes) {\n  this._types = userTypes || types\n  this.text = {}\n  this.binary = {}\n}\n\nTypeOverrides.prototype.getOverrides = function (format) {\n  switch (format) {\n    case 'text':\n      return this.text\n    case 'binary':\n      return this.binary\n    default:\n      return {}\n  }\n}\n\nTypeOverrides.prototype.setTypeParser = function (oid, format, parseFn) {\n  if (typeof format === 'function') {\n    parseFn = format\n    format = 'text'\n  }\n  this.getOverrides(format)[oid] = parseFn\n}\n\nTypeOverrides.prototype.getTypeParser = function (oid, format) {\n  format = format || 'text'\n  return this.getOverrides(format)[oid] || this._types.getTypeParser(oid, format)\n}\n\nmodule.exports = TypeOverrides\n", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"dns\" has been externalized for browser compatibility. Cannot access \"dns.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"fs\" has been externalized for browser compatibility. Cannot access \"fs.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "'use strict'\n\n//Parse method copied from https://github.com/brianc/node-postgres\n//Copyright (c) 2010-2014 <PERSON> (<EMAIL>)\n//MIT License\n\n//parses a connection string\nfunction parse(str, options = {}) {\n  //unix socket\n  if (str.charAt(0) === '/') {\n    const config = str.split(' ')\n    return { host: config[0], database: config[1] }\n  }\n\n  // Check for empty host in URL\n\n  const config = {}\n  let result\n  let dummyHost = false\n  if (/ |%[^a-f0-9]|%[a-f0-9][^a-f0-9]/i.test(str)) {\n    // Ensure spaces are encoded as %20\n    str = encodeURI(str).replace(/%25(\\d\\d)/g, '%$1')\n  }\n\n  try {\n    try {\n      result = new URL(str, 'postgres://base')\n    } catch (e) {\n      // The URL is invalid so try again with a dummy host\n      result = new URL(str.replace('@/', '@___DUMMY___/'), 'postgres://base')\n      dummyHost = true\n    }\n  } catch (err) {\n    // Remove the input from the error message to avoid leaking sensitive information\n    err.input && (err.input = '*****REDACTED*****')\n  }\n\n  // We'd like to use Object.fromEntries() here but Node.js 10 does not support it\n  for (const entry of result.searchParams.entries()) {\n    config[entry[0]] = entry[1]\n  }\n\n  config.user = config.user || decodeURIComponent(result.username)\n  config.password = config.password || decodeURIComponent(result.password)\n\n  if (result.protocol == 'socket:') {\n    config.host = decodeURI(result.pathname)\n    config.database = result.searchParams.get('db')\n    config.client_encoding = result.searchParams.get('encoding')\n    return config\n  }\n  const hostname = dummyHost ? '' : result.hostname\n  if (!config.host) {\n    // Only set the host if there is no equivalent query param.\n    config.host = decodeURIComponent(hostname)\n  } else if (hostname && /^%2f/i.test(hostname)) {\n    // Only prepend the hostname to the pathname if it is not a URL encoded Unix socket host.\n    result.pathname = hostname + result.pathname\n  }\n  if (!config.port) {\n    // Only set the port if there is no equivalent query param.\n    config.port = result.port\n  }\n\n  const pathname = result.pathname.slice(1) || null\n  config.database = pathname ? decodeURI(pathname) : null\n\n  if (config.ssl === 'true' || config.ssl === '1') {\n    config.ssl = true\n  }\n\n  if (config.ssl === '0') {\n    config.ssl = false\n  }\n\n  if (config.sslcert || config.sslkey || config.sslrootcert || config.sslmode) {\n    config.ssl = {}\n  }\n\n  // Only try to load fs if we expect to read from the disk\n  const fs = config.sslcert || config.sslkey || config.sslrootcert ? require('fs') : null\n\n  if (config.sslcert) {\n    config.ssl.cert = fs.readFileSync(config.sslcert).toString()\n  }\n\n  if (config.sslkey) {\n    config.ssl.key = fs.readFileSync(config.sslkey).toString()\n  }\n\n  if (config.sslrootcert) {\n    config.ssl.ca = fs.readFileSync(config.sslrootcert).toString()\n  }\n\n  if (options.useLibpqCompat && config.uselibpqcompat) {\n    throw new Error('Both useLibpqCompat and uselibpqcompat are set. Please use only one of them.')\n  }\n\n  if (config.uselibpqcompat === 'true' || options.useLibpqCompat) {\n    switch (config.sslmode) {\n      case 'disable': {\n        config.ssl = false\n        break\n      }\n      case 'prefer': {\n        config.ssl.rejectUnauthorized = false\n        break\n      }\n      case 'require': {\n        if (config.sslrootcert) {\n          // If a root CA is specified, behavior of `sslmode=require` will be the same as that of `verify-ca`\n          config.ssl.checkServerIdentity = function () {}\n        } else {\n          config.ssl.rejectUnauthorized = false\n        }\n        break\n      }\n      case 'verify-ca': {\n        if (!config.ssl.ca) {\n          throw new Error(\n            'SECURITY WARNING: Using sslmode=verify-ca requires specifying a CA with sslrootcert. If a public CA is used, verify-ca allows connections to a server that somebody else may have registered with the CA, making you vulnerable to Man-in-the-Middle attacks. Either specify a custom CA certificate with sslrootcert parameter or use sslmode=verify-full for proper security.'\n          )\n        }\n        config.ssl.checkServerIdentity = function () {}\n        break\n      }\n      case 'verify-full': {\n        break\n      }\n    }\n  } else {\n    switch (config.sslmode) {\n      case 'disable': {\n        config.ssl = false\n        break\n      }\n      case 'prefer':\n      case 'require':\n      case 'verify-ca':\n      case 'verify-full': {\n        break\n      }\n      case 'no-verify': {\n        config.ssl.rejectUnauthorized = false\n        break\n      }\n    }\n  }\n\n  return config\n}\n\n// convert pg-connection-string ssl config to a ClientConfig.ConnectionOptions\nfunction toConnectionOptions(sslConfig) {\n  const connectionOptions = Object.entries(sslConfig).reduce((c, [key, value]) => {\n    // we explicitly check for undefined and null instead of `if (value)` because some\n    // options accept falsy values. Example: `ssl.rejectUnauthorized = false`\n    if (value !== undefined && value !== null) {\n      c[key] = value\n    }\n\n    return c\n  }, {})\n\n  return connectionOptions\n}\n\n// convert pg-connection-string config to a ClientConfig\nfunction toClientConfig(config) {\n  const poolConfig = Object.entries(config).reduce((c, [key, value]) => {\n    if (key === 'ssl') {\n      const sslConfig = value\n\n      if (typeof sslConfig === 'boolean') {\n        c[key] = sslConfig\n      }\n\n      if (typeof sslConfig === 'object') {\n        c[key] = toConnectionOptions(sslConfig)\n      }\n    } else if (value !== undefined && value !== null) {\n      if (key === 'port') {\n        // when port is not specified, it is converted into an empty string\n        // we want to avoid NaN or empty string as a values in ClientConfig\n        if (value !== '') {\n          const v = parseInt(value, 10)\n          if (isNaN(v)) {\n            throw new Error(`Invalid ${key}: ${value}`)\n          }\n\n          c[key] = v\n        }\n      } else {\n        c[key] = value\n      }\n    }\n\n    return c\n  }, {})\n\n  return poolConfig\n}\n\n// parses a connection string into ClientConfig\nfunction parseIntoClientConfig(str) {\n  return toClientConfig(parse(str))\n}\n\nmodule.exports = parse\n\nparse.parse = parse\nparse.toClientConfig = toClientConfig\nparse.parseIntoClientConfig = parseIntoClientConfig\n", "'use strict'\n\nconst dns = require('dns')\n\nconst defaults = require('./defaults')\n\nconst parse = require('pg-connection-string').parse // parses a connection string\n\nconst val = function (key, config, envVar) {\n  if (envVar === undefined) {\n    envVar = process.env['PG' + key.toUpperCase()]\n  } else if (envVar === false) {\n    // do nothing ... use false\n  } else {\n    envVar = process.env[envVar]\n  }\n\n  return config[key] || envVar || defaults[key]\n}\n\nconst readSSLConfigFromEnvironment = function () {\n  switch (process.env.PGSSLMODE) {\n    case 'disable':\n      return false\n    case 'prefer':\n    case 'require':\n    case 'verify-ca':\n    case 'verify-full':\n      return true\n    case 'no-verify':\n      return { rejectUnauthorized: false }\n  }\n  return defaults.ssl\n}\n\n// Convert arg to a string, surround in single quotes, and escape single quotes and backslashes\nconst quoteParamValue = function (value) {\n  return \"'\" + ('' + value).replace(/\\\\/g, '\\\\\\\\').replace(/'/g, \"\\\\'\") + \"'\"\n}\n\nconst add = function (params, config, paramName) {\n  const value = config[paramName]\n  if (value !== undefined && value !== null) {\n    params.push(paramName + '=' + quoteParamValue(value))\n  }\n}\n\nclass ConnectionParameters {\n  constructor(config) {\n    // if a string is passed, it is a raw connection string so we parse it into a config\n    config = typeof config === 'string' ? parse(config) : config || {}\n\n    // if the config has a connectionString defined, parse IT into the config we use\n    // this will override other default values with what is stored in connectionString\n    if (config.connectionString) {\n      config = Object.assign({}, config, parse(config.connectionString))\n    }\n\n    this.user = val('user', config)\n    this.database = val('database', config)\n\n    if (this.database === undefined) {\n      this.database = this.user\n    }\n\n    this.port = parseInt(val('port', config), 10)\n    this.host = val('host', config)\n\n    // \"hiding\" the password so it doesn't show up in stack traces\n    // or if the client is console.logged\n    Object.defineProperty(this, 'password', {\n      configurable: true,\n      enumerable: false,\n      writable: true,\n      value: val('password', config),\n    })\n\n    this.binary = val('binary', config)\n    this.options = val('options', config)\n\n    this.ssl = typeof config.ssl === 'undefined' ? readSSLConfigFromEnvironment() : config.ssl\n\n    if (typeof this.ssl === 'string') {\n      if (this.ssl === 'true') {\n        this.ssl = true\n      }\n    }\n    // support passing in ssl=no-verify via connection string\n    if (this.ssl === 'no-verify') {\n      this.ssl = { rejectUnauthorized: false }\n    }\n    if (this.ssl && this.ssl.key) {\n      Object.defineProperty(this.ssl, 'key', {\n        enumerable: false,\n      })\n    }\n\n    this.client_encoding = val('client_encoding', config)\n    this.replication = val('replication', config)\n    // a domain socket begins with '/'\n    this.isDomainSocket = !(this.host || '').indexOf('/')\n\n    this.application_name = val('application_name', config, 'PGAPPNAME')\n    this.fallback_application_name = val('fallback_application_name', config, false)\n    this.statement_timeout = val('statement_timeout', config, false)\n    this.lock_timeout = val('lock_timeout', config, false)\n    this.idle_in_transaction_session_timeout = val('idle_in_transaction_session_timeout', config, false)\n    this.query_timeout = val('query_timeout', config, false)\n\n    if (config.connectionTimeoutMillis === undefined) {\n      this.connect_timeout = process.env.PGCONNECT_TIMEOUT || 0\n    } else {\n      this.connect_timeout = Math.floor(config.connectionTimeoutMillis / 1000)\n    }\n\n    if (config.keepAlive === false) {\n      this.keepalives = 0\n    } else if (config.keepAlive === true) {\n      this.keepalives = 1\n    }\n\n    if (typeof config.keepAliveInitialDelayMillis === 'number') {\n      this.keepalives_idle = Math.floor(config.keepAliveInitialDelayMillis / 1000)\n    }\n  }\n\n  getLibpqConnectionString(cb) {\n    const params = []\n    add(params, this, 'user')\n    add(params, this, 'password')\n    add(params, this, 'port')\n    add(params, this, 'application_name')\n    add(params, this, 'fallback_application_name')\n    add(params, this, 'connect_timeout')\n    add(params, this, 'options')\n\n    const ssl = typeof this.ssl === 'object' ? this.ssl : this.ssl ? { sslmode: this.ssl } : {}\n    add(params, ssl, 'sslmode')\n    add(params, ssl, 'sslca')\n    add(params, ssl, 'sslkey')\n    add(params, ssl, 'sslcert')\n    add(params, ssl, 'sslrootcert')\n\n    if (this.database) {\n      params.push('dbname=' + quoteParamValue(this.database))\n    }\n    if (this.replication) {\n      params.push('replication=' + quoteParamValue(this.replication))\n    }\n    if (this.host) {\n      params.push('host=' + quoteParamValue(this.host))\n    }\n    if (this.isDomainSocket) {\n      return cb(null, params.join(' '))\n    }\n    if (this.client_encoding) {\n      params.push('client_encoding=' + quoteParamValue(this.client_encoding))\n    }\n    dns.lookup(this.host, function (err, address) {\n      if (err) return cb(err, null)\n      params.push('hostaddr=' + quoteParamValue(address))\n      return cb(null, params.join(' '))\n    })\n  }\n}\n\nmodule.exports = ConnectionParameters\n", "'use strict'\n\nconst types = require('pg-types')\n\nconst matchRegexp = /^([A-Za-z]+)(?: (\\d+))?(?: (\\d+))?/\n\n// result object returned from query\n// in the 'end' event and also\n// passed as second argument to provided callback\nclass Result {\n  constructor(rowMode, types) {\n    this.command = null\n    this.rowCount = null\n    this.oid = null\n    this.rows = []\n    this.fields = []\n    this._parsers = undefined\n    this._types = types\n    this.RowCtor = null\n    this.rowAsArray = rowMode === 'array'\n    if (this.rowAsArray) {\n      this.parseRow = this._parseRowAsArray\n    }\n    this._prebuiltEmptyResultObject = null\n  }\n\n  // adds a command complete message\n  addCommandComplete(msg) {\n    let match\n    if (msg.text) {\n      // pure javascript\n      match = matchRegexp.exec(msg.text)\n    } else {\n      // native bindings\n      match = matchRegexp.exec(msg.command)\n    }\n    if (match) {\n      this.command = match[1]\n      if (match[3]) {\n        // COMMAND OID ROWS\n        this.oid = parseInt(match[2], 10)\n        this.rowCount = parseInt(match[3], 10)\n      } else if (match[2]) {\n        // COMMAND ROWS\n        this.rowCount = parseInt(match[2], 10)\n      }\n    }\n  }\n\n  _parseRowAsArray(rowData) {\n    const row = new Array(rowData.length)\n    for (let i = 0, len = rowData.length; i < len; i++) {\n      const rawValue = rowData[i]\n      if (rawValue !== null) {\n        row[i] = this._parsers[i](rawValue)\n      } else {\n        row[i] = null\n      }\n    }\n    return row\n  }\n\n  parseRow(rowData) {\n    const row = { ...this._prebuiltEmptyResultObject }\n    for (let i = 0, len = rowData.length; i < len; i++) {\n      const rawValue = rowData[i]\n      const field = this.fields[i].name\n      if (rawValue !== null) {\n        row[field] = this._parsers[i](rawValue)\n      } else {\n        row[field] = null\n      }\n    }\n    return row\n  }\n\n  addRow(row) {\n    this.rows.push(row)\n  }\n\n  addFields(fieldDescriptions) {\n    // clears field definitions\n    // multiple query statements in 1 action can result in multiple sets\n    // of rowDescriptions...eg: 'select NOW(); select 1::int;'\n    // you need to reset the fields\n    this.fields = fieldDescriptions\n    if (this.fields.length) {\n      this._parsers = new Array(fieldDescriptions.length)\n    }\n\n    const row = {}\n\n    for (let i = 0; i < fieldDescriptions.length; i++) {\n      const desc = fieldDescriptions[i]\n      row[desc.name] = null\n\n      if (this._types) {\n        this._parsers[i] = this._types.getTypeParser(desc.dataTypeID, desc.format || 'text')\n      } else {\n        this._parsers[i] = types.getTypeParser(desc.dataTypeID, desc.format || 'text')\n      }\n    }\n\n    this._prebuiltEmptyResultObject = { ...row }\n  }\n}\n\nmodule.exports = Result\n", "'use strict'\n\nconst { EventEmitter } = require('events')\n\nconst Result = require('./result')\nconst utils = require('./utils')\n\nclass Query extends EventEmitter {\n  constructor(config, values, callback) {\n    super()\n\n    config = utils.normalizeQueryConfig(config, values, callback)\n\n    this.text = config.text\n    this.values = config.values\n    this.rows = config.rows\n    this.types = config.types\n    this.name = config.name\n    this.queryMode = config.queryMode\n    this.binary = config.binary\n    // use unique portal name each time\n    this.portal = config.portal || ''\n    this.callback = config.callback\n    this._rowMode = config.rowMode\n    if (process.domain && config.callback) {\n      this.callback = process.domain.bind(config.callback)\n    }\n    this._result = new Result(this._rowMode, this.types)\n\n    // potential for multiple results\n    this._results = this._result\n    this._canceledDueToError = false\n  }\n\n  requiresPreparation() {\n    if (this.queryMode === 'extended') {\n      return true\n    }\n\n    // named queries must always be prepared\n    if (this.name) {\n      return true\n    }\n    // always prepare if there are max number of rows expected per\n    // portal execution\n    if (this.rows) {\n      return true\n    }\n    // don't prepare empty text queries\n    if (!this.text) {\n      return false\n    }\n    // prepare if there are values\n    if (!this.values) {\n      return false\n    }\n    return this.values.length > 0\n  }\n\n  _checkForMultirow() {\n    // if we already have a result with a command property\n    // then we've already executed one query in a multi-statement simple query\n    // turn our results into an array of results\n    if (this._result.command) {\n      if (!Array.isArray(this._results)) {\n        this._results = [this._result]\n      }\n      this._result = new Result(this._rowMode, this._result._types)\n      this._results.push(this._result)\n    }\n  }\n\n  // associates row metadata from the supplied\n  // message with this query object\n  // metadata used when parsing row results\n  handleRowDescription(msg) {\n    this._checkForMultirow()\n    this._result.addFields(msg.fields)\n    this._accumulateRows = this.callback || !this.listeners('row').length\n  }\n\n  handleDataRow(msg) {\n    let row\n\n    if (this._canceledDueToError) {\n      return\n    }\n\n    try {\n      row = this._result.parseRow(msg.fields)\n    } catch (err) {\n      this._canceledDueToError = err\n      return\n    }\n\n    this.emit('row', row, this._result)\n    if (this._accumulateRows) {\n      this._result.addRow(row)\n    }\n  }\n\n  handleCommandComplete(msg, connection) {\n    this._checkForMultirow()\n    this._result.addCommandComplete(msg)\n    // need to sync after each command complete of a prepared statement\n    // if we were using a row count which results in multiple calls to _getRows\n    if (this.rows) {\n      connection.sync()\n    }\n  }\n\n  // if a named prepared statement is created with empty query text\n  // the backend will send an emptyQuery message but *not* a command complete message\n  // since we pipeline sync immediately after execute we don't need to do anything here\n  // unless we have rows specified, in which case we did not pipeline the intial sync call\n  handleEmptyQuery(connection) {\n    if (this.rows) {\n      connection.sync()\n    }\n  }\n\n  handleError(err, connection) {\n    // need to sync after error during a prepared statement\n    if (this._canceledDueToError) {\n      err = this._canceledDueToError\n      this._canceledDueToError = false\n    }\n    // if callback supplied do not emit error event as uncaught error\n    // events will bubble up to node process\n    if (this.callback) {\n      return this.callback(err)\n    }\n    this.emit('error', err)\n  }\n\n  handleReadyForQuery(con) {\n    if (this._canceledDueToError) {\n      return this.handleError(this._canceledDueToError, con)\n    }\n    if (this.callback) {\n      try {\n        this.callback(null, this._results)\n      } catch (err) {\n        process.nextTick(() => {\n          throw err\n        })\n      }\n    }\n    this.emit('end', this._results)\n  }\n\n  submit(connection) {\n    if (typeof this.text !== 'string' && typeof this.name !== 'string') {\n      return new Error('A query must have either text or a name. Supplying neither is unsupported.')\n    }\n    const previous = connection.parsedStatements[this.name]\n    if (this.text && previous && this.text !== previous) {\n      return new Error(`Prepared statements must be unique - '${this.name}' was used for a different statement`)\n    }\n    if (this.values && !Array.isArray(this.values)) {\n      return new Error('Query values must be an array')\n    }\n    if (this.requiresPreparation()) {\n      // If we're using the extended query protocol we fire off several separate commands\n      // to the backend. On some versions of node & some operating system versions\n      // the network stack writes each message separately instead of buffering them together\n      // causing the client & network to send more slowly. Corking & uncorking the stream\n      // allows node to buffer up the messages internally before sending them all off at once.\n      // note: we're checking for existence of cork/uncork because some versions of streams\n      // might not have this (cloudflare?)\n      connection.stream.cork && connection.stream.cork()\n      try {\n        this.prepare(connection)\n      } finally {\n        // while unlikely for this.prepare to throw, if it does & we don't uncork this stream\n        // this client becomes unresponsive, so put in finally block \"just in case\"\n        connection.stream.uncork && connection.stream.uncork()\n      }\n    } else {\n      connection.query(this.text)\n    }\n    return null\n  }\n\n  hasBeenParsed(connection) {\n    return this.name && connection.parsedStatements[this.name]\n  }\n\n  handlePortalSuspended(connection) {\n    this._getRows(connection, this.rows)\n  }\n\n  _getRows(connection, rows) {\n    connection.execute({\n      portal: this.portal,\n      rows: rows,\n    })\n    // if we're not reading pages of rows send the sync command\n    // to indicate the pipeline is finished\n    if (!rows) {\n      connection.sync()\n    } else {\n      // otherwise flush the call out to read more rows\n      connection.flush()\n    }\n  }\n\n  // http://developer.postgresql.org/pgdocs/postgres/protocol-flow.html#PROTOCOL-FLOW-EXT-QUERY\n  prepare(connection) {\n    // TODO refactor this poor encapsulation\n    if (!this.hasBeenParsed(connection)) {\n      connection.parse({\n        text: this.text,\n        name: this.name,\n        types: this.types,\n      })\n    }\n\n    // because we're mapping user supplied values to\n    // postgres wire protocol compatible values it could\n    // throw an exception, so try/catch this section\n    try {\n      connection.bind({\n        portal: this.portal,\n        statement: this.name,\n        values: this.values,\n        binary: this.binary,\n        valueMapper: utils.prepareValue,\n      })\n    } catch (err) {\n      this.handleError(err, connection)\n      return\n    }\n\n    connection.describe({\n      type: 'P',\n      name: this.portal || '',\n    })\n\n    this._getRows(connection, this.rows)\n  }\n\n  handleCopyInResponse(connection) {\n    connection.sendCopyFail('No source stream defined')\n  }\n\n  handleCopyData(msg, connection) {\n    // noop\n  }\n}\n\nmodule.exports = Query\n", "export type Mode = 'text' | 'binary'\n\nexport type MessageName =\n  | 'parseComplete'\n  | 'bindComplete'\n  | 'closeComplete'\n  | 'noData'\n  | 'portalSuspended'\n  | 'replicationStart'\n  | 'emptyQuery'\n  | 'copyDone'\n  | 'copyData'\n  | 'rowDescription'\n  | 'parameterDescription'\n  | 'parameterStatus'\n  | 'backendKeyData'\n  | 'notification'\n  | 'readyForQuery'\n  | 'commandComplete'\n  | 'dataRow'\n  | 'copyInResponse'\n  | 'copyOutResponse'\n  | 'authenticationOk'\n  | 'authenticationMD5Password'\n  | 'authenticationCleartextPassword'\n  | 'authenticationSASL'\n  | 'authenticationSASLContinue'\n  | 'authenticationSASLFinal'\n  | 'error'\n  | 'notice'\n\nexport interface BackendMessage {\n  name: MessageName\n  length: number\n}\n\nexport const parseComplete: BackendMessage = {\n  name: 'parseComplete',\n  length: 5,\n}\n\nexport const bindComplete: BackendMessage = {\n  name: 'bindComplete',\n  length: 5,\n}\n\nexport const closeComplete: BackendMessage = {\n  name: 'closeComplete',\n  length: 5,\n}\n\nexport const noData: BackendMessage = {\n  name: 'noData',\n  length: 5,\n}\n\nexport const portalSuspended: BackendMessage = {\n  name: 'portalSuspended',\n  length: 5,\n}\n\nexport const replicationStart: BackendMessage = {\n  name: 'replicationStart',\n  length: 4,\n}\n\nexport const emptyQuery: BackendMessage = {\n  name: 'emptyQuery',\n  length: 4,\n}\n\nexport const copyDone: BackendMessage = {\n  name: 'copyDone',\n  length: 4,\n}\n\ninterface NoticeOrError {\n  message: string | undefined\n  severity: string | undefined\n  code: string | undefined\n  detail: string | undefined\n  hint: string | undefined\n  position: string | undefined\n  internalPosition: string | undefined\n  internalQuery: string | undefined\n  where: string | undefined\n  schema: string | undefined\n  table: string | undefined\n  column: string | undefined\n  dataType: string | undefined\n  constraint: string | undefined\n  file: string | undefined\n  line: string | undefined\n  routine: string | undefined\n}\n\nexport class DatabaseError extends Error implements NoticeOrError {\n  public severity: string | undefined\n  public code: string | undefined\n  public detail: string | undefined\n  public hint: string | undefined\n  public position: string | undefined\n  public internalPosition: string | undefined\n  public internalQuery: string | undefined\n  public where: string | undefined\n  public schema: string | undefined\n  public table: string | undefined\n  public column: string | undefined\n  public dataType: string | undefined\n  public constraint: string | undefined\n  public file: string | undefined\n  public line: string | undefined\n  public routine: string | undefined\n  constructor(\n    message: string,\n    public readonly length: number,\n    public readonly name: MessageName\n  ) {\n    super(message)\n  }\n}\n\nexport class CopyDataMessage {\n  public readonly name = 'copyData'\n  constructor(\n    public readonly length: number,\n    public readonly chunk: Buffer\n  ) {}\n}\n\nexport class CopyResponse {\n  public readonly columnTypes: number[]\n  constructor(\n    public readonly length: number,\n    public readonly name: MessageName,\n    public readonly binary: boolean,\n    columnCount: number\n  ) {\n    this.columnTypes = new Array(columnCount)\n  }\n}\n\nexport class Field {\n  constructor(\n    public readonly name: string,\n    public readonly tableID: number,\n    public readonly columnID: number,\n    public readonly dataTypeID: number,\n    public readonly dataTypeSize: number,\n    public readonly dataTypeModifier: number,\n    public readonly format: Mode\n  ) {}\n}\n\nexport class RowDescriptionMessage {\n  public readonly name: MessageName = 'rowDescription'\n  public readonly fields: Field[]\n  constructor(\n    public readonly length: number,\n    public readonly fieldCount: number\n  ) {\n    this.fields = new Array(this.fieldCount)\n  }\n}\n\nexport class ParameterDescriptionMessage {\n  public readonly name: MessageName = 'parameterDescription'\n  public readonly dataTypeIDs: number[]\n  constructor(\n    public readonly length: number,\n    public readonly parameterCount: number\n  ) {\n    this.dataTypeIDs = new Array(this.parameterCount)\n  }\n}\n\nexport class ParameterStatusMessage {\n  public readonly name: MessageName = 'parameterStatus'\n  constructor(\n    public readonly length: number,\n    public readonly parameterName: string,\n    public readonly parameterValue: string\n  ) {}\n}\n\nexport class AuthenticationMD5Password implements BackendMessage {\n  public readonly name: MessageName = 'authenticationMD5Password'\n  constructor(\n    public readonly length: number,\n    public readonly salt: Buffer\n  ) {}\n}\n\nexport class BackendKeyDataMessage {\n  public readonly name: MessageName = 'backendKeyData'\n  constructor(\n    public readonly length: number,\n    public readonly processID: number,\n    public readonly secretKey: number\n  ) {}\n}\n\nexport class NotificationResponseMessage {\n  public readonly name: MessageName = 'notification'\n  constructor(\n    public readonly length: number,\n    public readonly processId: number,\n    public readonly channel: string,\n    public readonly payload: string\n  ) {}\n}\n\nexport class ReadyForQueryMessage {\n  public readonly name: MessageName = 'readyForQuery'\n  constructor(\n    public readonly length: number,\n    public readonly status: string\n  ) {}\n}\n\nexport class CommandCompleteMessage {\n  public readonly name: MessageName = 'commandComplete'\n  constructor(\n    public readonly length: number,\n    public readonly text: string\n  ) {}\n}\n\nexport class DataRowMessage {\n  public readonly fieldCount: number\n  public readonly name: MessageName = 'dataRow'\n  constructor(\n    public length: number,\n    public fields: any[]\n  ) {\n    this.fieldCount = fields.length\n  }\n}\n\nexport class NoticeMessage implements BackendMessage, NoticeOrError {\n  constructor(\n    public readonly length: number,\n    public readonly message: string | undefined\n  ) {}\n  public readonly name = 'notice'\n  public severity: string | undefined\n  public code: string | undefined\n  public detail: string | undefined\n  public hint: string | undefined\n  public position: string | undefined\n  public internalPosition: string | undefined\n  public internalQuery: string | undefined\n  public where: string | undefined\n  public schema: string | undefined\n  public table: string | undefined\n  public column: string | undefined\n  public dataType: string | undefined\n  public constraint: string | undefined\n  public file: string | undefined\n  public line: string | undefined\n  public routine: string | undefined\n}\n", "//binary data writer tuned for encoding binary specific to the postgres binary protocol\n\nexport class Writer {\n  private buffer: <PERSON>uffer\n  private offset: number = 5\n  private headerPosition: number = 0\n  constructor(private size = 256) {\n    this.buffer = Buffer.allocUnsafe(size)\n  }\n\n  private ensure(size: number): void {\n    const remaining = this.buffer.length - this.offset\n    if (remaining < size) {\n      const oldBuffer = this.buffer\n      // exponential growth factor of around ~ 1.5\n      // https://stackoverflow.com/questions/2269063/buffer-growth-strategy\n      const newSize = oldBuffer.length + (oldBuffer.length >> 1) + size\n      this.buffer = Buffer.allocUnsafe(newSize)\n      oldBuffer.copy(this.buffer)\n    }\n  }\n\n  public addInt32(num: number): Writer {\n    this.ensure(4)\n    this.buffer[this.offset++] = (num >>> 24) & 0xff\n    this.buffer[this.offset++] = (num >>> 16) & 0xff\n    this.buffer[this.offset++] = (num >>> 8) & 0xff\n    this.buffer[this.offset++] = (num >>> 0) & 0xff\n    return this\n  }\n\n  public addInt16(num: number): Writer {\n    this.ensure(2)\n    this.buffer[this.offset++] = (num >>> 8) & 0xff\n    this.buffer[this.offset++] = (num >>> 0) & 0xff\n    return this\n  }\n\n  public addCString(string: string): Writer {\n    if (!string) {\n      this.ensure(1)\n    } else {\n      const len = Buffer.byteLength(string)\n      this.ensure(len + 1) // +1 for null terminator\n      this.buffer.write(string, this.offset, 'utf-8')\n      this.offset += len\n    }\n\n    this.buffer[this.offset++] = 0 // null terminator\n    return this\n  }\n\n  public addString(string: string = ''): Writer {\n    const len = Buffer.byteLength(string)\n    this.ensure(len)\n    this.buffer.write(string, this.offset)\n    this.offset += len\n    return this\n  }\n\n  public add(otherBuffer: Buffer): Writer {\n    this.ensure(otherBuffer.length)\n    otherBuffer.copy(this.buffer, this.offset)\n    this.offset += otherBuffer.length\n    return this\n  }\n\n  private join(code?: number): Buffer {\n    if (code) {\n      this.buffer[this.headerPosition] = code\n      //length is everything in this packet minus the code\n      const length = this.offset - (this.headerPosition + 1)\n      this.buffer.writeInt32BE(length, this.headerPosition + 1)\n    }\n    return this.buffer.slice(code ? 0 : 5, this.offset)\n  }\n\n  public flush(code?: number): Buffer {\n    const result = this.join(code)\n    this.offset = 5\n    this.headerPosition = 0\n    this.buffer = Buffer.allocUnsafe(this.size)\n    return result\n  }\n}\n", "import { Writer } from './buffer-writer'\n\nconst enum code {\n  startup = 0x70,\n  query = 0x51,\n  parse = 0x50,\n  bind = 0x42,\n  execute = 0x45,\n  flush = 0x48,\n  sync = 0x53,\n  end = 0x58,\n  close = 0x43,\n  describe = 0x44,\n  copyFromChunk = 0x64,\n  copyDone = 0x63,\n  copyFail = 0x66,\n}\n\nconst writer = new Writer()\n\nconst startup = (opts: Record<string, string>): Buffer => {\n  // protocol version\n  writer.addInt16(3).addInt16(0)\n  for (const key of Object.keys(opts)) {\n    writer.addCString(key).addCString(opts[key])\n  }\n\n  writer.addCString('client_encoding').addCString('UTF8')\n\n  const bodyBuffer = writer.addCString('').flush()\n  // this message is sent without a code\n\n  const length = bodyBuffer.length + 4\n\n  return new Writer().addInt32(length).add(bodyBuffer).flush()\n}\n\nconst requestSsl = (): Buffer => {\n  const response = Buffer.allocUnsafe(8)\n  response.writeInt32BE(8, 0)\n  response.writeInt32BE(80877103, 4)\n  return response\n}\n\nconst password = (password: string): Buffer => {\n  return writer.addCString(password).flush(code.startup)\n}\n\nconst sendSASLInitialResponseMessage = function (mechanism: string, initialResponse: string): Buffer {\n  // 0x70 = 'p'\n  writer.addCString(mechanism).addInt32(Buffer.byteLength(initialResponse)).addString(initialResponse)\n\n  return writer.flush(code.startup)\n}\n\nconst sendSCRAMClientFinalMessage = function (additionalData: string): Buffer {\n  return writer.addString(additionalData).flush(code.startup)\n}\n\nconst query = (text: string): Buffer => {\n  return writer.addCString(text).flush(code.query)\n}\n\ntype ParseOpts = {\n  name?: string\n  types?: number[]\n  text: string\n}\n\nconst emptyArray: any[] = []\n\nconst parse = (query: ParseOpts): Buffer => {\n  // expect something like this:\n  // { name: 'queryName',\n  //   text: 'select * from blah',\n  //   types: ['int8', 'bool'] }\n\n  // normalize missing query names to allow for null\n  const name = query.name || ''\n  if (name.length > 63) {\n    console.error('Warning! Postgres only supports 63 characters for query names.')\n    console.error('You supplied %s (%s)', name, name.length)\n    console.error('This can cause conflicts and silent errors executing queries')\n  }\n\n  const types = query.types || emptyArray\n\n  const len = types.length\n\n  const buffer = writer\n    .addCString(name) // name of query\n    .addCString(query.text) // actual query text\n    .addInt16(len)\n\n  for (let i = 0; i < len; i++) {\n    buffer.addInt32(types[i])\n  }\n\n  return writer.flush(code.parse)\n}\n\ntype ValueMapper = (param: any, index: number) => any\n\ntype BindOpts = {\n  portal?: string\n  binary?: boolean\n  statement?: string\n  values?: any[]\n  // optional map from JS value to postgres value per parameter\n  valueMapper?: ValueMapper\n}\n\nconst paramWriter = new Writer()\n\n// make this a const enum so typescript will inline the value\nconst enum ParamType {\n  STRING = 0,\n  BINARY = 1,\n}\n\nconst writeValues = function (values: any[], valueMapper?: ValueMapper): void {\n  for (let i = 0; i < values.length; i++) {\n    const mappedVal = valueMapper ? valueMapper(values[i], i) : values[i]\n    if (mappedVal == null) {\n      // add the param type (string) to the writer\n      writer.addInt16(ParamType.STRING)\n      // write -1 to the param writer to indicate null\n      paramWriter.addInt32(-1)\n    } else if (mappedVal instanceof Buffer) {\n      // add the param type (binary) to the writer\n      writer.addInt16(ParamType.BINARY)\n      // add the buffer to the param writer\n      paramWriter.addInt32(mappedVal.length)\n      paramWriter.add(mappedVal)\n    } else {\n      // add the param type (string) to the writer\n      writer.addInt16(ParamType.STRING)\n      paramWriter.addInt32(Buffer.byteLength(mappedVal))\n      paramWriter.addString(mappedVal)\n    }\n  }\n}\n\nconst bind = (config: BindOpts = {}): Buffer => {\n  // normalize config\n  const portal = config.portal || ''\n  const statement = config.statement || ''\n  const binary = config.binary || false\n  const values = config.values || emptyArray\n  const len = values.length\n\n  writer.addCString(portal).addCString(statement)\n  writer.addInt16(len)\n\n  writeValues(values, config.valueMapper)\n\n  writer.addInt16(len)\n  writer.add(paramWriter.flush())\n\n  // format code\n  writer.addInt16(binary ? ParamType.BINARY : ParamType.STRING)\n  return writer.flush(code.bind)\n}\n\ntype ExecOpts = {\n  portal?: string\n  rows?: number\n}\n\nconst emptyExecute = Buffer.from([code.execute, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00])\n\nconst execute = (config?: ExecOpts): Buffer => {\n  // this is the happy path for most queries\n  if (!config || (!config.portal && !config.rows)) {\n    return emptyExecute\n  }\n\n  const portal = config.portal || ''\n  const rows = config.rows || 0\n\n  const portalLength = Buffer.byteLength(portal)\n  const len = 4 + portalLength + 1 + 4\n  // one extra bit for code\n  const buff = Buffer.allocUnsafe(1 + len)\n  buff[0] = code.execute\n  buff.writeInt32BE(len, 1)\n  buff.write(portal, 5, 'utf-8')\n  buff[portalLength + 5] = 0 // null terminate portal cString\n  buff.writeUInt32BE(rows, buff.length - 4)\n  return buff\n}\n\nconst cancel = (processID: number, secretKey: number): Buffer => {\n  const buffer = Buffer.allocUnsafe(16)\n  buffer.writeInt32BE(16, 0)\n  buffer.writeInt16BE(1234, 4)\n  buffer.writeInt16BE(5678, 6)\n  buffer.writeInt32BE(processID, 8)\n  buffer.writeInt32BE(secretKey, 12)\n  return buffer\n}\n\ntype PortalOpts = {\n  type: 'S' | 'P'\n  name?: string\n}\n\nconst cstringMessage = (code: code, string: string): Buffer => {\n  const stringLen = Buffer.byteLength(string)\n  const len = 4 + stringLen + 1\n  // one extra bit for code\n  const buffer = Buffer.allocUnsafe(1 + len)\n  buffer[0] = code\n  buffer.writeInt32BE(len, 1)\n  buffer.write(string, 5, 'utf-8')\n  buffer[len] = 0 // null terminate cString\n  return buffer\n}\n\nconst emptyDescribePortal = writer.addCString('P').flush(code.describe)\nconst emptyDescribeStatement = writer.addCString('S').flush(code.describe)\n\nconst describe = (msg: PortalOpts): Buffer => {\n  return msg.name\n    ? cstringMessage(code.describe, `${msg.type}${msg.name || ''}`)\n    : msg.type === 'P'\n    ? emptyDescribePortal\n    : emptyDescribeStatement\n}\n\nconst close = (msg: PortalOpts): Buffer => {\n  const text = `${msg.type}${msg.name || ''}`\n  return cstringMessage(code.close, text)\n}\n\nconst copyData = (chunk: Buffer): Buffer => {\n  return writer.add(chunk).flush(code.copyFromChunk)\n}\n\nconst copyFail = (message: string): Buffer => {\n  return cstringMessage(code.copyFail, message)\n}\n\nconst codeOnlyBuffer = (code: code): Buffer => Buffer.from([code, 0x00, 0x00, 0x00, 0x04])\n\nconst flushBuffer = codeOnlyBuffer(code.flush)\nconst syncBuffer = codeOnlyBuffer(code.sync)\nconst endBuffer = codeOnlyBuffer(code.end)\nconst copyDoneBuffer = codeOnlyBuffer(code.copyDone)\n\nconst serialize = {\n  startup,\n  password,\n  requestSsl,\n  sendSASLInitialResponseMessage,\n  sendSCRAMClientFinalMessage,\n  query,\n  parse,\n  bind,\n  execute,\n  describe,\n  close,\n  flush: () => flushBuffer,\n  sync: () => syncBuffer,\n  end: () => endBuffer,\n  copyData,\n  copyDone: () => copyDoneBuffer,\n  copyFail,\n  cancel,\n}\n\nexport { serialize }\n", "const emptyBuffer = Buffer.allocUnsafe(0)\n\nexport class Buffer<PERSON>eader {\n  private buffer: Buffer = emptyBuffer\n\n  // TODO(bmc): support non-utf8 encoding?\n  private encoding: string = 'utf-8'\n\n  constructor(private offset: number = 0) {}\n\n  public setBuffer(offset: number, buffer: Buffer): void {\n    this.offset = offset\n    this.buffer = buffer\n  }\n\n  public int16(): number {\n    const result = this.buffer.readInt16BE(this.offset)\n    this.offset += 2\n    return result\n  }\n\n  public byte(): number {\n    const result = this.buffer[this.offset]\n    this.offset++\n    return result\n  }\n\n  public int32(): number {\n    const result = this.buffer.readInt32BE(this.offset)\n    this.offset += 4\n    return result\n  }\n\n  public uint32(): number {\n    const result = this.buffer.readUInt32BE(this.offset)\n    this.offset += 4\n    return result\n  }\n\n  public string(length: number): string {\n    const result = this.buffer.toString(this.encoding, this.offset, this.offset + length)\n    this.offset += length\n    return result\n  }\n\n  public cstring(): string {\n    const start = this.offset\n    let end = start\n    // eslint-disable-next-line no-empty\n    while (this.buffer[end++] !== 0) {}\n    this.offset = end\n    return this.buffer.toString(this.encoding, start, end - 1)\n  }\n\n  public bytes(length: number): Buffer {\n    const result = this.buffer.slice(this.offset, this.offset + length)\n    this.offset += length\n    return result\n  }\n}\n", "import { TransformOptions } from 'stream'\nimport {\n  Mode,\n  bindComplete,\n  parseComplete,\n  closeComplete,\n  noData,\n  portalSuspended,\n  copyDone,\n  replicationStart,\n  emptyQuery,\n  ReadyForQueryMessage,\n  CommandCompleteMessage,\n  CopyDataMessage,\n  CopyResponse,\n  NotificationResponseMessage,\n  RowDescriptionMessage,\n  ParameterDescriptionMessage,\n  Field,\n  DataRowMessage,\n  ParameterStatusMessage,\n  BackendKeyDataMessage,\n  DatabaseError,\n  BackendMessage,\n  MessageName,\n  AuthenticationMD5Password,\n  NoticeMessage,\n} from './messages'\nimport { BufferReader } from './buffer-reader'\n\n// every message is prefixed with a single bye\nconst CODE_LENGTH = 1\n// every message has an int32 length which includes itself but does\n// NOT include the code in the length\nconst LEN_LENGTH = 4\n\nconst HEADER_LENGTH = CODE_LENGTH + LEN_LENGTH\n\nexport type Packet = {\n  code: number\n  packet: Buffer\n}\n\nconst emptyBuffer = Buffer.allocUnsafe(0)\n\ntype StreamOptions = TransformOptions & {\n  mode: Mode\n}\n\nconst enum MessageCodes {\n  DataRow = 0x44, // D\n  ParseComplete = 0x31, // 1\n  BindComplete = 0x32, // 2\n  CloseComplete = 0x33, // 3\n  CommandComplete = 0x43, // C\n  ReadyForQuery = 0x5a, // Z\n  NoData = 0x6e, // n\n  NotificationResponse = 0x41, // A\n  AuthenticationResponse = 0x52, // R\n  ParameterStatus = 0x53, // S\n  BackendKeyData = 0x4b, // K\n  ErrorMessage = 0x45, // E\n  NoticeMessage = 0x4e, // N\n  RowDescriptionMessage = 0x54, // T\n  ParameterDescriptionMessage = 0x74, // t\n  PortalSuspended = 0x73, // s\n  ReplicationStart = 0x57, // W\n  EmptyQuery = 0x49, // I\n  CopyIn = 0x47, // G\n  CopyOut = 0x48, // H\n  CopyDone = 0x63, // c\n  CopyData = 0x64, // d\n}\n\nexport type MessageCallback = (msg: BackendMessage) => void\n\nexport class Parser {\n  private buffer: Buffer = emptyBuffer\n  private bufferLength: number = 0\n  private bufferOffset: number = 0\n  private reader = new BufferReader()\n  private mode: Mode\n\n  constructor(opts?: StreamOptions) {\n    if (opts?.mode === 'binary') {\n      throw new Error('Binary mode not supported yet')\n    }\n    this.mode = opts?.mode || 'text'\n  }\n\n  public parse(buffer: Buffer, callback: MessageCallback) {\n    this.mergeBuffer(buffer)\n    const bufferFullLength = this.bufferOffset + this.bufferLength\n    let offset = this.bufferOffset\n    while (offset + HEADER_LENGTH <= bufferFullLength) {\n      // code is 1 byte long - it identifies the message type\n      const code = this.buffer[offset]\n      // length is 1 Uint32BE - it is the length of the message EXCLUDING the code\n      const length = this.buffer.readUInt32BE(offset + CODE_LENGTH)\n      const fullMessageLength = CODE_LENGTH + length\n      if (fullMessageLength + offset <= bufferFullLength) {\n        const message = this.handlePacket(offset + HEADER_LENGTH, code, length, this.buffer)\n        callback(message)\n        offset += fullMessageLength\n      } else {\n        break\n      }\n    }\n    if (offset === bufferFullLength) {\n      // No more use for the buffer\n      this.buffer = emptyBuffer\n      this.bufferLength = 0\n      this.bufferOffset = 0\n    } else {\n      // Adjust the cursors of remainingBuffer\n      this.bufferLength = bufferFullLength - offset\n      this.bufferOffset = offset\n    }\n  }\n\n  private mergeBuffer(buffer: Buffer): void {\n    if (this.bufferLength > 0) {\n      const newLength = this.bufferLength + buffer.byteLength\n      const newFullLength = newLength + this.bufferOffset\n      if (newFullLength > this.buffer.byteLength) {\n        // We can't concat the new buffer with the remaining one\n        let newBuffer: Buffer\n        if (newLength <= this.buffer.byteLength && this.bufferOffset >= this.bufferLength) {\n          // We can move the relevant part to the beginning of the buffer instead of allocating a new buffer\n          newBuffer = this.buffer\n        } else {\n          // Allocate a new larger buffer\n          let newBufferLength = this.buffer.byteLength * 2\n          while (newLength >= newBufferLength) {\n            newBufferLength *= 2\n          }\n          newBuffer = Buffer.allocUnsafe(newBufferLength)\n        }\n        // Move the remaining buffer to the new one\n        this.buffer.copy(newBuffer, 0, this.bufferOffset, this.bufferOffset + this.bufferLength)\n        this.buffer = newBuffer\n        this.bufferOffset = 0\n      }\n      // Concat the new buffer with the remaining one\n      buffer.copy(this.buffer, this.bufferOffset + this.bufferLength)\n      this.bufferLength = newLength\n    } else {\n      this.buffer = buffer\n      this.bufferOffset = 0\n      this.bufferLength = buffer.byteLength\n    }\n  }\n\n  private handlePacket(offset: number, code: number, length: number, bytes: Buffer): BackendMessage {\n    switch (code) {\n      case MessageCodes.BindComplete:\n        return bindComplete\n      case MessageCodes.ParseComplete:\n        return parseComplete\n      case MessageCodes.CloseComplete:\n        return closeComplete\n      case MessageCodes.NoData:\n        return noData\n      case MessageCodes.PortalSuspended:\n        return portalSuspended\n      case MessageCodes.CopyDone:\n        return copyDone\n      case MessageCodes.ReplicationStart:\n        return replicationStart\n      case MessageCodes.EmptyQuery:\n        return emptyQuery\n      case MessageCodes.DataRow:\n        return this.parseDataRowMessage(offset, length, bytes)\n      case MessageCodes.CommandComplete:\n        return this.parseCommandCompleteMessage(offset, length, bytes)\n      case MessageCodes.ReadyForQuery:\n        return this.parseReadyForQueryMessage(offset, length, bytes)\n      case MessageCodes.NotificationResponse:\n        return this.parseNotificationMessage(offset, length, bytes)\n      case MessageCodes.AuthenticationResponse:\n        return this.parseAuthenticationResponse(offset, length, bytes)\n      case MessageCodes.ParameterStatus:\n        return this.parseParameterStatusMessage(offset, length, bytes)\n      case MessageCodes.BackendKeyData:\n        return this.parseBackendKeyData(offset, length, bytes)\n      case MessageCodes.ErrorMessage:\n        return this.parseErrorMessage(offset, length, bytes, 'error')\n      case MessageCodes.NoticeMessage:\n        return this.parseErrorMessage(offset, length, bytes, 'notice')\n      case MessageCodes.RowDescriptionMessage:\n        return this.parseRowDescriptionMessage(offset, length, bytes)\n      case MessageCodes.ParameterDescriptionMessage:\n        return this.parseParameterDescriptionMessage(offset, length, bytes)\n      case MessageCodes.CopyIn:\n        return this.parseCopyInMessage(offset, length, bytes)\n      case MessageCodes.CopyOut:\n        return this.parseCopyOutMessage(offset, length, bytes)\n      case MessageCodes.CopyData:\n        return this.parseCopyData(offset, length, bytes)\n      default:\n        return new DatabaseError('received invalid response: ' + code.toString(16), length, 'error')\n    }\n  }\n\n  private parseReadyForQueryMessage(offset: number, length: number, bytes: Buffer) {\n    this.reader.setBuffer(offset, bytes)\n    const status = this.reader.string(1)\n    return new ReadyForQueryMessage(length, status)\n  }\n\n  private parseCommandCompleteMessage(offset: number, length: number, bytes: Buffer) {\n    this.reader.setBuffer(offset, bytes)\n    const text = this.reader.cstring()\n    return new CommandCompleteMessage(length, text)\n  }\n\n  private parseCopyData(offset: number, length: number, bytes: Buffer) {\n    const chunk = bytes.slice(offset, offset + (length - 4))\n    return new CopyDataMessage(length, chunk)\n  }\n\n  private parseCopyInMessage(offset: number, length: number, bytes: Buffer) {\n    return this.parseCopyMessage(offset, length, bytes, 'copyInResponse')\n  }\n\n  private parseCopyOutMessage(offset: number, length: number, bytes: Buffer) {\n    return this.parseCopyMessage(offset, length, bytes, 'copyOutResponse')\n  }\n\n  private parseCopyMessage(offset: number, length: number, bytes: Buffer, messageName: MessageName) {\n    this.reader.setBuffer(offset, bytes)\n    const isBinary = this.reader.byte() !== 0\n    const columnCount = this.reader.int16()\n    const message = new CopyResponse(length, messageName, isBinary, columnCount)\n    for (let i = 0; i < columnCount; i++) {\n      message.columnTypes[i] = this.reader.int16()\n    }\n    return message\n  }\n\n  private parseNotificationMessage(offset: number, length: number, bytes: Buffer) {\n    this.reader.setBuffer(offset, bytes)\n    const processId = this.reader.int32()\n    const channel = this.reader.cstring()\n    const payload = this.reader.cstring()\n    return new NotificationResponseMessage(length, processId, channel, payload)\n  }\n\n  private parseRowDescriptionMessage(offset: number, length: number, bytes: Buffer) {\n    this.reader.setBuffer(offset, bytes)\n    const fieldCount = this.reader.int16()\n    const message = new RowDescriptionMessage(length, fieldCount)\n    for (let i = 0; i < fieldCount; i++) {\n      message.fields[i] = this.parseField()\n    }\n    return message\n  }\n\n  private parseField(): Field {\n    const name = this.reader.cstring()\n    const tableID = this.reader.uint32()\n    const columnID = this.reader.int16()\n    const dataTypeID = this.reader.uint32()\n    const dataTypeSize = this.reader.int16()\n    const dataTypeModifier = this.reader.int32()\n    const mode = this.reader.int16() === 0 ? 'text' : 'binary'\n    return new Field(name, tableID, columnID, dataTypeID, dataTypeSize, dataTypeModifier, mode)\n  }\n\n  private parseParameterDescriptionMessage(offset: number, length: number, bytes: Buffer) {\n    this.reader.setBuffer(offset, bytes)\n    const parameterCount = this.reader.int16()\n    const message = new ParameterDescriptionMessage(length, parameterCount)\n    for (let i = 0; i < parameterCount; i++) {\n      message.dataTypeIDs[i] = this.reader.int32()\n    }\n    return message\n  }\n\n  private parseDataRowMessage(offset: number, length: number, bytes: Buffer) {\n    this.reader.setBuffer(offset, bytes)\n    const fieldCount = this.reader.int16()\n    const fields: any[] = new Array(fieldCount)\n    for (let i = 0; i < fieldCount; i++) {\n      const len = this.reader.int32()\n      // a -1 for length means the value of the field is null\n      fields[i] = len === -1 ? null : this.reader.string(len)\n    }\n    return new DataRowMessage(length, fields)\n  }\n\n  private parseParameterStatusMessage(offset: number, length: number, bytes: Buffer) {\n    this.reader.setBuffer(offset, bytes)\n    const name = this.reader.cstring()\n    const value = this.reader.cstring()\n    return new ParameterStatusMessage(length, name, value)\n  }\n\n  private parseBackendKeyData(offset: number, length: number, bytes: Buffer) {\n    this.reader.setBuffer(offset, bytes)\n    const processID = this.reader.int32()\n    const secretKey = this.reader.int32()\n    return new BackendKeyDataMessage(length, processID, secretKey)\n  }\n\n  public parseAuthenticationResponse(offset: number, length: number, bytes: Buffer) {\n    this.reader.setBuffer(offset, bytes)\n    const code = this.reader.int32()\n    // TODO(bmc): maybe better types here\n    const message: BackendMessage & any = {\n      name: 'authenticationOk',\n      length,\n    }\n\n    switch (code) {\n      case 0: // AuthenticationOk\n        break\n      case 3: // AuthenticationCleartextPassword\n        if (message.length === 8) {\n          message.name = 'authenticationCleartextPassword'\n        }\n        break\n      case 5: // AuthenticationMD5Password\n        if (message.length === 12) {\n          message.name = 'authenticationMD5Password'\n          const salt = this.reader.bytes(4)\n          return new AuthenticationMD5Password(length, salt)\n        }\n        break\n      case 10: // AuthenticationSASL\n        {\n          message.name = 'authenticationSASL'\n          message.mechanisms = []\n          let mechanism: string\n          do {\n            mechanism = this.reader.cstring()\n            if (mechanism) {\n              message.mechanisms.push(mechanism)\n            }\n          } while (mechanism)\n        }\n        break\n      case 11: // AuthenticationSASLContinue\n        message.name = 'authenticationSASLContinue'\n        message.data = this.reader.string(length - 8)\n        break\n      case 12: // AuthenticationSASLFinal\n        message.name = 'authenticationSASLFinal'\n        message.data = this.reader.string(length - 8)\n        break\n      default:\n        throw new Error('Unknown authenticationOk message type ' + code)\n    }\n    return message\n  }\n\n  private parseErrorMessage(offset: number, length: number, bytes: Buffer, name: MessageName) {\n    this.reader.setBuffer(offset, bytes)\n    const fields: Record<string, string> = {}\n    let fieldType = this.reader.string(1)\n    while (fieldType !== '\\0') {\n      fields[fieldType] = this.reader.cstring()\n      fieldType = this.reader.string(1)\n    }\n\n    const messageValue = fields.M\n\n    const message =\n      name === 'notice' ? new NoticeMessage(length, messageValue) : new DatabaseError(messageValue, length, name)\n\n    message.severity = fields.S\n    message.code = fields.C\n    message.detail = fields.D\n    message.hint = fields.H\n    message.position = fields.P\n    message.internalPosition = fields.p\n    message.internalQuery = fields.q\n    message.where = fields.W\n    message.schema = fields.s\n    message.table = fields.t\n    message.column = fields.c\n    message.dataType = fields.d\n    message.constraint = fields.n\n    message.file = fields.F\n    message.line = fields.L\n    message.routine = fields.R\n    return message\n  }\n}\n", "import { DatabaseError } from './messages'\nimport { serialize } from './serializer'\nimport { Parser, MessageCallback } from './parser'\n\nexport function parse(stream: NodeJS.ReadableStream, callback: MessageCallback): Promise<void> {\n  const parser = new Parser()\n  stream.on('data', (buffer: Buffer) => parser.parse(buffer, callback))\n  return new Promise((resolve) => stream.on('end', () => resolve()))\n}\n\nexport { serialize, DatabaseError }\n", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"net\" has been externalized for browser compatibility. Cannot access \"net.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"tls\" has been externalized for browser compatibility. Cannot access \"tls.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "// This is an empty module that is served up when outside of a workerd environment\n// See the `exports` field in package.json\nexport default {}\n", "const { getStream, getSecureStream } = getStreamFuncs()\n\nmodule.exports = {\n  /**\n   * Get a socket stream compatible with the current runtime environment.\n   * @returns {Duplex}\n   */\n  getStream,\n  /**\n   * Get a TLS secured socket, compatible with the current environment,\n   * using the socket and other settings given in `options`.\n   * @returns {Duplex}\n   */\n  getSecureStream,\n}\n\n/**\n * The stream functions that work in Node.js\n */\nfunction getNodejsStreamFuncs() {\n  function getStream(ssl) {\n    const net = require('net')\n    return new net.Socket()\n  }\n\n  function getSecureStream(options) {\n    const tls = require('tls')\n    return tls.connect(options)\n  }\n  return {\n    getStream,\n    getSecureStream,\n  }\n}\n\n/**\n * The stream functions that work in Cloudflare Workers\n */\nfunction getCloudflareStreamFuncs() {\n  function getStream(ssl) {\n    const { CloudflareSocket } = require('pg-cloudflare')\n    return new CloudflareSocket(ssl)\n  }\n\n  function getSecureStream(options) {\n    options.socket.startTls(options)\n    return options.socket\n  }\n  return {\n    getStream,\n    getSecureStream,\n  }\n}\n\n/**\n * Are we running in a Cloudflare Worker?\n *\n * @returns true if the code is currently running inside a Cloudflare Worker.\n */\nfunction isCloudflareRuntime() {\n  // Since 2022-03-21 the `global_navigator` compatibility flag is on for Cloudflare Workers\n  // which means that `navigator.userAgent` will be defined.\n  // eslint-disable-next-line no-undef\n  if (typeof navigator === 'object' && navigator !== null && typeof navigator.userAgent === 'string') {\n    // eslint-disable-next-line no-undef\n    return navigator.userAgent === 'Cloudflare-Workers'\n  }\n  // In case `navigator` or `navigator.userAgent` is not defined then try a more sneaky approach\n  if (typeof Response === 'function') {\n    const resp = new Response(null, { cf: { thing: true } })\n    if (typeof resp.cf === 'object' && resp.cf !== null && resp.cf.thing) {\n      return true\n    }\n  }\n  return false\n}\n\nfunction getStreamFuncs() {\n  if (isCloudflareRuntime()) {\n    return getCloudflareStreamFuncs()\n  }\n  return getNodejsStreamFuncs()\n}\n", "'use strict'\n\nconst EventEmitter = require('events').EventEmitter\n\nconst { parse, serialize } = require('pg-protocol')\nconst { getStream, getSecureStream } = require('./stream')\n\nconst flushBuffer = serialize.flush()\nconst syncBuffer = serialize.sync()\nconst endBuffer = serialize.end()\n\n// TODO(bmc) support binary mode at some point\nclass Connection extends EventEmitter {\n  constructor(config) {\n    super()\n    config = config || {}\n\n    this.stream = config.stream || getStream(config.ssl)\n    if (typeof this.stream === 'function') {\n      this.stream = this.stream(config)\n    }\n\n    this._keepAlive = config.keepAlive\n    this._keepAliveInitialDelayMillis = config.keepAliveInitialDelayMillis\n    this.lastBuffer = false\n    this.parsedStatements = {}\n    this.ssl = config.ssl || false\n    this._ending = false\n    this._emitMessage = false\n    const self = this\n    this.on('newListener', function (eventName) {\n      if (eventName === 'message') {\n        self._emitMessage = true\n      }\n    })\n  }\n\n  connect(port, host) {\n    const self = this\n\n    this._connecting = true\n    this.stream.setNoDelay(true)\n    this.stream.connect(port, host)\n\n    this.stream.once('connect', function () {\n      if (self._keepAlive) {\n        self.stream.setKeepAlive(true, self._keepAliveInitialDelayMillis)\n      }\n      self.emit('connect')\n    })\n\n    const reportStreamError = function (error) {\n      // errors about disconnections should be ignored during disconnect\n      if (self._ending && (error.code === 'ECONNRESET' || error.code === 'EPIPE')) {\n        return\n      }\n      self.emit('error', error)\n    }\n    this.stream.on('error', reportStreamError)\n\n    this.stream.on('close', function () {\n      self.emit('end')\n    })\n\n    if (!this.ssl) {\n      return this.attachListeners(this.stream)\n    }\n\n    this.stream.once('data', function (buffer) {\n      const responseCode = buffer.toString('utf8')\n      switch (responseCode) {\n        case 'S': // Server supports SSL connections, continue with a secure connection\n          break\n        case 'N': // Server does not support SSL connections\n          self.stream.end()\n          return self.emit('error', new Error('The server does not support SSL connections'))\n        default:\n          // Any other response byte, including 'E' (ErrorResponse) indicating a server error\n          self.stream.end()\n          return self.emit('error', new Error('There was an error establishing an SSL connection'))\n      }\n      const options = {\n        socket: self.stream,\n      }\n\n      if (self.ssl !== true) {\n        Object.assign(options, self.ssl)\n\n        if ('key' in self.ssl) {\n          options.key = self.ssl.key\n        }\n      }\n\n      const net = require('net')\n      if (net.isIP && net.isIP(host) === 0) {\n        options.servername = host\n      }\n      try {\n        self.stream = getSecureStream(options)\n      } catch (err) {\n        return self.emit('error', err)\n      }\n      self.attachListeners(self.stream)\n      self.stream.on('error', reportStreamError)\n\n      self.emit('sslconnect')\n    })\n  }\n\n  attachListeners(stream) {\n    parse(stream, (msg) => {\n      const eventName = msg.name === 'error' ? 'errorMessage' : msg.name\n      if (this._emitMessage) {\n        this.emit('message', msg)\n      }\n      this.emit(eventName, msg)\n    })\n  }\n\n  requestSsl() {\n    this.stream.write(serialize.requestSsl())\n  }\n\n  startup(config) {\n    this.stream.write(serialize.startup(config))\n  }\n\n  cancel(processID, secretKey) {\n    this._send(serialize.cancel(processID, secretKey))\n  }\n\n  password(password) {\n    this._send(serialize.password(password))\n  }\n\n  sendSASLInitialResponseMessage(mechanism, initialResponse) {\n    this._send(serialize.sendSASLInitialResponseMessage(mechanism, initialResponse))\n  }\n\n  sendSCRAMClientFinalMessage(additionalData) {\n    this._send(serialize.sendSCRAMClientFinalMessage(additionalData))\n  }\n\n  _send(buffer) {\n    if (!this.stream.writable) {\n      return false\n    }\n    return this.stream.write(buffer)\n  }\n\n  query(text) {\n    this._send(serialize.query(text))\n  }\n\n  // send parse message\n  parse(query) {\n    this._send(serialize.parse(query))\n  }\n\n  // send bind message\n  bind(config) {\n    this._send(serialize.bind(config))\n  }\n\n  // send execute message\n  execute(config) {\n    this._send(serialize.execute(config))\n  }\n\n  flush() {\n    if (this.stream.writable) {\n      this.stream.write(flushBuffer)\n    }\n  }\n\n  sync() {\n    this._ending = true\n    this._send(syncBuffer)\n  }\n\n  ref() {\n    this.stream.ref()\n  }\n\n  unref() {\n    this.stream.unref()\n  }\n\n  end() {\n    // 0x58 = 'X'\n    this._ending = true\n    if (!this._connecting || !this.stream.writable) {\n      this.stream.end()\n      return\n    }\n    return this.stream.write(endBuffer, () => {\n      this.stream.end()\n    })\n  }\n\n  close(msg) {\n    this._send(serialize.close(msg))\n  }\n\n  describe(msg) {\n    this._send(serialize.describe(msg))\n  }\n\n  sendCopyFromChunk(chunk) {\n    this._send(serialize.copyData(chunk))\n  }\n\n  endCopyFrom() {\n    this._send(serialize.copyDone())\n  }\n\n  sendCopyFail(msg) {\n    this._send(serialize.copyFail(msg))\n  }\n}\n\nmodule.exports = Connection\n", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"path\" has been externalized for browser compatibility. Cannot access \"path.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"stream\" has been externalized for browser compatibility. Cannot access \"stream.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"string_decoder\" has been externalized for browser compatibility. Cannot access \"string_decoder.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "/*\nCopyright (c) 2014-2021, <PERSON> <<EMAIL>>\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n*/\n\n'use strict'\n\nconst { Transform } = require('stream')\nconst { StringDecoder } = require('string_decoder')\nconst kLast = Symbol('last')\nconst kDecoder = Symbol('decoder')\n\nfunction transform (chunk, enc, cb) {\n  let list\n  if (this.overflow) { // Line buffer is full. Skip to start of next line.\n    const buf = this[kDecoder].write(chunk)\n    list = buf.split(this.matcher)\n\n    if (list.length === 1) return cb() // Line ending not found. Discard entire chunk.\n\n    // Line ending found. Discard trailing fragment of previous line and reset overflow state.\n    list.shift()\n    this.overflow = false\n  } else {\n    this[kLast] += this[kDecoder].write(chunk)\n    list = this[kLast].split(this.matcher)\n  }\n\n  this[kLast] = list.pop()\n\n  for (let i = 0; i < list.length; i++) {\n    try {\n      push(this, this.mapper(list[i]))\n    } catch (error) {\n      return cb(error)\n    }\n  }\n\n  this.overflow = this[kLast].length > this.maxLength\n  if (this.overflow && !this.skipOverflow) {\n    cb(new Error('maximum buffer reached'))\n    return\n  }\n\n  cb()\n}\n\nfunction flush (cb) {\n  // forward any gibberish left in there\n  this[kLast] += this[kDecoder].end()\n\n  if (this[kLast]) {\n    try {\n      push(this, this.mapper(this[kLast]))\n    } catch (error) {\n      return cb(error)\n    }\n  }\n\n  cb()\n}\n\nfunction push (self, val) {\n  if (val !== undefined) {\n    self.push(val)\n  }\n}\n\nfunction noop (incoming) {\n  return incoming\n}\n\nfunction split (matcher, mapper, options) {\n  // Set defaults for any arguments not supplied.\n  matcher = matcher || /\\r?\\n/\n  mapper = mapper || noop\n  options = options || {}\n\n  // Test arguments explicitly.\n  switch (arguments.length) {\n    case 1:\n      // If mapper is only argument.\n      if (typeof matcher === 'function') {\n        mapper = matcher\n        matcher = /\\r?\\n/\n      // If options is only argument.\n      } else if (typeof matcher === 'object' && !(matcher instanceof RegExp) && !matcher[Symbol.split]) {\n        options = matcher\n        matcher = /\\r?\\n/\n      }\n      break\n\n    case 2:\n      // If mapper and options are arguments.\n      if (typeof matcher === 'function') {\n        options = mapper\n        mapper = matcher\n        matcher = /\\r?\\n/\n      // If matcher and options are arguments.\n      } else if (typeof mapper === 'object') {\n        options = mapper\n        mapper = noop\n      }\n  }\n\n  options = Object.assign({}, options)\n  options.autoDestroy = true\n  options.transform = transform\n  options.flush = flush\n  options.readableObjectMode = true\n\n  const stream = new Transform(options)\n\n  stream[kLast] = ''\n  stream[kDecoder] = new StringDecoder('utf8')\n  stream.matcher = matcher\n  stream.mapper = mapper\n  stream.maxLength = options.maxLength\n  stream.skipOverflow = options.skipOverflow || false\n  stream.overflow = false\n  stream._destroy = function (err, cb) {\n    // Weird Node v12 bug that we need to work around\n    this._writableState.errorEmitted = false\n    cb(err)\n  }\n\n  return stream\n}\n\nmodule.exports = split\n", "'use strict';\n\nvar path = require('path')\n  , Stream = require('stream').Stream\n  , split = require('split2')\n  , util = require('util')\n  , defaultPort = 5432\n  , isWin = (process.platform === 'win32')\n  , warnStream = process.stderr\n;\n\n\nvar S_IRWXG = 56     //    00070(8)\n  , S_IRWXO = 7      //    00007(8)\n  , S_IFMT  = 61440  // 00170000(8)\n  , S_IFREG = 32768  //  0100000(8)\n;\nfunction isRegFile(mode) {\n    return ((mode & S_IFMT) == S_IFREG);\n}\n\nvar fieldNames = [ 'host', 'port', 'database', 'user', 'password' ];\nvar nrOfFields = fieldNames.length;\nvar passKey = fieldNames[ nrOfFields -1 ];\n\n\nfunction warn() {\n    var isWritable = (\n        warnStream instanceof Stream &&\n          true === warnStream.writable\n    );\n\n    if (isWritable) {\n        var args = Array.prototype.slice.call(arguments).concat(\"\\n\");\n        warnStream.write( util.format.apply(util, args) );\n    }\n}\n\n\nObject.defineProperty(module.exports, 'isWin', {\n    get : function() {\n        return isWin;\n    } ,\n    set : function(val) {\n        isWin = val;\n    }\n});\n\n\nmodule.exports.warnTo = function(stream) {\n    var old = warnStream;\n    warnStream = stream;\n    return old;\n};\n\nmodule.exports.getFileName = function(rawEnv){\n    var env = rawEnv || process.env;\n    var file = env.PGPASSFILE || (\n        isWin ?\n          path.join( env.APPDATA || './' , 'postgresql', 'pgpass.conf' ) :\n          path.join( env.HOME || './', '.pgpass' )\n    );\n    return file;\n};\n\nmodule.exports.usePgPass = function(stats, fname) {\n    if (Object.prototype.hasOwnProperty.call(process.env, 'PGPASSWORD')) {\n        return false;\n    }\n\n    if (isWin) {\n        return true;\n    }\n\n    fname = fname || '<unkn>';\n\n    if (! isRegFile(stats.mode)) {\n        warn('WARNING: password file \"%s\" is not a plain file', fname);\n        return false;\n    }\n\n    if (stats.mode & (S_IRWXG | S_IRWXO)) {\n        /* If password file is insecure, alert the user and ignore it. */\n        warn('WARNING: password file \"%s\" has group or world access; permissions should be u=rw (0600) or less', fname);\n        return false;\n    }\n\n    return true;\n};\n\n\nvar matcher = module.exports.match = function(connInfo, entry) {\n    return fieldNames.slice(0, -1).reduce(function(prev, field, idx){\n        if (idx == 1) {\n            // the port\n            if ( Number( connInfo[field] || defaultPort ) === Number( entry[field] ) ) {\n                return prev && true;\n            }\n        }\n        return prev && (\n            entry[field] === '*' ||\n              entry[field] === connInfo[field]\n        );\n    }, true);\n};\n\n\nmodule.exports.getPassword = function(connInfo, stream, cb) {\n    var pass;\n    var lineStream = stream.pipe(split());\n\n    function onLine(line) {\n        var entry = parseLine(line);\n        if (entry && isValidEntry(entry) && matcher(connInfo, entry)) {\n            pass = entry[passKey];\n            lineStream.end(); // -> calls onEnd(), but pass is set now\n        }\n    }\n\n    var onEnd = function() {\n        stream.destroy();\n        cb(pass);\n    };\n\n    var onErr = function(err) {\n        stream.destroy();\n        warn('WARNING: error on reading file: %s', err);\n        cb(undefined);\n    };\n\n    stream.on('error', onErr);\n    lineStream\n        .on('data', onLine)\n        .on('end', onEnd)\n        .on('error', onErr)\n    ;\n\n};\n\n\nvar parseLine = module.exports.parseLine = function(line) {\n    if (line.length < 11 || line.match(/^\\s+#/)) {\n        return null;\n    }\n\n    var curChar = '';\n    var prevChar = '';\n    var fieldIdx = 0;\n    var startIdx = 0;\n    var endIdx = 0;\n    var obj = {};\n    var isLastField = false;\n    var addToObj = function(idx, i0, i1) {\n        var field = line.substring(i0, i1);\n\n        if (! Object.hasOwnProperty.call(process.env, 'PGPASS_NO_DEESCAPE')) {\n            field = field.replace(/\\\\([:\\\\])/g, '$1');\n        }\n\n        obj[ fieldNames[idx] ] = field;\n    };\n\n    for (var i = 0 ; i < line.length-1 ; i += 1) {\n        curChar = line.charAt(i+1);\n        prevChar = line.charAt(i);\n\n        isLastField = (fieldIdx == nrOfFields-1);\n\n        if (isLastField) {\n            addToObj(fieldIdx, startIdx);\n            break;\n        }\n\n        if (i >= 0 && curChar == ':' && prevChar !== '\\\\') {\n            addToObj(fieldIdx, startIdx, i+1);\n\n            startIdx = i+2;\n            fieldIdx += 1;\n        }\n    }\n\n    obj = ( Object.keys(obj).length === nrOfFields ) ? obj : null;\n\n    return obj;\n};\n\n\nvar isValidEntry = module.exports.isValidEntry = function(entry){\n    var rules = {\n        // host\n        0 : function(x){\n            return x.length > 0;\n        } ,\n        // port\n        1 : function(x){\n            if (x === '*') {\n                return true;\n            }\n            x = Number(x);\n            return (\n                isFinite(x) &&\n                  x > 0 &&\n                  x < 9007199254740992 &&\n                  Math.floor(x) === x\n            );\n        } ,\n        // database\n        2 : function(x){\n            return x.length > 0;\n        } ,\n        // username\n        3 : function(x){\n            return x.length > 0;\n        } ,\n        // password\n        4 : function(x){\n            return x.length > 0;\n        }\n    };\n\n    for (var idx = 0 ; idx < fieldNames.length ; idx += 1) {\n        var rule = rules[idx];\n        var value = entry[ fieldNames[idx] ] || '';\n\n        var res = rule(value);\n        if (!res) {\n            return false;\n        }\n    }\n\n    return true;\n};\n\n", "'use strict';\n\nvar path = require('path')\n  , fs = require('fs')\n  , helper = require('./helper.js')\n;\n\n\nmodule.exports = function(connInfo, cb) {\n    var file = helper.getFileName();\n    \n    fs.stat(file, function(err, stat){\n        if (err || !helper.usePgPass(stat, file)) {\n            return cb(undefined);\n        }\n\n        var st = fs.createReadStream(file);\n\n        helper.getPassword(connInfo, st, cb);\n    });\n};\n\nmodule.exports.warnTo = helper.warnTo;\n", "'use strict'\n\nconst EventEmitter = require('events').EventEmitter\nconst utils = require('./utils')\nconst sasl = require('./crypto/sasl')\nconst TypeOverrides = require('./type-overrides')\n\nconst ConnectionParameters = require('./connection-parameters')\nconst Query = require('./query')\nconst defaults = require('./defaults')\nconst Connection = require('./connection')\nconst crypto = require('./crypto/utils')\n\nclass Client extends EventEmitter {\n  constructor(config) {\n    super()\n\n    this.connectionParameters = new ConnectionParameters(config)\n    this.user = this.connectionParameters.user\n    this.database = this.connectionParameters.database\n    this.port = this.connectionParameters.port\n    this.host = this.connectionParameters.host\n\n    // \"hiding\" the password so it doesn't show up in stack traces\n    // or if the client is console.logged\n    Object.defineProperty(this, 'password', {\n      configurable: true,\n      enumerable: false,\n      writable: true,\n      value: this.connectionParameters.password,\n    })\n\n    this.replication = this.connectionParameters.replication\n\n    const c = config || {}\n\n    this._Promise = c.Promise || global.Promise\n    this._types = new TypeOverrides(c.types)\n    this._ending = false\n    this._ended = false\n    this._connecting = false\n    this._connected = false\n    this._connectionError = false\n    this._queryable = true\n\n    this.enableChannelBinding = Boolean(c.enableChannelBinding) // set true to use SCRAM-SHA-256-PLUS when offered\n    this.connection =\n      c.connection ||\n      new Connection({\n        stream: c.stream,\n        ssl: this.connectionParameters.ssl,\n        keepAlive: c.keepAlive || false,\n        keepAliveInitialDelayMillis: c.keepAliveInitialDelayMillis || 0,\n        encoding: this.connectionParameters.client_encoding || 'utf8',\n      })\n    this.queryQueue = []\n    this.binary = c.binary || defaults.binary\n    this.processID = null\n    this.secretKey = null\n    this.ssl = this.connectionParameters.ssl || false\n    // As with Password, make SSL->Key (the private key) non-enumerable.\n    // It won't show up in stack traces\n    // or if the client is console.logged\n    if (this.ssl && this.ssl.key) {\n      Object.defineProperty(this.ssl, 'key', {\n        enumerable: false,\n      })\n    }\n\n    this._connectionTimeoutMillis = c.connectionTimeoutMillis || 0\n  }\n\n  _errorAllQueries(err) {\n    const enqueueError = (query) => {\n      process.nextTick(() => {\n        query.handleError(err, this.connection)\n      })\n    }\n\n    if (this.activeQuery) {\n      enqueueError(this.activeQuery)\n      this.activeQuery = null\n    }\n\n    this.queryQueue.forEach(enqueueError)\n    this.queryQueue.length = 0\n  }\n\n  _connect(callback) {\n    const self = this\n    const con = this.connection\n    this._connectionCallback = callback\n\n    if (this._connecting || this._connected) {\n      const err = new Error('Client has already been connected. You cannot reuse a client.')\n      process.nextTick(() => {\n        callback(err)\n      })\n      return\n    }\n    this._connecting = true\n\n    if (this._connectionTimeoutMillis > 0) {\n      this.connectionTimeoutHandle = setTimeout(() => {\n        con._ending = true\n        con.stream.destroy(new Error('timeout expired'))\n      }, this._connectionTimeoutMillis)\n\n      if (this.connectionTimeoutHandle.unref) {\n        this.connectionTimeoutHandle.unref()\n      }\n    }\n\n    if (this.host && this.host.indexOf('/') === 0) {\n      con.connect(this.host + '/.s.PGSQL.' + this.port)\n    } else {\n      con.connect(this.port, this.host)\n    }\n\n    // once connection is established send startup message\n    con.on('connect', function () {\n      if (self.ssl) {\n        con.requestSsl()\n      } else {\n        con.startup(self.getStartupConf())\n      }\n    })\n\n    con.on('sslconnect', function () {\n      con.startup(self.getStartupConf())\n    })\n\n    this._attachListeners(con)\n\n    con.once('end', () => {\n      const error = this._ending ? new Error('Connection terminated') : new Error('Connection terminated unexpectedly')\n\n      clearTimeout(this.connectionTimeoutHandle)\n      this._errorAllQueries(error)\n      this._ended = true\n\n      if (!this._ending) {\n        // if the connection is ended without us calling .end()\n        // on this client then we have an unexpected disconnection\n        // treat this as an error unless we've already emitted an error\n        // during connection.\n        if (this._connecting && !this._connectionError) {\n          if (this._connectionCallback) {\n            this._connectionCallback(error)\n          } else {\n            this._handleErrorEvent(error)\n          }\n        } else if (!this._connectionError) {\n          this._handleErrorEvent(error)\n        }\n      }\n\n      process.nextTick(() => {\n        this.emit('end')\n      })\n    })\n  }\n\n  connect(callback) {\n    if (callback) {\n      this._connect(callback)\n      return\n    }\n\n    return new this._Promise((resolve, reject) => {\n      this._connect((error) => {\n        if (error) {\n          reject(error)\n        } else {\n          resolve()\n        }\n      })\n    })\n  }\n\n  _attachListeners(con) {\n    // password request handling\n    con.on('authenticationCleartextPassword', this._handleAuthCleartextPassword.bind(this))\n    // password request handling\n    con.on('authenticationMD5Password', this._handleAuthMD5Password.bind(this))\n    // password request handling (SASL)\n    con.on('authenticationSASL', this._handleAuthSASL.bind(this))\n    con.on('authenticationSASLContinue', this._handleAuthSASLContinue.bind(this))\n    con.on('authenticationSASLFinal', this._handleAuthSASLFinal.bind(this))\n    con.on('backendKeyData', this._handleBackendKeyData.bind(this))\n    con.on('error', this._handleErrorEvent.bind(this))\n    con.on('errorMessage', this._handleErrorMessage.bind(this))\n    con.on('readyForQuery', this._handleReadyForQuery.bind(this))\n    con.on('notice', this._handleNotice.bind(this))\n    con.on('rowDescription', this._handleRowDescription.bind(this))\n    con.on('dataRow', this._handleDataRow.bind(this))\n    con.on('portalSuspended', this._handlePortalSuspended.bind(this))\n    con.on('emptyQuery', this._handleEmptyQuery.bind(this))\n    con.on('commandComplete', this._handleCommandComplete.bind(this))\n    con.on('parseComplete', this._handleParseComplete.bind(this))\n    con.on('copyInResponse', this._handleCopyInResponse.bind(this))\n    con.on('copyData', this._handleCopyData.bind(this))\n    con.on('notification', this._handleNotification.bind(this))\n  }\n\n  // TODO(bmc): deprecate pgpass \"built in\" integration since this.password can be a function\n  // it can be supplied by the user if required - this is a breaking change!\n  _checkPgPass(cb) {\n    const con = this.connection\n    if (typeof this.password === 'function') {\n      this._Promise\n        .resolve()\n        .then(() => this.password())\n        .then((pass) => {\n          if (pass !== undefined) {\n            if (typeof pass !== 'string') {\n              con.emit('error', new TypeError('Password must be a string'))\n              return\n            }\n            this.connectionParameters.password = this.password = pass\n          } else {\n            this.connectionParameters.password = this.password = null\n          }\n          cb()\n        })\n        .catch((err) => {\n          con.emit('error', err)\n        })\n    } else if (this.password !== null) {\n      cb()\n    } else {\n      try {\n        const pgPass = require('pgpass')\n        pgPass(this.connectionParameters, (pass) => {\n          if (undefined !== pass) {\n            this.connectionParameters.password = this.password = pass\n          }\n          cb()\n        })\n      } catch (e) {\n        this.emit('error', e)\n      }\n    }\n  }\n\n  _handleAuthCleartextPassword(msg) {\n    this._checkPgPass(() => {\n      this.connection.password(this.password)\n    })\n  }\n\n  _handleAuthMD5Password(msg) {\n    this._checkPgPass(async () => {\n      try {\n        const hashedPassword = await crypto.postgresMd5PasswordHash(this.user, this.password, msg.salt)\n        this.connection.password(hashedPassword)\n      } catch (e) {\n        this.emit('error', e)\n      }\n    })\n  }\n\n  _handleAuthSASL(msg) {\n    this._checkPgPass(() => {\n      try {\n        this.saslSession = sasl.startSession(msg.mechanisms, this.enableChannelBinding && this.connection.stream)\n        this.connection.sendSASLInitialResponseMessage(this.saslSession.mechanism, this.saslSession.response)\n      } catch (err) {\n        this.connection.emit('error', err)\n      }\n    })\n  }\n\n  async _handleAuthSASLContinue(msg) {\n    try {\n      await sasl.continueSession(\n        this.saslSession,\n        this.password,\n        msg.data,\n        this.enableChannelBinding && this.connection.stream\n      )\n      this.connection.sendSCRAMClientFinalMessage(this.saslSession.response)\n    } catch (err) {\n      this.connection.emit('error', err)\n    }\n  }\n\n  _handleAuthSASLFinal(msg) {\n    try {\n      sasl.finalizeSession(this.saslSession, msg.data)\n      this.saslSession = null\n    } catch (err) {\n      this.connection.emit('error', err)\n    }\n  }\n\n  _handleBackendKeyData(msg) {\n    this.processID = msg.processID\n    this.secretKey = msg.secretKey\n  }\n\n  _handleReadyForQuery(msg) {\n    if (this._connecting) {\n      this._connecting = false\n      this._connected = true\n      clearTimeout(this.connectionTimeoutHandle)\n\n      // process possible callback argument to Client#connect\n      if (this._connectionCallback) {\n        this._connectionCallback(null, this)\n        // remove callback for proper error handling\n        // after the connect event\n        this._connectionCallback = null\n      }\n      this.emit('connect')\n    }\n    const { activeQuery } = this\n    this.activeQuery = null\n    this.readyForQuery = true\n    if (activeQuery) {\n      activeQuery.handleReadyForQuery(this.connection)\n    }\n    this._pulseQueryQueue()\n  }\n\n  // if we receieve an error event or error message\n  // during the connection process we handle it here\n  _handleErrorWhileConnecting(err) {\n    if (this._connectionError) {\n      // TODO(bmc): this is swallowing errors - we shouldn't do this\n      return\n    }\n    this._connectionError = true\n    clearTimeout(this.connectionTimeoutHandle)\n    if (this._connectionCallback) {\n      return this._connectionCallback(err)\n    }\n    this.emit('error', err)\n  }\n\n  // if we're connected and we receive an error event from the connection\n  // this means the socket is dead - do a hard abort of all queries and emit\n  // the socket error on the client as well\n  _handleErrorEvent(err) {\n    if (this._connecting) {\n      return this._handleErrorWhileConnecting(err)\n    }\n    this._queryable = false\n    this._errorAllQueries(err)\n    this.emit('error', err)\n  }\n\n  // handle error messages from the postgres backend\n  _handleErrorMessage(msg) {\n    if (this._connecting) {\n      return this._handleErrorWhileConnecting(msg)\n    }\n    const activeQuery = this.activeQuery\n\n    if (!activeQuery) {\n      this._handleErrorEvent(msg)\n      return\n    }\n\n    this.activeQuery = null\n    activeQuery.handleError(msg, this.connection)\n  }\n\n  _handleRowDescription(msg) {\n    // delegate rowDescription to active query\n    this.activeQuery.handleRowDescription(msg)\n  }\n\n  _handleDataRow(msg) {\n    // delegate dataRow to active query\n    this.activeQuery.handleDataRow(msg)\n  }\n\n  _handlePortalSuspended(msg) {\n    // delegate portalSuspended to active query\n    this.activeQuery.handlePortalSuspended(this.connection)\n  }\n\n  _handleEmptyQuery(msg) {\n    // delegate emptyQuery to active query\n    this.activeQuery.handleEmptyQuery(this.connection)\n  }\n\n  _handleCommandComplete(msg) {\n    if (this.activeQuery == null) {\n      const error = new Error('Received unexpected commandComplete message from backend.')\n      this._handleErrorEvent(error)\n      return\n    }\n    // delegate commandComplete to active query\n    this.activeQuery.handleCommandComplete(msg, this.connection)\n  }\n\n  _handleParseComplete() {\n    if (this.activeQuery == null) {\n      const error = new Error('Received unexpected parseComplete message from backend.')\n      this._handleErrorEvent(error)\n      return\n    }\n    // if a prepared statement has a name and properly parses\n    // we track that its already been executed so we don't parse\n    // it again on the same client\n    if (this.activeQuery.name) {\n      this.connection.parsedStatements[this.activeQuery.name] = this.activeQuery.text\n    }\n  }\n\n  _handleCopyInResponse(msg) {\n    this.activeQuery.handleCopyInResponse(this.connection)\n  }\n\n  _handleCopyData(msg) {\n    this.activeQuery.handleCopyData(msg, this.connection)\n  }\n\n  _handleNotification(msg) {\n    this.emit('notification', msg)\n  }\n\n  _handleNotice(msg) {\n    this.emit('notice', msg)\n  }\n\n  getStartupConf() {\n    const params = this.connectionParameters\n\n    const data = {\n      user: params.user,\n      database: params.database,\n    }\n\n    const appName = params.application_name || params.fallback_application_name\n    if (appName) {\n      data.application_name = appName\n    }\n    if (params.replication) {\n      data.replication = '' + params.replication\n    }\n    if (params.statement_timeout) {\n      data.statement_timeout = String(parseInt(params.statement_timeout, 10))\n    }\n    if (params.lock_timeout) {\n      data.lock_timeout = String(parseInt(params.lock_timeout, 10))\n    }\n    if (params.idle_in_transaction_session_timeout) {\n      data.idle_in_transaction_session_timeout = String(parseInt(params.idle_in_transaction_session_timeout, 10))\n    }\n    if (params.options) {\n      data.options = params.options\n    }\n\n    return data\n  }\n\n  cancel(client, query) {\n    if (client.activeQuery === query) {\n      const con = this.connection\n\n      if (this.host && this.host.indexOf('/') === 0) {\n        con.connect(this.host + '/.s.PGSQL.' + this.port)\n      } else {\n        con.connect(this.port, this.host)\n      }\n\n      // once connection is established send cancel message\n      con.on('connect', function () {\n        con.cancel(client.processID, client.secretKey)\n      })\n    } else if (client.queryQueue.indexOf(query) !== -1) {\n      client.queryQueue.splice(client.queryQueue.indexOf(query), 1)\n    }\n  }\n\n  setTypeParser(oid, format, parseFn) {\n    return this._types.setTypeParser(oid, format, parseFn)\n  }\n\n  getTypeParser(oid, format) {\n    return this._types.getTypeParser(oid, format)\n  }\n\n  // escapeIdentifier and escapeLiteral moved to utility functions & exported\n  // on PG\n  // re-exported here for backwards compatibility\n  escapeIdentifier(str) {\n    return utils.escapeIdentifier(str)\n  }\n\n  escapeLiteral(str) {\n    return utils.escapeLiteral(str)\n  }\n\n  _pulseQueryQueue() {\n    if (this.readyForQuery === true) {\n      this.activeQuery = this.queryQueue.shift()\n      if (this.activeQuery) {\n        this.readyForQuery = false\n        this.hasExecuted = true\n\n        const queryError = this.activeQuery.submit(this.connection)\n        if (queryError) {\n          process.nextTick(() => {\n            this.activeQuery.handleError(queryError, this.connection)\n            this.readyForQuery = true\n            this._pulseQueryQueue()\n          })\n        }\n      } else if (this.hasExecuted) {\n        this.activeQuery = null\n        this.emit('drain')\n      }\n    }\n  }\n\n  query(config, values, callback) {\n    // can take in strings, config object or query object\n    let query\n    let result\n    let readTimeout\n    let readTimeoutTimer\n    let queryCallback\n\n    if (config === null || config === undefined) {\n      throw new TypeError('Client was passed a null or undefined query')\n    } else if (typeof config.submit === 'function') {\n      readTimeout = config.query_timeout || this.connectionParameters.query_timeout\n      result = query = config\n      if (typeof values === 'function') {\n        query.callback = query.callback || values\n      }\n    } else {\n      readTimeout = config.query_timeout || this.connectionParameters.query_timeout\n      query = new Query(config, values, callback)\n      if (!query.callback) {\n        result = new this._Promise((resolve, reject) => {\n          query.callback = (err, res) => (err ? reject(err) : resolve(res))\n        }).catch((err) => {\n          // replace the stack trace that leads to `TCP.onStreamRead` with one that leads back to the\n          // application that created the query\n          Error.captureStackTrace(err)\n          throw err\n        })\n      }\n    }\n\n    if (readTimeout) {\n      queryCallback = query.callback\n\n      readTimeoutTimer = setTimeout(() => {\n        const error = new Error('Query read timeout')\n\n        process.nextTick(() => {\n          query.handleError(error, this.connection)\n        })\n\n        queryCallback(error)\n\n        // we already returned an error,\n        // just do nothing if query completes\n        query.callback = () => {}\n\n        // Remove from queue\n        const index = this.queryQueue.indexOf(query)\n        if (index > -1) {\n          this.queryQueue.splice(index, 1)\n        }\n\n        this._pulseQueryQueue()\n      }, readTimeout)\n\n      query.callback = (err, res) => {\n        clearTimeout(readTimeoutTimer)\n        queryCallback(err, res)\n      }\n    }\n\n    if (this.binary && !query.binary) {\n      query.binary = true\n    }\n\n    if (query._result && !query._result._types) {\n      query._result._types = this._types\n    }\n\n    if (!this._queryable) {\n      process.nextTick(() => {\n        query.handleError(new Error('Client has encountered a connection error and is not queryable'), this.connection)\n      })\n      return result\n    }\n\n    if (this._ending) {\n      process.nextTick(() => {\n        query.handleError(new Error('Client was closed and is not queryable'), this.connection)\n      })\n      return result\n    }\n\n    this.queryQueue.push(query)\n    this._pulseQueryQueue()\n    return result\n  }\n\n  ref() {\n    this.connection.ref()\n  }\n\n  unref() {\n    this.connection.unref()\n  }\n\n  end(cb) {\n    this._ending = true\n\n    // if we have never connected, then end is a noop, callback immediately\n    if (!this.connection._connecting || this._ended) {\n      if (cb) {\n        cb()\n      } else {\n        return this._Promise.resolve()\n      }\n    }\n\n    if (this.activeQuery || !this._queryable) {\n      // if we have an active query we need to force a disconnect\n      // on the socket - otherwise a hung query could block end forever\n      this.connection.stream.destroy()\n    } else {\n      this.connection.end()\n    }\n\n    if (cb) {\n      this.connection.once('end', cb)\n    } else {\n      return new this._Promise((resolve) => {\n        this.connection.once('end', resolve)\n      })\n    }\n  }\n}\n\n// expose a Query constructor\nClient.Query = Query\n\nmodule.exports = Client\n", "'use strict'\nconst EventEmitter = require('events').EventEmitter\n\nconst NOOP = function () {}\n\nconst removeWhere = (list, predicate) => {\n  const i = list.findIndex(predicate)\n\n  return i === -1 ? undefined : list.splice(i, 1)[0]\n}\n\nclass IdleItem {\n  constructor(client, idleListener, timeoutId) {\n    this.client = client\n    this.idleListener = idleListener\n    this.timeoutId = timeoutId\n  }\n}\n\nclass PendingItem {\n  constructor(callback) {\n    this.callback = callback\n  }\n}\n\nfunction throwOnDoubleRelease() {\n  throw new Error('Release called on client which has already been released to the pool.')\n}\n\nfunction promisify(Promise, callback) {\n  if (callback) {\n    return { callback: callback, result: undefined }\n  }\n  let rej\n  let res\n  const cb = function (err, client) {\n    err ? rej(err) : res(client)\n  }\n  const result = new Promise(function (resolve, reject) {\n    res = resolve\n    rej = reject\n  }).catch((err) => {\n    // replace the stack trace that leads to `TCP.onStreamRead` with one that leads back to the\n    // application that created the query\n    Error.captureStackTrace(err)\n    throw err\n  })\n  return { callback: cb, result: result }\n}\n\nfunction makeIdleListener(pool, client) {\n  return function idleListener(err) {\n    err.client = client\n\n    client.removeListener('error', idleListener)\n    client.on('error', () => {\n      pool.log('additional client error after disconnection due to error', err)\n    })\n    pool._remove(client)\n    // TODO - document that once the pool emits an error\n    // the client has already been closed & purged and is unusable\n    pool.emit('error', err, client)\n  }\n}\n\nclass Pool extends EventEmitter {\n  constructor(options, Client) {\n    super()\n    this.options = Object.assign({}, options)\n\n    if (options != null && 'password' in options) {\n      // \"hiding\" the password so it doesn't show up in stack traces\n      // or if the client is console.logged\n      Object.defineProperty(this.options, 'password', {\n        configurable: true,\n        enumerable: false,\n        writable: true,\n        value: options.password,\n      })\n    }\n    if (options != null && options.ssl && options.ssl.key) {\n      // \"hiding\" the ssl->key so it doesn't show up in stack traces\n      // or if the client is console.logged\n      Object.defineProperty(this.options.ssl, 'key', {\n        enumerable: false,\n      })\n    }\n\n    this.options.max = this.options.max || this.options.poolSize || 10\n    this.options.min = this.options.min || 0\n    this.options.maxUses = this.options.maxUses || Infinity\n    this.options.allowExitOnIdle = this.options.allowExitOnIdle || false\n    this.options.maxLifetimeSeconds = this.options.maxLifetimeSeconds || 0\n    this.log = this.options.log || function () {}\n    this.Client = this.options.Client || Client || require('pg').Client\n    this.Promise = this.options.Promise || global.Promise\n\n    if (typeof this.options.idleTimeoutMillis === 'undefined') {\n      this.options.idleTimeoutMillis = 10000\n    }\n\n    this._clients = []\n    this._idle = []\n    this._expired = new WeakSet()\n    this._pendingQueue = []\n    this._endCallback = undefined\n    this.ending = false\n    this.ended = false\n  }\n\n  _isFull() {\n    return this._clients.length >= this.options.max\n  }\n\n  _isAboveMin() {\n    return this._clients.length > this.options.min\n  }\n\n  _pulseQueue() {\n    this.log('pulse queue')\n    if (this.ended) {\n      this.log('pulse queue ended')\n      return\n    }\n    if (this.ending) {\n      this.log('pulse queue on ending')\n      if (this._idle.length) {\n        this._idle.slice().map((item) => {\n          this._remove(item.client)\n        })\n      }\n      if (!this._clients.length) {\n        this.ended = true\n        this._endCallback()\n      }\n      return\n    }\n\n    // if we don't have any waiting, do nothing\n    if (!this._pendingQueue.length) {\n      this.log('no queued requests')\n      return\n    }\n    // if we don't have any idle clients and we have no more room do nothing\n    if (!this._idle.length && this._isFull()) {\n      return\n    }\n    const pendingItem = this._pendingQueue.shift()\n    if (this._idle.length) {\n      const idleItem = this._idle.pop()\n      clearTimeout(idleItem.timeoutId)\n      const client = idleItem.client\n      client.ref && client.ref()\n      const idleListener = idleItem.idleListener\n\n      return this._acquireClient(client, pendingItem, idleListener, false)\n    }\n    if (!this._isFull()) {\n      return this.newClient(pendingItem)\n    }\n    throw new Error('unexpected condition')\n  }\n\n  _remove(client, callback) {\n    const removed = removeWhere(this._idle, (item) => item.client === client)\n\n    if (removed !== undefined) {\n      clearTimeout(removed.timeoutId)\n    }\n\n    this._clients = this._clients.filter((c) => c !== client)\n    const context = this\n    client.end(() => {\n      context.emit('remove', client)\n\n      if (typeof callback === 'function') {\n        callback()\n      }\n    })\n  }\n\n  connect(cb) {\n    if (this.ending) {\n      const err = new Error('Cannot use a pool after calling end on the pool')\n      return cb ? cb(err) : this.Promise.reject(err)\n    }\n\n    const response = promisify(this.Promise, cb)\n    const result = response.result\n\n    // if we don't have to connect a new client, don't do so\n    if (this._isFull() || this._idle.length) {\n      // if we have idle clients schedule a pulse immediately\n      if (this._idle.length) {\n        process.nextTick(() => this._pulseQueue())\n      }\n\n      if (!this.options.connectionTimeoutMillis) {\n        this._pendingQueue.push(new PendingItem(response.callback))\n        return result\n      }\n\n      const queueCallback = (err, res, done) => {\n        clearTimeout(tid)\n        response.callback(err, res, done)\n      }\n\n      const pendingItem = new PendingItem(queueCallback)\n\n      // set connection timeout on checking out an existing client\n      const tid = setTimeout(() => {\n        // remove the callback from pending waiters because\n        // we're going to call it with a timeout error\n        removeWhere(this._pendingQueue, (i) => i.callback === queueCallback)\n        pendingItem.timedOut = true\n        response.callback(new Error('timeout exceeded when trying to connect'))\n      }, this.options.connectionTimeoutMillis)\n\n      if (tid.unref) {\n        tid.unref()\n      }\n\n      this._pendingQueue.push(pendingItem)\n      return result\n    }\n\n    this.newClient(new PendingItem(response.callback))\n\n    return result\n  }\n\n  newClient(pendingItem) {\n    const client = new this.Client(this.options)\n    this._clients.push(client)\n    const idleListener = makeIdleListener(this, client)\n\n    this.log('checking client timeout')\n\n    // connection timeout logic\n    let tid\n    let timeoutHit = false\n    if (this.options.connectionTimeoutMillis) {\n      tid = setTimeout(() => {\n        this.log('ending client due to timeout')\n        timeoutHit = true\n        // force kill the node driver, and let libpq do its teardown\n        client.connection ? client.connection.stream.destroy() : client.end()\n      }, this.options.connectionTimeoutMillis)\n    }\n\n    this.log('connecting new client')\n    client.connect((err) => {\n      if (tid) {\n        clearTimeout(tid)\n      }\n      client.on('error', idleListener)\n      if (err) {\n        this.log('client failed to connect', err)\n        // remove the dead client from our list of clients\n        this._clients = this._clients.filter((c) => c !== client)\n        if (timeoutHit) {\n          err = new Error('Connection terminated due to connection timeout', { cause: err })\n        }\n\n        // this client won’t be released, so move on immediately\n        this._pulseQueue()\n\n        if (!pendingItem.timedOut) {\n          pendingItem.callback(err, undefined, NOOP)\n        }\n      } else {\n        this.log('new client connected')\n\n        if (this.options.maxLifetimeSeconds !== 0) {\n          const maxLifetimeTimeout = setTimeout(() => {\n            this.log('ending client due to expired lifetime')\n            this._expired.add(client)\n            const idleIndex = this._idle.findIndex((idleItem) => idleItem.client === client)\n            if (idleIndex !== -1) {\n              this._acquireClient(\n                client,\n                new PendingItem((err, client, clientRelease) => clientRelease()),\n                idleListener,\n                false\n              )\n            }\n          }, this.options.maxLifetimeSeconds * 1000)\n\n          maxLifetimeTimeout.unref()\n          client.once('end', () => clearTimeout(maxLifetimeTimeout))\n        }\n\n        return this._acquireClient(client, pendingItem, idleListener, true)\n      }\n    })\n  }\n\n  // acquire a client for a pending work item\n  _acquireClient(client, pendingItem, idleListener, isNew) {\n    if (isNew) {\n      this.emit('connect', client)\n    }\n\n    this.emit('acquire', client)\n\n    client.release = this._releaseOnce(client, idleListener)\n\n    client.removeListener('error', idleListener)\n\n    if (!pendingItem.timedOut) {\n      if (isNew && this.options.verify) {\n        this.options.verify(client, (err) => {\n          if (err) {\n            client.release(err)\n            return pendingItem.callback(err, undefined, NOOP)\n          }\n\n          pendingItem.callback(undefined, client, client.release)\n        })\n      } else {\n        pendingItem.callback(undefined, client, client.release)\n      }\n    } else {\n      if (isNew && this.options.verify) {\n        this.options.verify(client, client.release)\n      } else {\n        client.release()\n      }\n    }\n  }\n\n  // returns a function that wraps _release and throws if called more than once\n  _releaseOnce(client, idleListener) {\n    let released = false\n\n    return (err) => {\n      if (released) {\n        throwOnDoubleRelease()\n      }\n\n      released = true\n      this._release(client, idleListener, err)\n    }\n  }\n\n  // release a client back to the poll, include an error\n  // to remove it from the pool\n  _release(client, idleListener, err) {\n    client.on('error', idleListener)\n\n    client._poolUseCount = (client._poolUseCount || 0) + 1\n\n    this.emit('release', err, client)\n\n    // TODO(bmc): expose a proper, public interface _queryable and _ending\n    if (err || this.ending || !client._queryable || client._ending || client._poolUseCount >= this.options.maxUses) {\n      if (client._poolUseCount >= this.options.maxUses) {\n        this.log('remove expended client')\n      }\n\n      return this._remove(client, this._pulseQueue.bind(this))\n    }\n\n    const isExpired = this._expired.has(client)\n    if (isExpired) {\n      this.log('remove expired client')\n      this._expired.delete(client)\n      return this._remove(client, this._pulseQueue.bind(this))\n    }\n\n    // idle timeout\n    let tid\n    if (this.options.idleTimeoutMillis && this._isAboveMin()) {\n      tid = setTimeout(() => {\n        this.log('remove idle client')\n        this._remove(client, this._pulseQueue.bind(this))\n      }, this.options.idleTimeoutMillis)\n\n      if (this.options.allowExitOnIdle) {\n        // allow Node to exit if this is all that's left\n        tid.unref()\n      }\n    }\n\n    if (this.options.allowExitOnIdle) {\n      client.unref()\n    }\n\n    this._idle.push(new IdleItem(client, idleListener, tid))\n    this._pulseQueue()\n  }\n\n  query(text, values, cb) {\n    // guard clause against passing a function as the first parameter\n    if (typeof text === 'function') {\n      const response = promisify(this.Promise, text)\n      setImmediate(function () {\n        return response.callback(new Error('Passing a function as the first parameter to pool.query is not supported'))\n      })\n      return response.result\n    }\n\n    // allow plain text query without values\n    if (typeof values === 'function') {\n      cb = values\n      values = undefined\n    }\n    const response = promisify(this.Promise, cb)\n    cb = response.callback\n\n    this.connect((err, client) => {\n      if (err) {\n        return cb(err)\n      }\n\n      let clientReleased = false\n      const onError = (err) => {\n        if (clientReleased) {\n          return\n        }\n        clientReleased = true\n        client.release(err)\n        cb(err)\n      }\n\n      client.once('error', onError)\n      this.log('dispatching query')\n      try {\n        client.query(text, values, (err, res) => {\n          this.log('query dispatched')\n          client.removeListener('error', onError)\n          if (clientReleased) {\n            return\n          }\n          clientReleased = true\n          client.release(err)\n          if (err) {\n            return cb(err)\n          }\n          return cb(undefined, res)\n        })\n      } catch (err) {\n        client.release(err)\n        return cb(err)\n      }\n    })\n    return response.result\n  }\n\n  end(cb) {\n    this.log('ending')\n    if (this.ending) {\n      const err = new Error('Called end on pool more than once')\n      return cb ? cb(err) : this.Promise.reject(err)\n    }\n    this.ending = true\n    const promised = promisify(this.Promise, cb)\n    this._endCallback = promised.callback\n    this._pulseQueue()\n    return promised.result\n  }\n\n  get waitingCount() {\n    return this._pendingQueue.length\n  }\n\n  get idleCount() {\n    return this._idle.length\n  }\n\n  get expiredCount() {\n    return this._clients.reduce((acc, client) => acc + (this._expired.has(client) ? 1 : 0), 0)\n  }\n\n  get totalCount() {\n    return this._clients.length\n  }\n}\nmodule.exports = Pool\n", "throw new Error(`Could not resolve \"pg-native\" imported by \"pg\". Is it installed?`)", "'use strict'\n\nconst EventEmitter = require('events').EventEmitter\nconst util = require('util')\nconst utils = require('../utils')\n\nconst NativeQuery = (module.exports = function (config, values, callback) {\n  EventEmitter.call(this)\n  config = utils.normalizeQueryConfig(config, values, callback)\n  this.text = config.text\n  this.values = config.values\n  this.name = config.name\n  this.queryMode = config.queryMode\n  this.callback = config.callback\n  this.state = 'new'\n  this._arrayMode = config.rowMode === 'array'\n\n  // if the 'row' event is listened for\n  // then emit them as they come in\n  // without setting singleRowMode to true\n  // this has almost no meaning because libpq\n  // reads all rows into memory befor returning any\n  this._emitRowEvents = false\n  this.on(\n    'newListener',\n    function (event) {\n      if (event === 'row') this._emitRowEvents = true\n    }.bind(this)\n  )\n})\n\nutil.inherits(NativeQuery, EventEmitter)\n\nconst errorFieldMap = {\n  sqlState: 'code',\n  statementPosition: 'position',\n  messagePrimary: 'message',\n  context: 'where',\n  schemaName: 'schema',\n  tableName: 'table',\n  columnName: 'column',\n  dataTypeName: 'dataType',\n  constraintName: 'constraint',\n  sourceFile: 'file',\n  sourceLine: 'line',\n  sourceFunction: 'routine',\n}\n\nNativeQuery.prototype.handleError = function (err) {\n  // copy pq error fields into the error object\n  const fields = this.native.pq.resultErrorFields()\n  if (fields) {\n    for (const key in fields) {\n      const normalizedFieldName = errorFieldMap[key] || key\n      err[normalizedFieldName] = fields[key]\n    }\n  }\n  if (this.callback) {\n    this.callback(err)\n  } else {\n    this.emit('error', err)\n  }\n  this.state = 'error'\n}\n\nNativeQuery.prototype.then = function (onSuccess, onFailure) {\n  return this._getPromise().then(onSuccess, onFailure)\n}\n\nNativeQuery.prototype.catch = function (callback) {\n  return this._getPromise().catch(callback)\n}\n\nNativeQuery.prototype._getPromise = function () {\n  if (this._promise) return this._promise\n  this._promise = new Promise(\n    function (resolve, reject) {\n      this._once('end', resolve)\n      this._once('error', reject)\n    }.bind(this)\n  )\n  return this._promise\n}\n\nNativeQuery.prototype.submit = function (client) {\n  this.state = 'running'\n  const self = this\n  this.native = client.native\n  client.native.arrayMode = this._arrayMode\n\n  let after = function (err, rows, results) {\n    client.native.arrayMode = false\n    setImmediate(function () {\n      self.emit('_done')\n    })\n\n    // handle possible query error\n    if (err) {\n      return self.handleError(err)\n    }\n\n    // emit row events for each row in the result\n    if (self._emitRowEvents) {\n      if (results.length > 1) {\n        rows.forEach((rowOfRows, i) => {\n          rowOfRows.forEach((row) => {\n            self.emit('row', row, results[i])\n          })\n        })\n      } else {\n        rows.forEach(function (row) {\n          self.emit('row', row, results)\n        })\n      }\n    }\n\n    // handle successful result\n    self.state = 'end'\n    self.emit('end', results)\n    if (self.callback) {\n      self.callback(null, results)\n    }\n  }\n\n  if (process.domain) {\n    after = process.domain.bind(after)\n  }\n\n  // named query\n  if (this.name) {\n    if (this.name.length > 63) {\n      console.error('Warning! Postgres only supports 63 characters for query names.')\n      console.error('You supplied %s (%s)', this.name, this.name.length)\n      console.error('This can cause conflicts and silent errors executing queries')\n    }\n    const values = (this.values || []).map(utils.prepareValue)\n\n    // check if the client has already executed this named query\n    // if so...just execute it again - skip the planning phase\n    if (client.namedQueries[this.name]) {\n      if (this.text && client.namedQueries[this.name] !== this.text) {\n        const err = new Error(`Prepared statements must be unique - '${this.name}' was used for a different statement`)\n        return after(err)\n      }\n      return client.native.execute(this.name, values, after)\n    }\n    // plan the named query the first time, then execute it\n    return client.native.prepare(this.name, this.text, values.length, function (err) {\n      if (err) return after(err)\n      client.namedQueries[self.name] = self.text\n      return self.native.execute(self.name, values, after)\n    })\n  } else if (this.values) {\n    if (!Array.isArray(this.values)) {\n      const err = new Error('Query values must be an array')\n      return after(err)\n    }\n    const vals = this.values.map(utils.prepareValue)\n    client.native.query(this.text, vals, after)\n  } else if (this.queryMode === 'extended') {\n    client.native.query(this.text, [], after)\n  } else {\n    client.native.query(this.text, after)\n  }\n}\n", "'use strict'\n\n// eslint-disable-next-line\nvar Native\n// eslint-disable-next-line no-useless-catch\ntry {\n  // Wrap this `require()` in a try-catch to avoid upstream bundlers from complaining that this might not be available since it is an optional import\n  Native = require('pg-native')\n} catch (e) {\n  throw e\n}\nconst TypeOverrides = require('../type-overrides')\nconst EventEmitter = require('events').EventEmitter\nconst util = require('util')\nconst ConnectionParameters = require('../connection-parameters')\n\nconst NativeQuery = require('./query')\n\nconst Client = (module.exports = function (config) {\n  EventEmitter.call(this)\n  config = config || {}\n\n  this._Promise = config.Promise || global.Promise\n  this._types = new TypeOverrides(config.types)\n\n  this.native = new Native({\n    types: this._types,\n  })\n\n  this._queryQueue = []\n  this._ending = false\n  this._connecting = false\n  this._connected = false\n  this._queryable = true\n\n  // keep these on the object for legacy reasons\n  // for the time being. TODO: deprecate all this jazz\n  const cp = (this.connectionParameters = new ConnectionParameters(config))\n  if (config.nativeConnectionString) cp.nativeConnectionString = config.nativeConnectionString\n  this.user = cp.user\n\n  // \"hiding\" the password so it doesn't show up in stack traces\n  // or if the client is console.logged\n  Object.defineProperty(this, 'password', {\n    configurable: true,\n    enumerable: false,\n    writable: true,\n    value: cp.password,\n  })\n  this.database = cp.database\n  this.host = cp.host\n  this.port = cp.port\n\n  // a hash to hold named queries\n  this.namedQueries = {}\n})\n\nClient.Query = NativeQuery\n\nutil.inherits(Client, EventEmitter)\n\nClient.prototype._errorAllQueries = function (err) {\n  const enqueueError = (query) => {\n    process.nextTick(() => {\n      query.native = this.native\n      query.handleError(err)\n    })\n  }\n\n  if (this._hasActiveQuery()) {\n    enqueueError(this._activeQuery)\n    this._activeQuery = null\n  }\n\n  this._queryQueue.forEach(enqueueError)\n  this._queryQueue.length = 0\n}\n\n// connect to the backend\n// pass an optional callback to be called once connected\n// or with an error if there was a connection error\nClient.prototype._connect = function (cb) {\n  const self = this\n\n  if (this._connecting) {\n    process.nextTick(() => cb(new Error('Client has already been connected. You cannot reuse a client.')))\n    return\n  }\n\n  this._connecting = true\n\n  this.connectionParameters.getLibpqConnectionString(function (err, conString) {\n    if (self.connectionParameters.nativeConnectionString) conString = self.connectionParameters.nativeConnectionString\n    if (err) return cb(err)\n    self.native.connect(conString, function (err) {\n      if (err) {\n        self.native.end()\n        return cb(err)\n      }\n\n      // set internal states to connected\n      self._connected = true\n\n      // handle connection errors from the native layer\n      self.native.on('error', function (err) {\n        self._queryable = false\n        self._errorAllQueries(err)\n        self.emit('error', err)\n      })\n\n      self.native.on('notification', function (msg) {\n        self.emit('notification', {\n          channel: msg.relname,\n          payload: msg.extra,\n        })\n      })\n\n      // signal we are connected now\n      self.emit('connect')\n      self._pulseQueryQueue(true)\n\n      cb()\n    })\n  })\n}\n\nClient.prototype.connect = function (callback) {\n  if (callback) {\n    this._connect(callback)\n    return\n  }\n\n  return new this._Promise((resolve, reject) => {\n    this._connect((error) => {\n      if (error) {\n        reject(error)\n      } else {\n        resolve()\n      }\n    })\n  })\n}\n\n// send a query to the server\n// this method is highly overloaded to take\n// 1) string query, optional array of parameters, optional function callback\n// 2) object query with {\n//    string query\n//    optional array values,\n//    optional function callback instead of as a separate parameter\n//    optional string name to name & cache the query plan\n//    optional string rowMode = 'array' for an array of results\n//  }\nClient.prototype.query = function (config, values, callback) {\n  let query\n  let result\n  let readTimeout\n  let readTimeoutTimer\n  let queryCallback\n\n  if (config === null || config === undefined) {\n    throw new TypeError('Client was passed a null or undefined query')\n  } else if (typeof config.submit === 'function') {\n    readTimeout = config.query_timeout || this.connectionParameters.query_timeout\n    result = query = config\n    // accept query(new Query(...), (err, res) => { }) style\n    if (typeof values === 'function') {\n      config.callback = values\n    }\n  } else {\n    readTimeout = config.query_timeout || this.connectionParameters.query_timeout\n    query = new NativeQuery(config, values, callback)\n    if (!query.callback) {\n      let resolveOut, rejectOut\n      result = new this._Promise((resolve, reject) => {\n        resolveOut = resolve\n        rejectOut = reject\n      }).catch((err) => {\n        Error.captureStackTrace(err)\n        throw err\n      })\n      query.callback = (err, res) => (err ? rejectOut(err) : resolveOut(res))\n    }\n  }\n\n  if (readTimeout) {\n    queryCallback = query.callback\n\n    readTimeoutTimer = setTimeout(() => {\n      const error = new Error('Query read timeout')\n\n      process.nextTick(() => {\n        query.handleError(error, this.connection)\n      })\n\n      queryCallback(error)\n\n      // we already returned an error,\n      // just do nothing if query completes\n      query.callback = () => {}\n\n      // Remove from queue\n      const index = this._queryQueue.indexOf(query)\n      if (index > -1) {\n        this._queryQueue.splice(index, 1)\n      }\n\n      this._pulseQueryQueue()\n    }, readTimeout)\n\n    query.callback = (err, res) => {\n      clearTimeout(readTimeoutTimer)\n      queryCallback(err, res)\n    }\n  }\n\n  if (!this._queryable) {\n    query.native = this.native\n    process.nextTick(() => {\n      query.handleError(new Error('Client has encountered a connection error and is not queryable'))\n    })\n    return result\n  }\n\n  if (this._ending) {\n    query.native = this.native\n    process.nextTick(() => {\n      query.handleError(new Error('Client was closed and is not queryable'))\n    })\n    return result\n  }\n\n  this._queryQueue.push(query)\n  this._pulseQueryQueue()\n  return result\n}\n\n// disconnect from the backend server\nClient.prototype.end = function (cb) {\n  const self = this\n\n  this._ending = true\n\n  if (!this._connected) {\n    this.once('connect', this.end.bind(this, cb))\n  }\n  let result\n  if (!cb) {\n    result = new this._Promise(function (resolve, reject) {\n      cb = (err) => (err ? reject(err) : resolve())\n    })\n  }\n  this.native.end(function () {\n    self._errorAllQueries(new Error('Connection terminated'))\n\n    process.nextTick(() => {\n      self.emit('end')\n      if (cb) cb()\n    })\n  })\n  return result\n}\n\nClient.prototype._hasActiveQuery = function () {\n  return this._activeQuery && this._activeQuery.state !== 'error' && this._activeQuery.state !== 'end'\n}\n\nClient.prototype._pulseQueryQueue = function (initialConnection) {\n  if (!this._connected) {\n    return\n  }\n  if (this._hasActiveQuery()) {\n    return\n  }\n  const query = this._queryQueue.shift()\n  if (!query) {\n    if (!initialConnection) {\n      this.emit('drain')\n    }\n    return\n  }\n  this._activeQuery = query\n  query.submit(this)\n  const self = this\n  query.once('_done', function () {\n    self._pulseQueryQueue()\n  })\n}\n\n// attempt to cancel an in-progress query\nClient.prototype.cancel = function (query) {\n  if (this._activeQuery === query) {\n    this.native.cancel(function () {})\n  } else if (this._queryQueue.indexOf(query) !== -1) {\n    this._queryQueue.splice(this._queryQueue.indexOf(query), 1)\n  }\n}\n\nClient.prototype.ref = function () {}\nClient.prototype.unref = function () {}\n\nClient.prototype.setTypeParser = function (oid, format, parseFn) {\n  return this._types.setTypeParser(oid, format, parseFn)\n}\n\nClient.prototype.getTypeParser = function (oid, format) {\n  return this._types.getTypeParser(oid, format)\n}\n", "'use strict'\nmodule.exports = require('./client')\n", "'use strict'\n\nconst Client = require('./client')\nconst defaults = require('./defaults')\nconst Connection = require('./connection')\nconst Result = require('./result')\nconst utils = require('./utils')\nconst Pool = require('pg-pool')\nconst TypeOverrides = require('./type-overrides')\nconst { DatabaseError } = require('pg-protocol')\nconst { escapeIdentifier, escapeLiteral } = require('./utils')\n\nconst poolFactory = (Client) => {\n  return class BoundPool extends Pool {\n    constructor(options) {\n      super(options, Client)\n    }\n  }\n}\n\nconst PG = function (clientConstructor) {\n  this.defaults = defaults\n  this.Client = clientConstructor\n  this.Query = this.Client.Query\n  this.Pool = poolFactory(this.Client)\n  this._pools = []\n  this.Connection = Connection\n  this.types = require('pg-types')\n  this.DatabaseError = DatabaseError\n  this.TypeOverrides = TypeOverrides\n  this.escapeIdentifier = escapeIdentifier\n  this.escapeLiteral = escapeLiteral\n  this.Result = Result\n  this.utils = utils\n}\n\nif (typeof process.env.NODE_PG_FORCE_NATIVE !== 'undefined') {\n  module.exports = new PG(require('./native'))\n} else {\n  module.exports = new PG(Client)\n\n  // lazy require native module...the native module may not have installed\n  Object.defineProperty(module.exports, 'native', {\n    configurable: true,\n    enumerable: false,\n    get() {\n      let native = null\n      try {\n        native = new PG(require('./native'))\n      } catch (err) {\n        if (err.code !== 'MODULE_NOT_FOUND') {\n          throw err\n        }\n      }\n\n      // overwrite module.exports.native so that getter is never called again\n      Object.defineProperty(module.exports, 'native', {\n        value: native,\n      })\n\n      return native\n    },\n  })\n}\n", "// ESM wrapper for pg\nimport pg from '../lib/index.js'\n\n// Re-export all the properties\nexport const Client = pg.Client\nexport const Pool = pg.Pool\nexport const Connection = pg.Connection\nexport const types = pg.types\nexport const Query = pg.Query\nexport const DatabaseError = pg.DatabaseError\nexport const escapeIdentifier = pg.escapeIdentifier\nexport const escapeLiteral = pg.escapeLiteral\nexport const Result = pg.Result\nexport const TypeOverrides = pg.TypeOverrides\n\n// Also export the defaults\nexport const defaults = pg.defaults\n\n// Re-export the default\nexport default pg\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,0FAA0F,GAAG,mIAAmI;AAAA,QAC/O;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA;AAEA,YAAQ,QAAQ,SAAU,QAAQ,WAAW;AAC3C,aAAO,IAAI,YAAY,QAAQ,SAAS,EAAE,MAAM;AAAA,IAClD;AAEA,QAAM,cAAN,MAAM,aAAY;AAAA,MAChB,YAAa,QAAQ,WAAW;AAC9B,aAAK,SAAS;AACd,aAAK,YAAY,aAAa;AAC9B,aAAK,WAAW;AAChB,aAAK,UAAU,CAAC;AAChB,aAAK,WAAW,CAAC;AACjB,aAAK,YAAY;AAAA,MACnB;AAAA,MAEA,QAAS;AACP,eAAO,KAAK,YAAY,KAAK,OAAO;AAAA,MACtC;AAAA,MAEA,gBAAiB;AACf,YAAI,YAAY,KAAK,OAAO,KAAK,UAAU;AAC3C,YAAI,cAAc,MAAM;AACtB,iBAAO;AAAA,YACL,OAAO,KAAK,OAAO,KAAK,UAAU;AAAA,YAClC,SAAS;AAAA,UACX;AAAA,QACF;AACA,eAAO;AAAA,UACL,OAAO;AAAA,UACP,SAAS;AAAA,QACX;AAAA,MACF;AAAA,MAEA,OAAQ,WAAW;AACjB,aAAK,SAAS,KAAK,SAAS;AAAA,MAC9B;AAAA,MAEA,SAAU,cAAc;AACtB,YAAI;AACJ,YAAI,KAAK,SAAS,SAAS,KAAK,cAAc;AAC5C,kBAAQ,KAAK,SAAS,KAAK,EAAE;AAC7B,cAAI,UAAU,UAAU,CAAC,cAAc;AACrC,oBAAQ;AAAA,UACV;AACA,cAAI,UAAU,KAAM,SAAQ,KAAK,UAAU,KAAK;AAChD,eAAK,QAAQ,KAAK,KAAK;AACvB,eAAK,WAAW,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MAEA,oBAAqB;AACnB,YAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AAC1B,iBAAO,CAAC,KAAK,MAAM,GAAG;AACpB,gBAAI,OAAO,KAAK,cAAc;AAC9B,gBAAI,KAAK,UAAU,IAAK;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAAA,MAEA,MAAO,QAAQ;AACb,YAAI,WAAW,QAAQ;AACvB,aAAK,kBAAkB;AACvB,eAAO,CAAC,KAAK,MAAM,GAAG;AACpB,sBAAY,KAAK,cAAc;AAC/B,cAAI,UAAU,UAAU,OAAO,CAAC,OAAO;AACrC,iBAAK;AACL,gBAAI,KAAK,YAAY,GAAG;AACtB,uBAAS,IAAI,aAAY,KAAK,OAAO,OAAO,KAAK,WAAW,CAAC,GAAG,KAAK,SAAS;AAC9E,mBAAK,QAAQ,KAAK,OAAO,MAAM,IAAI,CAAC;AACpC,mBAAK,YAAY,OAAO,WAAW;AAAA,YACrC;AAAA,UACF,WAAW,UAAU,UAAU,OAAO,CAAC,OAAO;AAC5C,iBAAK;AACL,gBAAI,CAAC,KAAK,WAAW;AACnB,mBAAK,SAAS;AACd,kBAAI,OAAQ,QAAO,KAAK;AAAA,YAC1B;AAAA,UACF,WAAW,UAAU,UAAU,OAAO,CAAC,UAAU,SAAS;AACxD,gBAAI,MAAO,MAAK,SAAS,IAAI;AAC7B,oBAAQ,CAAC;AAAA,UACX,WAAW,UAAU,UAAU,OAAO,CAAC,OAAO;AAC5C,iBAAK,SAAS;AAAA,UAChB,OAAO;AACL,iBAAK,OAAO,UAAU,KAAK;AAAA,UAC7B;AAAA,QACF;AACA,YAAI,KAAK,cAAc,GAAG;AACxB,gBAAM,IAAI,MAAM,8BAA8B;AAAA,QAChD;AACA,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAEA,aAAS,SAAU,OAAO;AACxB,aAAO;AAAA,IACT;AAAA;AAAA;;;AChGA;AAAA;AAAA,QAAI,QAAQ;AAEZ,WAAO,UAAU;AAAA,MACf,QAAQ,SAAU,QAAQ,WAAW;AACnC,eAAO;AAAA,UACL,OAAO,WAAW;AAChB,mBAAO,MAAM,MAAM,QAAQ,SAAS;AAAA,UACtC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACVA;AAAA;AAAA;AAEA,QAAI,YAAY;AAChB,QAAI,OAAO;AACX,QAAI,YAAY;AAChB,QAAI,WAAW;AAEf,WAAO,UAAU,SAAS,UAAW,SAAS;AAC5C,UAAI,SAAS,KAAK,OAAO,GAAG;AAE1B,eAAO,OAAO,QAAQ,QAAQ,KAAK,GAAG,CAAC;AAAA,MACzC;AACA,UAAI,UAAU,UAAU,KAAK,OAAO;AAEpC,UAAI,CAAC,SAAS;AAEZ,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC7B;AAEA,UAAI,OAAO,CAAC,CAAC,QAAQ,CAAC;AACtB,UAAI,OAAO,SAAS,QAAQ,CAAC,GAAG,EAAE;AAClC,UAAI,MAAM;AACR,eAAO,qBAAqB,IAAI;AAAA,MAClC;AAEA,UAAI,QAAQ,SAAS,QAAQ,CAAC,GAAG,EAAE,IAAI;AACvC,UAAI,MAAM,QAAQ,CAAC;AACnB,UAAI,OAAO,SAAS,QAAQ,CAAC,GAAG,EAAE;AAClC,UAAI,SAAS,SAAS,QAAQ,CAAC,GAAG,EAAE;AACpC,UAAI,SAAS,SAAS,QAAQ,CAAC,GAAG,EAAE;AAEpC,UAAI,KAAK,QAAQ,CAAC;AAClB,WAAK,KAAK,MAAO,WAAW,EAAE,IAAI;AAElC,UAAI;AACJ,UAAI,SAAS,eAAe,OAAO;AACnC,UAAI,UAAU,MAAM;AAClB,eAAO,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,EAAE,CAAC;AAIpE,YAAI,QAAQ,IAAI,GAAG;AACjB,eAAK,eAAe,IAAI;AAAA,QAC1B;AAEA,YAAI,WAAW,GAAG;AAChB,eAAK,QAAQ,KAAK,QAAQ,IAAI,MAAM;AAAA,QACtC;AAAA,MACF,OAAO;AACL,eAAO,IAAI,KAAK,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,EAAE;AAE1D,YAAI,QAAQ,IAAI,GAAG;AACjB,eAAK,YAAY,IAAI;AAAA,QACvB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,QAAS,SAAS;AACzB,UAAI,UAAU,KAAK,KAAK,OAAO;AAC/B,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AAEA,UAAI,OAAO,SAAS,QAAQ,CAAC,GAAG,EAAE;AAClC,UAAI,OAAO,CAAC,CAAC,QAAQ,CAAC;AACtB,UAAI,MAAM;AACR,eAAO,qBAAqB,IAAI;AAAA,MAClC;AAEA,UAAI,QAAQ,SAAS,QAAQ,CAAC,GAAG,EAAE,IAAI;AACvC,UAAI,MAAM,QAAQ,CAAC;AAEnB,UAAI,OAAO,IAAI,KAAK,MAAM,OAAO,GAAG;AAEpC,UAAI,QAAQ,IAAI,GAAG;AACjB,aAAK,YAAY,IAAI;AAAA,MACvB;AAEA,aAAO;AAAA,IACT;AAMA,aAAS,eAAgB,SAAS;AAChC,UAAI,QAAQ,SAAS,KAAK,GAAG;AAC3B,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,UAAU,KAAK,QAAQ,MAAM,GAAG,EAAE,CAAC,CAAC;AAC/C,UAAI,CAAC,KAAM;AACX,UAAI,OAAO,KAAK,CAAC;AAEjB,UAAI,SAAS,KAAK;AAChB,eAAO;AAAA,MACT;AACA,UAAI,OAAO,SAAS,MAAM,KAAK;AAC/B,UAAI,SAAS,SAAS,KAAK,CAAC,GAAG,EAAE,IAAI,OACnC,SAAS,KAAK,CAAC,KAAK,GAAG,EAAE,IAAI,KAC7B,SAAS,KAAK,CAAC,KAAK,GAAG,EAAE;AAE3B,aAAO,SAAS,OAAO;AAAA,IACzB;AAEA,aAAS,qBAAsB,MAAM;AAGnC,aAAO,EAAE,OAAO;AAAA,IAClB;AAEA,aAAS,QAAS,KAAK;AACrB,aAAO,OAAO,KAAK,MAAM;AAAA,IAC3B;AAAA;AAAA;;;ACnHA;AAAA;AAAA,WAAO,UAAU;AAEjB,QAAI,iBAAiB,OAAO,UAAU;AAEtC,aAAS,OAAO,QAAQ;AACpB,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,YAAI,SAAS,UAAU,CAAC;AAExB,iBAAS,OAAO,QAAQ;AACpB,cAAI,eAAe,KAAK,QAAQ,GAAG,GAAG;AAClC,mBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,UAC5B;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAAA;AAAA;;;AChBA;AAAA;AAAA;AAEA,QAAI,SAAS;AAEb,WAAO,UAAU;AAEjB,aAAS,iBAAkB,KAAK;AAC9B,UAAI,EAAE,gBAAgB,mBAAmB;AACvC,eAAO,IAAI,iBAAiB,GAAG;AAAA,MACjC;AACA,aAAO,MAAM,MAAM,GAAG,CAAC;AAAA,IACzB;AACA,QAAI,aAAa,CAAC,WAAW,WAAW,SAAS,QAAQ,UAAU,OAAO;AAC1E,qBAAiB,UAAU,aAAa,WAAY;AAClD,UAAI,WAAW,WAAW,OAAO,KAAK,gBAAgB,IAAI;AAG1D,UAAI,KAAK,gBAAgB,SAAS,QAAQ,SAAS,IAAI,GAAG;AACxD,iBAAS,KAAK,SAAS;AAAA,MACzB;AAEA,UAAI,SAAS,WAAW,EAAG,QAAO;AAClC,aAAO,SACJ,IAAI,SAAU,UAAU;AACvB,YAAI,QAAQ,KAAK,QAAQ,KAAK;AAI9B,YAAI,aAAa,aAAa,KAAK,cAAc;AAC/C,mBAAS,QAAQ,KAAK,eAAe,KAAM,QAAQ,CAAC,EAAE,QAAQ,UAAU,EAAE;AAAA,QAC5E;AAEA,eAAO,QAAQ,MAAM;AAAA,MACvB,GAAG,IAAI,EACN,KAAK,GAAG;AAAA,IACb;AAEA,QAAI,0BAA0B;AAAA,MAC5B,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,QAAI,iBAAiB,CAAC,SAAS,UAAU,MAAM;AAC/C,QAAI,iBAAiB,CAAC,SAAS,WAAW,SAAS;AAEnD,qBAAiB,UAAU,cAAc,iBAAiB,UAAU,QAAQ,WAAY;AACtF,UAAI,WAAW,eACZ,IAAI,eAAe,IAAI,EACvB,KAAK,EAAE;AAEV,UAAI,WAAW,eACZ,IAAI,eAAe,IAAI,EACvB,KAAK,EAAE;AAEV,aAAO,MAAM,WAAW,MAAM;AAE9B,eAAS,cAAe,UAAU;AAChC,YAAI,QAAQ,KAAK,QAAQ,KAAK;AAI9B,YAAI,aAAa,aAAa,KAAK,cAAc;AAC/C,mBAAS,QAAQ,KAAK,eAAe,KAAM,QAAQ,CAAC,EAAE,QAAQ,OAAO,EAAE;AAAA,QACzE;AAEA,eAAO,QAAQ,wBAAwB,QAAQ;AAAA,MACjD;AAAA,IACF;AAEA,QAAI,SAAS;AACb,QAAI,OAAO,SAAS;AACpB,QAAI,QAAQ,SAAS;AACrB,QAAI,MAAM,SAAS;AACnB,QAAI,OAAO;AACX,QAAI,WAAW,IAAI,OAAO,CAAC,MAAM,OAAO,KAAK,IAAI,EAAE,IAAI,SAAU,aAAa;AAC5E,aAAO,MAAM,cAAc;AAAA,IAC7B,CAAC,EACE,KAAK,MAAM,CAAC;AAGf,QAAI,YAAY;AAAA,MACd,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,cAAc;AAAA,IAChB;AAEA,QAAI,YAAY,CAAC,SAAS,WAAW,WAAW,cAAc;AAE9D,aAAS,kBAAmB,UAAU;AAEpC,UAAI,eAAe,WAAW,SAAS,MAAM,SAAS,MAAM;AAC5D,aAAO,SAAS,cAAc,EAAE,IAAI;AAAA,IACtC;AAEA,aAAS,MAAO,UAAU;AACxB,UAAI,CAAC,SAAU,QAAO,CAAC;AACvB,UAAI,UAAU,SAAS,KAAK,QAAQ;AACpC,UAAI,aAAa,QAAQ,CAAC,MAAM;AAChC,aAAO,OAAO,KAAK,SAAS,EACzB,OAAO,SAAU,QAAQ,UAAU;AAClC,YAAI,WAAW,UAAU,QAAQ;AACjC,YAAI,QAAQ,QAAQ,QAAQ;AAE5B,YAAI,CAAC,MAAO,QAAO;AAGnB,gBAAQ,aAAa,iBACjB,kBAAkB,KAAK,IACvB,SAAS,OAAO,EAAE;AAEtB,YAAI,CAAC,MAAO,QAAO;AACnB,YAAI,cAAc,CAAC,UAAU,QAAQ,QAAQ,GAAG;AAC9C,mBAAS;AAAA,QACX;AACA,eAAO,QAAQ,IAAI;AACnB,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACT;AAAA;AAAA;;;AC5HA;AAAA;AAAA;AAEA,WAAO,UAAU,SAAS,WAAY,OAAO;AAC3C,UAAI,OAAO,KAAK,KAAK,GAAG;AAEtB,eAAO,IAAI,OAAO,MAAM,OAAO,CAAC,GAAG,KAAK;AAAA,MAC1C;AACA,UAAI,SAAS;AACb,UAAI,IAAI;AACR,aAAO,IAAI,MAAM,QAAQ;AACvB,YAAI,MAAM,CAAC,MAAM,MAAM;AACrB,oBAAU,MAAM,CAAC;AACjB,YAAE;AAAA,QACJ,OAAO;AACL,cAAI,WAAW,KAAK,MAAM,OAAO,IAAI,GAAG,CAAC,CAAC,GAAG;AAC3C,sBAAU,OAAO,aAAa,SAAS,MAAM,OAAO,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AACjE,iBAAK;AAAA,UACP,OAAO;AACL,gBAAI,cAAc;AAClB,mBAAO,IAAI,cAAc,MAAM,UAAU,MAAM,IAAI,WAAW,MAAM,MAAM;AACxE;AAAA,YACF;AACA,qBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,cAAc,CAAC,GAAG,EAAE,GAAG;AACpD,wBAAU;AAAA,YACZ;AACA,iBAAK,KAAK,MAAM,cAAc,CAAC,IAAI;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AACA,aAAO,IAAI,OAAO,QAAQ,QAAQ;AAAA,IACpC;AAAA;AAAA;;;AC9BA;AAAA;AAAA,QAAI,QAAQ;AACZ,QAAI,cAAc;AAClB,QAAI,YAAY;AAChB,QAAI,gBAAgB;AACpB,QAAI,aAAa;AAEjB,aAAS,UAAW,IAAI;AACtB,aAAO,SAAS,YAAa,OAAO;AAClC,YAAI,UAAU,KAAM,QAAO;AAC3B,eAAO,GAAG,KAAK;AAAA,MACjB;AAAA,IACF;AAEA,aAAS,UAAW,OAAO;AACzB,UAAI,UAAU,KAAM,QAAO;AAC3B,aAAO,UAAU,UACf,UAAU,OACV,UAAU,UACV,UAAU,OACV,UAAU,SACV,UAAU,QACV,UAAU;AAAA,IACd;AAEA,aAAS,eAAgB,OAAO;AAC9B,UAAI,CAAC,MAAO,QAAO;AACnB,aAAO,MAAM,MAAM,OAAO,SAAS;AAAA,IACrC;AAEA,aAAS,gBAAiB,QAAQ;AAChC,aAAO,SAAS,QAAQ,EAAE;AAAA,IAC5B;AAEA,aAAS,kBAAmB,OAAO;AACjC,UAAI,CAAC,MAAO,QAAO;AACnB,aAAO,MAAM,MAAM,OAAO,UAAU,eAAe,CAAC;AAAA,IACtD;AAEA,aAAS,qBAAsB,OAAO;AACpC,UAAI,CAAC,MAAO,QAAO;AACnB,aAAO,MAAM,MAAM,OAAO,UAAU,SAAU,OAAO;AACnD,eAAO,gBAAgB,KAAK,EAAE,KAAK;AAAA,MACrC,CAAC,CAAC;AAAA,IACJ;AAEA,QAAI,kBAAkB,SAAS,OAAO;AACpC,UAAG,CAAC,OAAO;AAAE,eAAO;AAAA,MAAM;AAC1B,UAAI,IAAI,YAAY,OAAO,OAAO,SAAS,OAAO;AAChD,YAAG,UAAU,MAAM;AACjB,kBAAQ,WAAW,KAAK;AAAA,QAC1B;AACA,eAAO;AAAA,MACT,CAAC;AAED,aAAO,EAAE,MAAM;AAAA,IACjB;AAEA,QAAI,kBAAkB,SAAS,OAAO;AACpC,UAAG,CAAC,OAAO;AAAE,eAAO;AAAA,MAAM;AAC1B,UAAI,IAAI,YAAY,OAAO,OAAO,SAAS,OAAO;AAChD,YAAG,UAAU,MAAM;AACjB,kBAAQ,WAAW,KAAK;AAAA,QAC1B;AACA,eAAO;AAAA,MACT,CAAC;AAED,aAAO,EAAE,MAAM;AAAA,IACjB;AAEA,QAAI,mBAAmB,SAAS,OAAO;AACrC,UAAG,CAAC,OAAO;AAAE,eAAO;AAAA,MAAM;AAE1B,UAAI,IAAI,YAAY,OAAO,KAAK;AAChC,aAAO,EAAE,MAAM;AAAA,IACjB;AAEA,QAAI,iBAAiB,SAAS,OAAO;AACnC,UAAI,CAAC,OAAO;AAAE,eAAO;AAAA,MAAM;AAE3B,UAAI,IAAI,YAAY,OAAO,OAAO,SAAS,OAAO;AAChD,YAAI,UAAU,MAAM;AAClB,kBAAQ,UAAU,KAAK;AAAA,QACzB;AACA,eAAO;AAAA,MACT,CAAC;AAED,aAAO,EAAE,MAAM;AAAA,IACjB;AAEA,QAAI,qBAAqB,SAAS,OAAO;AACvC,UAAI,CAAC,OAAO;AAAE,eAAO;AAAA,MAAM;AAE3B,UAAI,IAAI,YAAY,OAAO,OAAO,SAAS,OAAO;AAChD,YAAI,UAAU,MAAM;AAClB,kBAAQ,cAAc,KAAK;AAAA,QAC7B;AACA,eAAO;AAAA,MACT,CAAC;AAED,aAAO,EAAE,MAAM;AAAA,IACjB;AAEA,QAAI,kBAAkB,SAAS,OAAO;AACpC,UAAI,CAAC,OAAO;AAAE,eAAO;AAAA,MAAM;AAE3B,aAAO,MAAM,MAAM,OAAO,UAAU,UAAU,CAAC;AAAA,IACjD;AAEA,QAAI,eAAe,SAAS,OAAO;AACjC,aAAO,SAAS,OAAO,EAAE;AAAA,IAC3B;AAEA,QAAI,kBAAkB,SAAS,OAAO;AACpC,UAAI,SAAS,OAAO,KAAK;AACzB,UAAI,QAAQ,KAAK,MAAM,GAAG;AAAE,eAAO;AAAA,MAAQ;AAC3C,aAAO;AAAA,IACT;AAEA,QAAI,iBAAiB,SAAS,OAAO;AACnC,UAAI,CAAC,OAAO;AAAE,eAAO;AAAA,MAAM;AAE3B,aAAO,MAAM,MAAM,OAAO,UAAU,KAAK,KAAK,CAAC;AAAA,IACjD;AAEA,QAAI,aAAa,SAAS,OAAO;AAC/B,UAAI,MAAM,CAAC,MAAM,KAAK;AAAE,eAAO;AAAA,MAAM;AAErC,cAAQ,MAAM,UAAW,GAAG,MAAM,SAAS,CAAE,EAAE,MAAM,GAAG;AAExD,aAAO;AAAA,QACL,GAAG,WAAW,MAAM,CAAC,CAAC;AAAA,QACtB,GAAG,WAAW,MAAM,CAAC,CAAC;AAAA,MACxB;AAAA,IACF;AAEA,QAAI,cAAc,SAAS,OAAO;AAChC,UAAI,MAAM,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM,KAAK;AAAE,eAAO;AAAA,MAAM;AAEzD,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb,UAAI,cAAc;AAClB,eAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAI;AACxC,YAAI,CAAC,aAAa;AAChB,mBAAS,MAAM,CAAC;AAAA,QAClB;AAEA,YAAI,MAAM,CAAC,MAAM,KAAK;AACpB,wBAAc;AACd;AAAA,QACF,WAAW,CAAC,aAAa;AACvB;AAAA,QACF;AAEA,YAAI,MAAM,CAAC,MAAM,KAAI;AACnB;AAAA,QACF;AAEA,kBAAU,MAAM,CAAC;AAAA,MACnB;AACA,UAAI,SAAS,WAAW,KAAK;AAC7B,aAAO,SAAS,WAAW,MAAM;AAEjC,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,SAAS,UAAU;AAC5B,eAAS,IAAI,eAAe;AAC5B,eAAS,IAAI,YAAY;AACzB,eAAS,IAAI,YAAY;AACzB,eAAS,IAAI,YAAY;AACzB,eAAS,KAAK,UAAU;AACxB,eAAS,KAAK,UAAU;AACxB,eAAS,IAAI,SAAS;AACtB,eAAS,MAAM,SAAS;AACxB,eAAS,MAAM,SAAS;AACxB,eAAS,MAAM,SAAS;AACxB,eAAS,KAAK,UAAU;AACxB,eAAS,KAAK,gBAAgB;AAC9B,eAAS,KAAK,WAAW;AACzB,eAAS,KAAM,cAAc;AAC7B,eAAS,MAAM,eAAe;AAC9B,eAAS,MAAM,iBAAiB;AAChC,eAAS,MAAM,iBAAiB;AAChC,eAAS,MAAM,iBAAiB;AAChC,eAAS,MAAM,oBAAoB;AACnC,eAAS,MAAM,eAAe;AAC9B,eAAS,MAAM,eAAe;AAC9B,eAAS,MAAM,eAAe;AAC9B,eAAS,MAAM,eAAe;AAC9B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,cAAc;AAC7B,eAAS,MAAM,cAAc;AAC7B,eAAS,MAAM,cAAc;AAC7B,eAAS,MAAM,aAAa;AAC5B,eAAS,MAAM,kBAAkB;AACjC,eAAS,IAAI,UAAU;AACvB,eAAS,KAAK,KAAK,MAAM,KAAK,IAAI,CAAC;AACnC,eAAS,MAAM,KAAK,MAAM,KAAK,IAAI,CAAC;AACpC,eAAS,KAAK,cAAc;AAC5B,eAAS,MAAM,cAAc;AAC7B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,KAAK,gBAAgB;AAC9B,eAAS,MAAM,gBAAgB;AAC/B,eAAS,MAAM,gBAAgB;AAAA,IACjC;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,IACF;AAAA;AAAA;;;ACtNA;AAAA;AAAA;AAGA,QAAI,OAAO;AAEX,aAAS,SAAS,QAAQ;AACzB,UAAI,OAAO,OAAO,YAAY,CAAC;AAC/B,UAAI,MAAM,OAAO,aAAa,CAAC;AAC/B,UAAI,OAAO;AAEX,UAAI,OAAO,GAAG;AACb,eAAO,CAAC,QAAQ,QAAQ;AACxB,cAAO,CAAC,MAAM,MAAO;AACrB,eAAO;AAAA,MACR;AAEA,UAAI,SAAS;AACb,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ;AACC,gBAAQ,OAAO;AACf,eAAO,OAAO,SAAS;AAEvB,YAAI,aAAc,QAAQ;AAC1B,cAAM,IAAI,SAAS;AACnB,iBAAS,MAAM,IAAI,OAAO;AAE1B,YAAI,QAAQ,KAAK,SAAS,GAAG;AAC5B,iBAAO,OAAO,SAAS;AAAA,QACxB;AAEA,cAAM;AACN,YAAI,IAAI,OAAO;AAEf,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,iBAAO;AAAA,QACR;AAEA,iBAAS,MAAM,SAAS;AAAA,MACzB;AAEA;AACC,gBAAQ,OAAO;AACf,eAAO,OAAO,SAAS;AAEvB,YAAI,aAAc,QAAQ;AAC1B,cAAM,IAAI,SAAS;AACnB,iBAAS,MAAM,IAAI,OAAO;AAE1B,YAAI,QAAQ,KAAK,SAAS,GAAG;AAC5B,iBAAO,OAAO,SAAS;AAAA,QACxB;AAEA,cAAM;AACN,YAAI,IAAI,OAAO;AAEf,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,iBAAO;AAAA,QACR;AAEA,iBAAS,MAAM,SAAS;AAAA,MACzB;AAEA;AACC,gBAAQ,OAAO;AACf,eAAO,OAAO,SAAS;AAEvB,YAAI,aAAc,QAAQ;AAC1B,cAAM,IAAI,SAAS;AACnB,iBAAS,MAAM,IAAI,OAAO;AAE1B,YAAI,QAAQ,KAAK,SAAS,GAAG;AAC5B,iBAAO,OAAO,SAAS;AAAA,QACxB;AAEA,cAAM;AACN,YAAI,IAAI,OAAO;AAEf,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,iBAAO;AAAA,QACR;AAEA,iBAAS,MAAM,SAAS;AAAA,MACzB;AAEA;AACC,gBAAQ,OAAO;AACf,YAAI,aAAc,QAAQ;AAC1B,iBAAS,KAAK,IAAI;AAElB,eAAO,OAAO,SAAS;AAAA,MACxB;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnGjB;AAAA;AAAA,QAAI,aAAa;AAEjB,QAAI,YAAY,SAAS,MAAM,MAAM,QAAQ,QAAQ,UAAU;AAC7D,eAAS,UAAU;AACnB,eAAS,UAAU;AACnB,iBAAW,YAAY,SAAS,WAAW,UAAUA,OAAM;AAAE,eAAQ,YAAY,KAAK,IAAI,GAAGA,KAAI,IAAK;AAAA,MAAU;AAChH,UAAI,cAAc,UAAU;AAE5B,UAAI,MAAM,SAAS,OAAO;AACxB,YAAI,QAAQ;AACV,iBAAO,CAAC,QAAQ;AAAA,QAClB;AAEA,eAAO;AAAA,MACT;AAGA,UAAI,OAAO;AACX,UAAI,YAAY,IAAK,SAAS;AAC9B,UAAI,OAAO,WAAW;AACpB,eAAQ,OAAS,IAAI,OAAS;AAC9B,oBAAY;AAAA,MACd;AAEA,UAAI,QAAQ;AACV,eAAO,QAAS,SAAS;AAAA,MAC3B;AAEA,UAAI,SAAS;AACb,UAAK,SAAS,IAAK,QAAQ,GAAG;AAC5B,iBAAS,SAAS,GAAG,IAAI,KAAK,WAAW,CAAC,IAAI,MAAM,SAAS;AAAA,MAC/D;AAGA,UAAI,QAAS,OAAO,UAAW;AAC/B,eAAS,IAAI,cAAc,GAAG,IAAI,OAAO,KAAK;AAC5C,iBAAS,SAAS,QAAQ,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC;AAAA,MAC3C;AAGA,UAAI,YAAY,OAAO,UAAU;AACjC,UAAI,WAAW,GAAG;AAChB,iBAAS,SAAS,QAAQ,IAAI,KAAK,KAAK,CAAC,KAAM,IAAI,UAAW,QAAQ;AAAA,MACxE;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,qBAAqB,SAAS,MAAM,eAAe,cAAc;AACnE,UAAI,OAAO,KAAK,IAAI,GAAG,eAAe,CAAC,IAAI;AAC3C,UAAI,OAAO,UAAU,MAAM,CAAC;AAC5B,UAAI,WAAW,UAAU,MAAM,cAAc,CAAC;AAE9C,UAAI,aAAa,GAAG;AAClB,eAAO;AAAA,MACT;AAGA,UAAI,uBAAuB;AAC3B,UAAI,qBAAqB,SAAS,WAAW,UAAU,MAAM;AAC3D,YAAI,cAAc,GAAG;AACnB,sBAAY;AAAA,QACd;AAEA,iBAAS,IAAI,GAAG,KAAK,MAAM,KAAK;AAC9B,kCAAwB;AACxB,eAAK,WAAY,KAAQ,OAAO,KAAO,GAAG;AACxC,yBAAa;AAAA,UACf;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,UAAI,WAAW,UAAU,MAAM,eAAe,eAAe,GAAG,OAAO,kBAAkB;AAGzF,UAAI,YAAa,KAAK,IAAI,GAAG,eAAe,CAAC,IAAI,GAAI;AACnD,YAAI,aAAa,GAAG;AAClB,iBAAQ,SAAS,IAAK,WAAW;AAAA,QACnC;AAEA,eAAO;AAAA,MACT;AAGA,cAAS,SAAS,IAAK,IAAI,MAAM,KAAK,IAAI,GAAG,WAAW,IAAI,IAAI;AAAA,IAClE;AAEA,QAAI,aAAa,SAAS,OAAO;AAC/B,UAAI,UAAU,OAAO,CAAC,KAAK,GAAG;AAC5B,eAAO,MAAM,UAAU,OAAO,IAAI,GAAG,IAAI,IAAI;AAAA,MAC/C;AAEA,aAAO,UAAU,OAAO,IAAI,CAAC;AAAA,IAC/B;AAEA,QAAI,aAAa,SAAS,OAAO;AAC/B,UAAI,UAAU,OAAO,CAAC,KAAK,GAAG;AAC5B,eAAO,MAAM,UAAU,OAAO,IAAI,GAAG,IAAI,IAAI;AAAA,MAC/C;AAEA,aAAO,UAAU,OAAO,IAAI,CAAC;AAAA,IAC/B;AAEA,QAAI,eAAe,SAAS,OAAO;AACjC,aAAO,mBAAmB,OAAO,IAAI,CAAC;AAAA,IACxC;AAEA,QAAI,eAAe,SAAS,OAAO;AACjC,aAAO,mBAAmB,OAAO,IAAI,EAAE;AAAA,IACzC;AAEA,QAAI,eAAe,SAAS,OAAO;AACjC,UAAI,OAAO,UAAU,OAAO,IAAI,EAAE;AAClC,UAAI,QAAQ,OAAQ;AAClB,eAAO;AAAA,MACT;AAEA,UAAI,SAAS,KAAK,IAAI,KAAO,UAAU,OAAO,IAAI,EAAE,CAAC;AACrD,UAAI,SAAS;AAEb,UAAI,SAAS,CAAC;AACd,UAAI,UAAU,UAAU,OAAO,EAAE;AACjC,eAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,kBAAU,UAAU,OAAO,IAAI,KAAM,KAAK,CAAE,IAAI;AAChD,kBAAU;AAAA,MACZ;AAEA,UAAI,QAAQ,KAAK,IAAI,IAAI,UAAU,OAAO,IAAI,EAAE,CAAC;AACjD,cAAS,SAAS,IAAK,IAAI,MAAM,KAAK,MAAM,SAAS,KAAK,IAAI;AAAA,IAChE;AAEA,QAAI,YAAY,SAAS,OAAO,OAAO;AACrC,UAAI,OAAO,UAAU,OAAO,CAAC;AAC7B,UAAI,WAAW,UAAU,OAAO,IAAI,CAAC;AAGrC,UAAI,SAAS,IAAI,MAAQ,SAAS,IAAK,IAAI,MAAM,WAAW,MAAQ,SAAY;AAEhF,UAAI,CAAC,OAAO;AACV,eAAO,QAAQ,OAAO,QAAQ,IAAI,OAAO,kBAAkB,IAAI,GAAK;AAAA,MACtE;AAGA,aAAO,OAAO,WAAW;AACzB,aAAO,kBAAkB,WAAW;AAClC,eAAO,KAAK;AAAA,MACd;AACA,aAAO,kBAAkB,SAASC,QAAO;AACvC,aAAK,OAAOA;AAAA,MACd;AACA,aAAO,qBAAqB,WAAW;AACrC,eAAO,KAAK;AAAA,MACd;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,aAAa,SAAS,OAAO;AAC/B,UAAI,MAAM,UAAU,OAAO,EAAE;AAE7B,UAAI,QAAQ,UAAU,OAAO,IAAI,EAAE;AACnC,UAAI,cAAc,UAAU,OAAO,IAAI,EAAE;AAEzC,UAAI,SAAS;AACb,UAAI,OAAO,CAAC;AACZ,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAE5B,aAAK,CAAC,IAAI,UAAU,OAAO,IAAI,MAAM;AACrC,kBAAU;AAGV,kBAAU;AAAA,MACZ;AAEA,UAAI,eAAe,SAASC,cAAa;AAEvC,YAAI,SAAS,UAAU,OAAO,IAAI,MAAM;AACxC,kBAAU;AAGV,YAAI,UAAU,YAAY;AACxB,iBAAO;AAAA,QACT;AAEA,YAAI;AACJ,YAAKA,gBAAe,MAAUA,gBAAe,IAAO;AAElD,mBAAS,UAAU,OAAO,SAAS,GAAG,MAAM;AAC5C,oBAAU,SAAS;AACnB,iBAAO;AAAA,QACT,WACSA,gBAAe,IAAM;AAE5B,mBAAS,MAAM,SAAS,KAAK,UAAU,UAAU,IAAI,UAAW,UAAU,MAAO,CAAC;AAClF,iBAAO;AAAA,QACT,OACK;AACH,kBAAQ,IAAI,yCAAyCA,YAAW;AAAA,QAClE;AAAA,MACF;AAEA,UAAI,QAAQ,SAAS,WAAWA,cAAa;AAC3C,YAAI,QAAQ,CAAC;AACb,YAAIC;AAEJ,YAAI,UAAU,SAAS,GAAG;AACxB,cAAI,QAAQ,UAAU,MAAM;AAC5B,eAAKA,KAAI,GAAGA,KAAI,OAAOA,MAAK;AAC1B,kBAAMA,EAAC,IAAI,MAAM,WAAWD,YAAW;AAAA,UACzC;AACA,oBAAU,QAAQ,KAAK;AAAA,QACzB,OACK;AACH,eAAKC,KAAI,GAAGA,KAAI,UAAU,CAAC,GAAGA,MAAK;AACjC,kBAAMA,EAAC,IAAI,aAAaD,YAAW;AAAA,UACrC;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,aAAO,MAAM,MAAM,WAAW;AAAA,IAChC;AAEA,QAAI,YAAY,SAAS,OAAO;AAC9B,aAAO,MAAM,SAAS,MAAM;AAAA,IAC9B;AAEA,QAAI,YAAY,SAAS,OAAO;AAC9B,UAAG,UAAU,KAAM,QAAO;AAC1B,aAAQ,UAAU,OAAO,CAAC,IAAI;AAAA,IAChC;AAEA,QAAI,OAAO,SAAS,UAAU;AAC5B,eAAS,IAAI,UAAU;AACvB,eAAS,IAAI,UAAU;AACvB,eAAS,IAAI,UAAU;AACvB,eAAS,IAAI,UAAU;AACvB,eAAS,MAAM,YAAY;AAC3B,eAAS,KAAK,YAAY;AAC1B,eAAS,KAAK,YAAY;AAC1B,eAAS,IAAI,SAAS;AACtB,eAAS,MAAM,UAAU,KAAK,MAAM,KAAK,CAAC;AAC1C,eAAS,MAAM,UAAU,KAAK,MAAM,IAAI,CAAC;AACzC,eAAS,KAAM,UAAU;AACzB,eAAS,MAAM,UAAU;AACzB,eAAS,MAAM,UAAU;AACzB,eAAS,MAAM,UAAU;AACzB,eAAS,MAAM,UAAU;AACzB,eAAS,IAAI,SAAS;AAAA,IACxB;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,IACF;AAAA;AAAA;;;AChQA;AAAA;AAWA,WAAO,UAAU;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,KAAK;AAAA,MACL,cAAc;AAAA,MACd,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,MACP,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,MACX,aAAa;AAAA,MACb,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,SAAS;AAAA,MACT,MAAM;AAAA,MACN,eAAe;AAAA,MACf,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,UAAU;AAAA,MACV,SAAS;AAAA,MACT,WAAW;AAAA,MACX,WAAW;AAAA,MACX,eAAe;AAAA,MACf,OAAO;AAAA,MACP,cAAc;AAAA,MACd,SAAS;AAAA,IACb;AAAA;AAAA;;;ACxEA;AAAA;AAAA,QAAI,cAAc;AAClB,QAAI,gBAAgB;AACpB,QAAI,cAAc;AAClB,QAAI,eAAe;AAEnB,YAAQ,gBAAgB;AACxB,YAAQ,gBAAgB;AACxB,YAAQ,cAAc;AACtB,YAAQ,WAAW;AAEnB,QAAI,cAAc;AAAA,MAChB,MAAM,CAAC;AAAA,MACP,QAAQ,CAAC;AAAA,IACX;AAGA,aAAS,QAAS,KAAK;AACrB,aAAO,OAAO,GAAG;AAAA,IACnB;AAMA,aAAS,cAAe,KAAK,QAAQ;AACnC,eAAS,UAAU;AACnB,UAAI,CAAC,YAAY,MAAM,GAAG;AACxB,eAAO;AAAA,MACT;AACA,aAAO,YAAY,MAAM,EAAE,GAAG,KAAK;AAAA,IACrC;AAEA,aAAS,cAAe,KAAK,QAAQ,SAAS;AAC5C,UAAG,OAAO,UAAU,YAAY;AAC9B,kBAAU;AACV,iBAAS;AAAA,MACX;AACA,kBAAY,MAAM,EAAE,GAAG,IAAI;AAAA,IAC7B;AAEA,gBAAY,KAAK,SAAS,KAAK,WAAW;AACxC,kBAAY,KAAK,GAAG,IAAI;AAAA,IAC1B,CAAC;AAED,kBAAc,KAAK,SAAS,KAAK,WAAW;AAC1C,kBAAY,OAAO,GAAG,IAAI;AAAA,IAC5B,CAAC;AAAA;AAAA;;;AC9CD;AAAA;AAAA;AAEA,WAAO,UAAU;AAAA;AAAA,MAEf,MAAM;AAAA;AAAA,MAGN,MAAM,QAAQ,aAAa,UAAU,QAAQ,IAAI,WAAW,QAAQ,IAAI;AAAA;AAAA,MAGxE,UAAU;AAAA;AAAA,MAGV,UAAU;AAAA;AAAA;AAAA;AAAA,MAKV,kBAAkB;AAAA;AAAA,MAGlB,MAAM;AAAA;AAAA;AAAA,MAIN,MAAM;AAAA;AAAA,MAGN,QAAQ;AAAA;AAAA;AAAA;AAAA,MAMR,KAAK;AAAA;AAAA;AAAA,MAIL,mBAAmB;AAAA,MAEnB,iBAAiB;AAAA,MAEjB,KAAK;AAAA,MAEL,kBAAkB;AAAA,MAElB,2BAA2B;AAAA,MAE3B,SAAS;AAAA,MAET,sBAAsB;AAAA;AAAA;AAAA,MAItB,mBAAmB;AAAA;AAAA;AAAA,MAInB,cAAc;AAAA;AAAA;AAAA,MAId,qCAAqC;AAAA;AAAA,MAGrC,eAAe;AAAA,MAEf,iBAAiB;AAAA,MAEjB,YAAY;AAAA,MAEZ,iBAAiB;AAAA,IACnB;AAEA,QAAM,UAAU;AAEhB,QAAM,kBAAkB,QAAQ,cAAc,IAAI,MAAM;AACxD,QAAM,uBAAuB,QAAQ,cAAc,MAAM,MAAM;AAG/D,WAAO,QAAQ,iBAAiB,aAAa,SAAU,KAAK;AAC1D,cAAQ,cAAc,IAAI,QAAQ,MAAM,QAAQ,cAAc,IAAI,MAAM,IAAI,eAAe;AAC3F,cAAQ,cAAc,MAAM,QAAQ,MAAM,QAAQ,cAAc,MAAM,MAAM,IAAI,oBAAoB;AAAA,IACtG,CAAC;AAAA;AAAA;;;ACnFD;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,sFAAsF,GAAG,mIAAmI;AAAA,QAC3O;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA;AAEA,QAAME,YAAW;AAEjB,QAAM,OAAO;AACb,QAAM,EAAE,OAAO,IAAI,KAAK,SAAS;AAEjC,aAAS,cAAc,uBAAuB;AAC5C,YAAM,UAAU,sBAAsB,QAAQ,OAAO,MAAM,EAAE,QAAQ,MAAM,KAAK;AAEhF,aAAO,MAAM,UAAU;AAAA,IACzB;AAKA,aAAS,YAAY,KAAK;AACxB,UAAI,SAAS;AACb,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAI,IAAI,GAAG;AACT,mBAAS,SAAS;AAAA,QACpB;AACA,YAAI,IAAI,CAAC,MAAM,QAAQ,OAAO,IAAI,CAAC,MAAM,aAAa;AACpD,mBAAS,SAAS;AAAA,QACpB,WAAW,MAAM,QAAQ,IAAI,CAAC,CAAC,GAAG;AAChC,mBAAS,SAAS,YAAY,IAAI,CAAC,CAAC;AAAA,QACtC,WAAW,YAAY,OAAO,IAAI,CAAC,CAAC,GAAG;AACrC,cAAI,OAAO,IAAI,CAAC;AAChB,cAAI,EAAE,gBAAgB,SAAS;AAC7B,kBAAM,MAAM,OAAO,KAAK,KAAK,QAAQ,KAAK,YAAY,KAAK,UAAU;AACrE,gBAAI,IAAI,WAAW,KAAK,YAAY;AAClC,qBAAO;AAAA,YACT,OAAO;AACL,qBAAO,IAAI,MAAM,KAAK,YAAY,KAAK,aAAa,KAAK,UAAU;AAAA,YACrE;AAAA,UACF;AACA,oBAAU,UAAU,KAAK,SAAS,KAAK;AAAA,QACzC,OAAO;AACL,oBAAU,cAAc,aAAa,IAAI,CAAC,CAAC,CAAC;AAAA,QAC9C;AAAA,MACF;AACA,eAAS,SAAS;AAClB,aAAO;AAAA,IACT;AAMA,QAAM,eAAe,SAAU,KAAK,MAAM;AAExC,UAAI,OAAO,MAAM;AACf,eAAO;AAAA,MACT;AACA,UAAI,OAAO,QAAQ,UAAU;AAC3B,YAAI,eAAe,QAAQ;AACzB,iBAAO;AAAA,QACT;AACA,YAAI,YAAY,OAAO,GAAG,GAAG;AAC3B,gBAAM,MAAM,OAAO,KAAK,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AAClE,cAAI,IAAI,WAAW,IAAI,YAAY;AACjC,mBAAO;AAAA,UACT;AACA,iBAAO,IAAI,MAAM,IAAI,YAAY,IAAI,aAAa,IAAI,UAAU;AAAA,QAClE;AACA,YAAI,OAAO,GAAG,GAAG;AACf,cAAIA,UAAS,sBAAsB;AACjC,mBAAO,gBAAgB,GAAG;AAAA,UAC5B,OAAO;AACL,mBAAO,aAAa,GAAG;AAAA,UACzB;AAAA,QACF;AACA,YAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,iBAAO,YAAY,GAAG;AAAA,QACxB;AAEA,eAAO,cAAc,KAAK,IAAI;AAAA,MAChC;AACA,aAAO,IAAI,SAAS;AAAA,IACtB;AAEA,aAAS,cAAc,KAAK,MAAM;AAChC,UAAI,OAAO,OAAO,IAAI,eAAe,YAAY;AAC/C,eAAO,QAAQ,CAAC;AAChB,YAAI,KAAK,QAAQ,GAAG,MAAM,IAAI;AAC5B,gBAAM,IAAI,MAAM,kDAAkD,MAAM,aAAa;AAAA,QACvF;AACA,aAAK,KAAK,GAAG;AAEb,eAAO,aAAa,IAAI,WAAW,YAAY,GAAG,IAAI;AAAA,MACxD;AACA,aAAO,KAAK,UAAU,GAAG;AAAA,IAC3B;AAEA,aAAS,aAAa,MAAM;AAC1B,UAAI,SAAS,CAAC,KAAK,kBAAkB;AAErC,UAAI,OAAO,KAAK,YAAY;AAC5B,YAAM,WAAW,OAAO;AACxB,UAAI,SAAU,QAAO,KAAK,IAAI,IAAI,IAAI;AAEtC,UAAI,MACF,OAAO,IAAI,EAAE,SAAS,GAAG,GAAG,IAC5B,MACA,OAAO,KAAK,SAAS,IAAI,CAAC,EAAE,SAAS,GAAG,GAAG,IAC3C,MACA,OAAO,KAAK,QAAQ,CAAC,EAAE,SAAS,GAAG,GAAG,IACtC,MACA,OAAO,KAAK,SAAS,CAAC,EAAE,SAAS,GAAG,GAAG,IACvC,MACA,OAAO,KAAK,WAAW,CAAC,EAAE,SAAS,GAAG,GAAG,IACzC,MACA,OAAO,KAAK,WAAW,CAAC,EAAE,SAAS,GAAG,GAAG,IACzC,MACA,OAAO,KAAK,gBAAgB,CAAC,EAAE,SAAS,GAAG,GAAG;AAEhD,UAAI,SAAS,GAAG;AACd,eAAO;AACP,kBAAU;AAAA,MACZ,OAAO;AACL,eAAO;AAAA,MACT;AAEA,aAAO,OAAO,KAAK,MAAM,SAAS,EAAE,CAAC,EAAE,SAAS,GAAG,GAAG,IAAI,MAAM,OAAO,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AACnG,UAAI,SAAU,QAAO;AACrB,aAAO;AAAA,IACT;AAEA,aAAS,gBAAgB,MAAM;AAC7B,UAAI,OAAO,KAAK,eAAe;AAC/B,YAAM,WAAW,OAAO;AACxB,UAAI,SAAU,QAAO,KAAK,IAAI,IAAI,IAAI;AAEtC,UAAI,MACF,OAAO,IAAI,EAAE,SAAS,GAAG,GAAG,IAC5B,MACA,OAAO,KAAK,YAAY,IAAI,CAAC,EAAE,SAAS,GAAG,GAAG,IAC9C,MACA,OAAO,KAAK,WAAW,CAAC,EAAE,SAAS,GAAG,GAAG,IACzC,MACA,OAAO,KAAK,YAAY,CAAC,EAAE,SAAS,GAAG,GAAG,IAC1C,MACA,OAAO,KAAK,cAAc,CAAC,EAAE,SAAS,GAAG,GAAG,IAC5C,MACA,OAAO,KAAK,cAAc,CAAC,EAAE,SAAS,GAAG,GAAG,IAC5C,MACA,OAAO,KAAK,mBAAmB,CAAC,EAAE,SAAS,GAAG,GAAG;AAEnD,aAAO;AACP,UAAI,SAAU,QAAO;AACrB,aAAO;AAAA,IACT;AAEA,aAAS,qBAAqB,QAAQ,QAAQ,UAAU;AAEtD,eAAS,OAAO,WAAW,WAAW,EAAE,MAAM,OAAO,IAAI;AACzD,UAAI,QAAQ;AACV,YAAI,OAAO,WAAW,YAAY;AAChC,iBAAO,WAAW;AAAA,QACpB,OAAO;AACL,iBAAO,SAAS;AAAA,QAClB;AAAA,MACF;AACA,UAAI,UAAU;AACZ,eAAO,WAAW;AAAA,MACpB;AACA,aAAO;AAAA,IACT;AAGA,QAAMC,oBAAmB,SAAU,KAAK;AACtC,aAAO,MAAM,IAAI,QAAQ,MAAM,IAAI,IAAI;AAAA,IACzC;AAEA,QAAMC,iBAAgB,SAAU,KAAK;AACnC,UAAI,eAAe;AACnB,UAAI,UAAU;AAEd,UAAI,OAAO,MAAM;AACf,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO;AAAA,MACT;AAEA,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAM,IAAI,IAAI,CAAC;AACf,YAAI,MAAM,KAAK;AACb,qBAAW,IAAI;AAAA,QACjB,WAAW,MAAM,MAAM;AACrB,qBAAW,IAAI;AACf,yBAAe;AAAA,QACjB,OAAO;AACL,qBAAW;AAAA,QACb;AAAA,MACF;AAEA,iBAAW;AAEX,UAAI,iBAAiB,MAAM;AACzB,kBAAU,OAAO;AAAA,MACnB;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA,MACf,cAAc,SAAS,oBAAoB,OAAO;AAGhD,eAAO,aAAa,KAAK;AAAA,MAC3B;AAAA,MACA;AAAA,MACA,kBAAAD;AAAA,MACA,eAAAC;AAAA,IACF;AAAA;AAAA;;;ACxNA;AAAA;AAAA;AAIA,QAAM,aAAa;AAEnB,aAAS,IAAI,QAAQ;AACnB,aAAO,WAAW,WAAW,KAAK,EAAE,OAAO,QAAQ,OAAO,EAAE,OAAO,KAAK;AAAA,IAC1E;AAGA,aAAS,wBAAwB,MAAM,UAAU,MAAM;AACrD,YAAM,QAAQ,IAAI,WAAW,IAAI;AACjC,YAAM,QAAQ,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,KAAK,GAAG,IAAI,CAAC,CAAC;AAC3D,aAAO,QAAQ;AAAA,IACjB;AAEA,aAAS,OAAO,MAAM;AACpB,aAAO,WAAW,WAAW,QAAQ,EAAE,OAAO,IAAI,EAAE,OAAO;AAAA,IAC7D;AAEA,aAAS,WAAW,UAAU,MAAM;AAClC,iBAAW,SAAS,QAAQ,SAAS,IAAI;AACzC,aAAO,WAAW,WAAW,QAAQ,EAAE,OAAO,IAAI,EAAE,OAAO;AAAA,IAC7D;AAEA,aAAS,WAAW,KAAK,KAAK;AAC5B,aAAO,WAAW,WAAW,UAAU,GAAG,EAAE,OAAO,GAAG,EAAE,OAAO;AAAA,IACjE;AAEA,mBAAe,UAAU,UAAU,MAAM,YAAY;AACnD,aAAO,WAAW,WAAW,UAAU,MAAM,YAAY,IAAI,QAAQ;AAAA,IACvE;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA,aAAa,WAAW;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AC1CA;AAAA;AAAA,QAAM,aAAa;AAEnB,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAOA,QAAM,YAAY,WAAW,aAAa,WAAW;AAKrD,QAAM,eAAe,UAAU;AAC/B,QAAM,cAAc,IAAI,YAAY;AAOpC,aAAS,YAAY,QAAQ;AAC3B,aAAO,UAAU,gBAAgB,OAAO,MAAM,MAAM,CAAC;AAAA,IACvD;AAEA,mBAAe,IAAI,QAAQ;AACzB,UAAI;AACF,eAAO,WAAW,WAAW,KAAK,EAAE,OAAO,QAAQ,OAAO,EAAE,OAAO,KAAK;AAAA,MAC1E,SAAS,GAAG;AAIV,cAAM,OAAO,OAAO,WAAW,WAAW,YAAY,OAAO,MAAM,IAAI;AACvE,cAAM,OAAO,MAAM,aAAa,OAAO,OAAO,IAAI;AAClD,eAAO,MAAM,KAAK,IAAI,WAAW,IAAI,CAAC,EACnC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAC1C,KAAK,EAAE;AAAA,MACZ;AAAA,IACF;AAGA,mBAAe,wBAAwB,MAAM,UAAU,MAAM;AAC3D,YAAM,QAAQ,MAAM,IAAI,WAAW,IAAI;AACvC,YAAM,QAAQ,MAAM,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,KAAK,GAAG,IAAI,CAAC,CAAC;AACjE,aAAO,QAAQ;AAAA,IACjB;AAMA,mBAAe,OAAO,MAAM;AAC1B,aAAO,MAAM,aAAa,OAAO,WAAW,IAAI;AAAA,IAClD;AAEA,mBAAe,WAAW,UAAU,MAAM;AACxC,aAAO,MAAM,aAAa,OAAO,UAAU,IAAI;AAAA,IACjD;AAOA,mBAAe,WAAW,WAAW,KAAK;AACxC,YAAM,MAAM,MAAM,aAAa,UAAU,OAAO,WAAW,EAAE,MAAM,QAAQ,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;AAC7G,aAAO,MAAM,aAAa,KAAK,QAAQ,KAAK,YAAY,OAAO,GAAG,CAAC;AAAA,IACrE;AAQA,mBAAe,UAAU,UAAU,MAAM,YAAY;AACnD,YAAM,MAAM,MAAM,aAAa,UAAU,OAAO,YAAY,OAAO,QAAQ,GAAG,UAAU,OAAO,CAAC,YAAY,CAAC;AAC7G,YAAM,SAAS,EAAE,MAAM,UAAU,MAAM,WAAW,MAAY,WAAuB;AACrF,aAAO,MAAM,aAAa,WAAW,QAAQ,KAAK,KAAK,GAAG,CAAC,YAAY,CAAC;AAAA,IAC1E;AAAA;AAAA;;;ACxFA,IAAAC,iBAAA;AAAA;AAAA;AAEA,QAAM,kBAAkB,SAAS,QAAQ,YAAY,QAAQ,SAAS,QAAQ,QAAQ,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC,IAAI;AACrH,QAAI,iBAAiB;AAEnB,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACRA;AAAA;AAAA,aAAS,UAAU,KAAK,MAAM;AAC5B,aAAO,IAAI,MAAM,2BAA2B,MAAM,sCAAsC,KAAK,SAAS,QAAQ,CAAC;AAAA,IACjH;AAEA,aAAS,eAAe,MAAM,OAAO;AACnC,UAAI,SAAS,KAAK,OAAO;AACzB,UAAI,SAAS,IAAM,QAAO,EAAE,QAAQ,MAAM;AAE1C,YAAM,cAAc,SAAS;AAC7B,UAAI,cAAc,EAAG,OAAM,UAAU,cAAc,IAAI;AAEvD,eAAS;AACT,eAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,iBAAU,UAAU,IAAK,KAAK,OAAO;AAAA,MACvC;AAEA,aAAO,EAAE,QAAQ,MAAM;AAAA,IACzB;AAEA,aAAS,YAAY,MAAM,OAAO;AAChC,UAAI,KAAK,OAAO,MAAM,EAAK,OAAM,UAAU,gBAAgB,IAAI;AAE/D,YAAM,EAAE,QAAQ,WAAW,OAAO,oBAAoB,IAAI,eAAe,MAAM,KAAK;AACpF,cAAQ;AACR,YAAM,YAAY,QAAQ;AAE1B,YAAM,QAAQ,KAAK,OAAO;AAC1B,UAAI,OAAQ,QAAQ,MAAO,KAAK,MAAO,QAAQ;AAE/C,aAAO,QAAQ,WAAW;AAExB,YAAI,QAAQ;AACZ,eAAO,QAAQ,WAAW;AAExB,gBAAM,WAAW,KAAK,OAAO;AAC7B,kBAAS,SAAS,IAAM,WAAW;AACnC,cAAI,WAAW,IAAM;AAAA,QACvB;AACA,eAAO,MAAM;AAAA,MACf;AAEA,aAAO,EAAE,KAAK,MAAM;AAAA,IACtB;AAEA,aAAS,cAAc,MAAM,OAAO;AAClC,UAAI,KAAK,OAAO,MAAM,GAAM,OAAM,UAAU,qBAAqB,IAAI;AACrE,aAAO,eAAe,MAAM,KAAK;AAAA,IACnC;AAEA,aAAS,sCAAsC,MAAM,OAAO;AAE1D,UAAI,UAAU,OAAW,SAAQ;AACjC,cAAQ,cAAc,MAAM,KAAK,EAAE;AACnC,YAAM,EAAE,QAAQ,gBAAgB,OAAO,yBAAyB,IAAI,cAAc,MAAM,KAAK;AAC7F,cAAQ,2BAA2B;AACnC,cAAQ,cAAc,MAAM,KAAK,EAAE;AACnC,YAAM,EAAE,KAAK,OAAO,cAAc,IAAI,YAAY,MAAM,KAAK;AAC7D,cAAQ,KAAK;AAAA,QAEX,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QAET,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QAET,KAAK,yBAAyB;AAC5B,kBAAQ;AACR,kBAAQ,cAAc,MAAM,KAAK,EAAE;AACnC,cAAI,KAAK,OAAO,MAAM,IAAM,OAAM,UAAU,gBAAgB,IAAI;AAChE,kBAAQ,eAAe,MAAM,KAAK,EAAE;AACpC,kBAAQ,cAAc,MAAM,KAAK,EAAE;AACnC,gBAAM,EAAE,KAAK,QAAQ,IAAI,YAAY,MAAM,KAAK;AAChD,kBAAQ,SAAS;AAAA,YAEf,KAAK;AACH,qBAAO;AAAA,YACT,KAAK;AACH,qBAAO;AAAA,YACT,KAAK;AACH,qBAAO;AAAA,YACT,KAAK;AACH,qBAAO;AAAA,YACT,KAAK;AACH,qBAAO;AAAA,UACX;AACA,gBAAM,UAAU,sBAAsB,SAAS,IAAI;AAAA,QACrD;AAAA,QAEA,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QAET,KAAK;AAAA,QACL,KAAK;AACH,gBAAM,UAAU,0EAA0E;AAAA,MAC9F;AACA,YAAM,UAAU,iBAAiB,KAAK,IAAI;AAAA,IAC5C;AAEA,WAAO,UAAU,EAAE,sCAAsC;AAAA;AAAA;;;ACzHzD;AAAA;AAAA;AACA,QAAM,SAAS;AACf,QAAM,EAAE,sCAAsC,IAAI;AAElD,aAAS,aAAa,YAAY,QAAQ;AACxC,YAAM,aAAa,CAAC,eAAe;AACnC,UAAI,OAAQ,YAAW,QAAQ,oBAAoB;AAEnD,YAAM,YAAY,WAAW,KAAK,CAAC,cAAc,WAAW,SAAS,SAAS,CAAC;AAE/E,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,6BAA6B,WAAW,KAAK,OAAO,IAAI,gBAAgB;AAAA,MAC1F;AAEA,UAAI,cAAc,wBAAwB,OAAO,OAAO,uBAAuB,YAAY;AAEzF,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,YAAM,cAAc,OAAO,YAAY,EAAE,EAAE,SAAS,QAAQ;AAC5D,YAAM,YAAY,cAAc,uBAAuB,2BAA2B,SAAS,MAAM;AAEjG,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,UAAU,YAAY,aAAa;AAAA,QACnC,SAAS;AAAA,MACX;AAAA,IACF;AAEA,mBAAe,gBAAgB,SAAS,UAAU,YAAY,QAAQ;AACpE,UAAI,QAAQ,YAAY,uBAAuB;AAC7C,cAAM,IAAI,MAAM,gDAAgD;AAAA,MAClE;AACA,UAAI,OAAO,aAAa,UAAU;AAChC,cAAM,IAAI,MAAM,oEAAoE;AAAA,MACtF;AACA,UAAI,aAAa,IAAI;AACnB,cAAM,IAAI,MAAM,8EAA8E;AAAA,MAChG;AACA,UAAI,OAAO,eAAe,UAAU;AAClC,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,YAAM,KAAK,wBAAwB,UAAU;AAE7C,UAAI,CAAC,GAAG,MAAM,WAAW,QAAQ,WAAW,GAAG;AAC7C,cAAM,IAAI,MAAM,iFAAiF;AAAA,MACnG,WAAW,GAAG,MAAM,WAAW,QAAQ,YAAY,QAAQ;AACzD,cAAM,IAAI,MAAM,6DAA6D;AAAA,MAC/E;AAEA,YAAM,yBAAyB,WAAW,QAAQ;AAClD,YAAM,qBAAqB,OAAO,GAAG,QAAQ,QAAQ,GAAG,OAAO,QAAQ,GAAG;AAG1E,UAAI,iBAAiB,SAAS,SAAS;AAGvC,UAAI,QAAQ,cAAc,sBAAsB;AAC9C,cAAM,WAAW,OAAO,mBAAmB,EAAE;AAC7C,YAAI,WAAW,sCAAsC,QAAQ;AAC7D,YAAI,aAAa,SAAS,aAAa,QAAS,YAAW;AAC3D,cAAM,WAAW,MAAM,OAAO,WAAW,UAAU,QAAQ;AAC3D,cAAM,cAAc,OAAO,OAAO,CAAC,OAAO,KAAK,0BAA0B,GAAG,OAAO,KAAK,QAAQ,CAAC,CAAC;AAClG,yBAAiB,YAAY,SAAS,QAAQ;AAAA,MAChD;AAEA,YAAM,iCAAiC,OAAO,iBAAiB,QAAQ,GAAG;AAC1E,YAAM,cAAc,yBAAyB,MAAM,qBAAqB,MAAM;AAE9E,YAAM,YAAY,OAAO,KAAK,GAAG,MAAM,QAAQ;AAC/C,YAAM,iBAAiB,MAAM,OAAO,UAAU,UAAU,WAAW,GAAG,SAAS;AAC/E,YAAM,YAAY,MAAM,OAAO,WAAW,gBAAgB,YAAY;AACtE,YAAM,YAAY,MAAM,OAAO,OAAO,SAAS;AAC/C,YAAM,kBAAkB,MAAM,OAAO,WAAW,WAAW,WAAW;AACtE,YAAM,cAAc,WAAW,OAAO,KAAK,SAAS,GAAG,OAAO,KAAK,eAAe,CAAC,EAAE,SAAS,QAAQ;AACtG,YAAM,YAAY,MAAM,OAAO,WAAW,gBAAgB,YAAY;AACtE,YAAM,uBAAuB,MAAM,OAAO,WAAW,WAAW,WAAW;AAE3E,cAAQ,UAAU;AAClB,cAAQ,kBAAkB,OAAO,KAAK,oBAAoB,EAAE,SAAS,QAAQ;AAC7E,cAAQ,WAAW,iCAAiC,QAAQ;AAAA,IAC9D;AAEA,aAAS,gBAAgB,SAAS,YAAY;AAC5C,UAAI,QAAQ,YAAY,gBAAgB;AACtC,cAAM,IAAI,MAAM,yCAAyC;AAAA,MAC3D;AACA,UAAI,OAAO,eAAe,UAAU;AAClC,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,YAAM,EAAE,gBAAgB,IAAI,wBAAwB,UAAU;AAE9D,UAAI,oBAAoB,QAAQ,iBAAiB;AAC/C,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAAA,IACF;AAQA,aAAS,iBAAiB,MAAM;AAC9B,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,IAAI,UAAU,6BAA6B;AAAA,MACnD;AACA,aAAO,KACJ,MAAM,EAAE,EACR,IAAI,CAAC,GAAG,MAAM,KAAK,WAAW,CAAC,CAAC,EAChC,MAAM,CAAC,MAAO,KAAK,MAAQ,KAAK,MAAU,KAAK,MAAQ,KAAK,GAAK;AAAA,IACtE;AAaA,aAAS,SAAS,MAAM;AACtB,aAAO,mEAAmE,KAAK,IAAI;AAAA,IACrF;AAEA,aAAS,oBAAoB,MAAM;AACjC,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,IAAI,UAAU,6CAA6C;AAAA,MACnE;AAEA,aAAO,IAAI;AAAA,QACT,KAAK,MAAM,GAAG,EAAE,IAAI,CAAC,cAAc;AACjC,cAAI,CAAC,MAAM,KAAK,SAAS,GAAG;AAC1B,kBAAM,IAAI,MAAM,oCAAoC;AAAA,UACtD;AACA,gBAAM,OAAO,UAAU,CAAC;AACxB,gBAAM,QAAQ,UAAU,UAAU,CAAC;AACnC,iBAAO,CAAC,MAAM,KAAK;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF;AAEA,aAAS,wBAAwB,MAAM;AACrC,YAAM,YAAY,oBAAoB,IAAI;AAE1C,YAAM,QAAQ,UAAU,IAAI,GAAG;AAC/B,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,iDAAiD;AAAA,MACnE,WAAW,CAAC,iBAAiB,KAAK,GAAG;AACnC,cAAM,IAAI,MAAM,gFAAgF;AAAA,MAClG;AACA,YAAM,OAAO,UAAU,IAAI,GAAG;AAC9B,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,gDAAgD;AAAA,MAClE,WAAW,CAAC,SAAS,IAAI,GAAG;AAC1B,cAAM,IAAI,MAAM,uDAAuD;AAAA,MACzE;AACA,YAAM,gBAAgB,UAAU,IAAI,GAAG;AACvC,UAAI,CAAC,eAAe;AAClB,cAAM,IAAI,MAAM,qDAAqD;AAAA,MACvE,WAAW,CAAC,gBAAgB,KAAK,aAAa,GAAG;AAC/C,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AACA,YAAM,YAAY,SAAS,eAAe,EAAE;AAE5C,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,aAAS,wBAAwB,YAAY;AAC3C,YAAM,YAAY,oBAAoB,UAAU;AAChD,YAAM,kBAAkB,UAAU,IAAI,GAAG;AACzC,UAAI,CAAC,iBAAiB;AACpB,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF,WAAW,CAAC,SAAS,eAAe,GAAG;AACrC,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AACA,aAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAEA,aAAS,WAAW,GAAG,GAAG;AACxB,UAAI,CAAC,OAAO,SAAS,CAAC,GAAG;AACvB,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AACA,UAAI,CAAC,OAAO,SAAS,CAAC,GAAG;AACvB,cAAM,IAAI,UAAU,kCAAkC;AAAA,MACxD;AACA,UAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,cAAM,IAAI,MAAM,2BAA2B;AAAA,MAC7C;AACA,UAAI,EAAE,WAAW,GAAG;AAClB,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AACA,aAAO,OAAO,KAAK,EAAE,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAAA,IACjD;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;ACnNA;AAAA;AAAA;AAEA,QAAMC,SAAQ;AAEd,aAASC,eAAc,WAAW;AAChC,WAAK,SAAS,aAAaD;AAC3B,WAAK,OAAO,CAAC;AACb,WAAK,SAAS,CAAC;AAAA,IACjB;AAEA,IAAAC,eAAc,UAAU,eAAe,SAAU,QAAQ;AACvD,cAAQ,QAAQ;AAAA,QACd,KAAK;AACH,iBAAO,KAAK;AAAA,QACd,KAAK;AACH,iBAAO,KAAK;AAAA,QACd;AACE,iBAAO,CAAC;AAAA,MACZ;AAAA,IACF;AAEA,IAAAA,eAAc,UAAU,gBAAgB,SAAU,KAAK,QAAQ,SAAS;AACtE,UAAI,OAAO,WAAW,YAAY;AAChC,kBAAU;AACV,iBAAS;AAAA,MACX;AACA,WAAK,aAAa,MAAM,EAAE,GAAG,IAAI;AAAA,IACnC;AAEA,IAAAA,eAAc,UAAU,gBAAgB,SAAU,KAAK,QAAQ;AAC7D,eAAS,UAAU;AACnB,aAAO,KAAK,aAAa,MAAM,EAAE,GAAG,KAAK,KAAK,OAAO,cAAc,KAAK,MAAM;AAAA,IAChF;AAEA,WAAO,UAAUA;AAAA;AAAA;;;AClCjB;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,oFAAoF,GAAG,mIAAmI;AAAA,QACzO;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,kFAAkF,GAAG,mIAAmI;AAAA,QACvO;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA;AAOA,aAAS,MAAM,KAAK,UAAU,CAAC,GAAG;AAEhC,UAAI,IAAI,OAAO,CAAC,MAAM,KAAK;AACzB,cAAMC,UAAS,IAAI,MAAM,GAAG;AAC5B,eAAO,EAAE,MAAMA,QAAO,CAAC,GAAG,UAAUA,QAAO,CAAC,EAAE;AAAA,MAChD;AAIA,YAAM,SAAS,CAAC;AAChB,UAAI;AACJ,UAAI,YAAY;AAChB,UAAI,mCAAmC,KAAK,GAAG,GAAG;AAEhD,cAAM,UAAU,GAAG,EAAE,QAAQ,cAAc,KAAK;AAAA,MAClD;AAEA,UAAI;AACF,YAAI;AACF,mBAAS,IAAI,IAAI,KAAK,iBAAiB;AAAA,QACzC,SAAS,GAAG;AAEV,mBAAS,IAAI,IAAI,IAAI,QAAQ,MAAM,eAAe,GAAG,iBAAiB;AACtE,sBAAY;AAAA,QACd;AAAA,MACF,SAAS,KAAK;AAEZ,YAAI,UAAU,IAAI,QAAQ;AAAA,MAC5B;AAGA,iBAAW,SAAS,OAAO,aAAa,QAAQ,GAAG;AACjD,eAAO,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC;AAAA,MAC5B;AAEA,aAAO,OAAO,OAAO,QAAQ,mBAAmB,OAAO,QAAQ;AAC/D,aAAO,WAAW,OAAO,YAAY,mBAAmB,OAAO,QAAQ;AAEvE,UAAI,OAAO,YAAY,WAAW;AAChC,eAAO,OAAO,UAAU,OAAO,QAAQ;AACvC,eAAO,WAAW,OAAO,aAAa,IAAI,IAAI;AAC9C,eAAO,kBAAkB,OAAO,aAAa,IAAI,UAAU;AAC3D,eAAO;AAAA,MACT;AACA,YAAM,WAAW,YAAY,KAAK,OAAO;AACzC,UAAI,CAAC,OAAO,MAAM;AAEhB,eAAO,OAAO,mBAAmB,QAAQ;AAAA,MAC3C,WAAW,YAAY,QAAQ,KAAK,QAAQ,GAAG;AAE7C,eAAO,WAAW,WAAW,OAAO;AAAA,MACtC;AACA,UAAI,CAAC,OAAO,MAAM;AAEhB,eAAO,OAAO,OAAO;AAAA,MACvB;AAEA,YAAM,WAAW,OAAO,SAAS,MAAM,CAAC,KAAK;AAC7C,aAAO,WAAW,WAAW,UAAU,QAAQ,IAAI;AAEnD,UAAI,OAAO,QAAQ,UAAU,OAAO,QAAQ,KAAK;AAC/C,eAAO,MAAM;AAAA,MACf;AAEA,UAAI,OAAO,QAAQ,KAAK;AACtB,eAAO,MAAM;AAAA,MACf;AAEA,UAAI,OAAO,WAAW,OAAO,UAAU,OAAO,eAAe,OAAO,SAAS;AAC3E,eAAO,MAAM,CAAC;AAAA,MAChB;AAGA,YAAM,KAAK,OAAO,WAAW,OAAO,UAAU,OAAO,cAAc,eAAgB;AAEnF,UAAI,OAAO,SAAS;AAClB,eAAO,IAAI,OAAO,GAAG,aAAa,OAAO,OAAO,EAAE,SAAS;AAAA,MAC7D;AAEA,UAAI,OAAO,QAAQ;AACjB,eAAO,IAAI,MAAM,GAAG,aAAa,OAAO,MAAM,EAAE,SAAS;AAAA,MAC3D;AAEA,UAAI,OAAO,aAAa;AACtB,eAAO,IAAI,KAAK,GAAG,aAAa,OAAO,WAAW,EAAE,SAAS;AAAA,MAC/D;AAEA,UAAI,QAAQ,kBAAkB,OAAO,gBAAgB;AACnD,cAAM,IAAI,MAAM,8EAA8E;AAAA,MAChG;AAEA,UAAI,OAAO,mBAAmB,UAAU,QAAQ,gBAAgB;AAC9D,gBAAQ,OAAO,SAAS;AAAA,UACtB,KAAK,WAAW;AACd,mBAAO,MAAM;AACb;AAAA,UACF;AAAA,UACA,KAAK,UAAU;AACb,mBAAO,IAAI,qBAAqB;AAChC;AAAA,UACF;AAAA,UACA,KAAK,WAAW;AACd,gBAAI,OAAO,aAAa;AAEtB,qBAAO,IAAI,sBAAsB,WAAY;AAAA,cAAC;AAAA,YAChD,OAAO;AACL,qBAAO,IAAI,qBAAqB;AAAA,YAClC;AACA;AAAA,UACF;AAAA,UACA,KAAK,aAAa;AAChB,gBAAI,CAAC,OAAO,IAAI,IAAI;AAClB,oBAAM,IAAI;AAAA,gBACR;AAAA,cACF;AAAA,YACF;AACA,mBAAO,IAAI,sBAAsB,WAAY;AAAA,YAAC;AAC9C;AAAA,UACF;AAAA,UACA,KAAK,eAAe;AAClB;AAAA,UACF;AAAA,QACF;AAAA,MACF,OAAO;AACL,gBAAQ,OAAO,SAAS;AAAA,UACtB,KAAK,WAAW;AACd,mBAAO,MAAM;AACb;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,eAAe;AAClB;AAAA,UACF;AAAA,UACA,KAAK,aAAa;AAChB,mBAAO,IAAI,qBAAqB;AAChC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAGA,aAAS,oBAAoB,WAAW;AACtC,YAAM,oBAAoB,OAAO,QAAQ,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,MAAM;AAG9E,YAAI,UAAU,UAAa,UAAU,MAAM;AACzC,YAAE,GAAG,IAAI;AAAA,QACX;AAEA,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAEL,aAAO;AAAA,IACT;AAGA,aAAS,eAAe,QAAQ;AAC9B,YAAM,aAAa,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,MAAM;AACpE,YAAI,QAAQ,OAAO;AACjB,gBAAM,YAAY;AAElB,cAAI,OAAO,cAAc,WAAW;AAClC,cAAE,GAAG,IAAI;AAAA,UACX;AAEA,cAAI,OAAO,cAAc,UAAU;AACjC,cAAE,GAAG,IAAI,oBAAoB,SAAS;AAAA,UACxC;AAAA,QACF,WAAW,UAAU,UAAa,UAAU,MAAM;AAChD,cAAI,QAAQ,QAAQ;AAGlB,gBAAI,UAAU,IAAI;AAChB,oBAAM,IAAI,SAAS,OAAO,EAAE;AAC5B,kBAAI,MAAM,CAAC,GAAG;AACZ,sBAAM,IAAI,MAAM,WAAW,GAAG,KAAK,KAAK,EAAE;AAAA,cAC5C;AAEA,gBAAE,GAAG,IAAI;AAAA,YACX;AAAA,UACF,OAAO;AACL,cAAE,GAAG,IAAI;AAAA,UACX;AAAA,QACF;AAEA,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAEL,aAAO;AAAA,IACT;AAGA,aAAS,sBAAsB,KAAK;AAClC,aAAO,eAAe,MAAM,GAAG,CAAC;AAAA,IAClC;AAEA,WAAO,UAAU;AAEjB,UAAM,QAAQ;AACd,UAAM,iBAAiB;AACvB,UAAM,wBAAwB;AAAA;AAAA;;;ACpN9B;AAAA;AAAA;AAEA,QAAM,MAAM;AAEZ,QAAMC,YAAW;AAEjB,QAAM,QAAQ,+BAAgC;AAE9C,QAAM,MAAM,SAAU,KAAK,QAAQ,QAAQ;AACzC,UAAI,WAAW,QAAW;AACxB,iBAAS,QAAQ,IAAI,OAAO,IAAI,YAAY,CAAC;AAAA,MAC/C,WAAW,WAAW,OAAO;AAAA,MAE7B,OAAO;AACL,iBAAS,QAAQ,IAAI,MAAM;AAAA,MAC7B;AAEA,aAAO,OAAO,GAAG,KAAK,UAAUA,UAAS,GAAG;AAAA,IAC9C;AAEA,QAAM,+BAA+B,WAAY;AAC/C,cAAQ,QAAQ,IAAI,WAAW;AAAA,QAC7B,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO,EAAE,oBAAoB,MAAM;AAAA,MACvC;AACA,aAAOA,UAAS;AAAA,IAClB;AAGA,QAAM,kBAAkB,SAAU,OAAO;AACvC,aAAO,OAAO,KAAK,OAAO,QAAQ,OAAO,MAAM,EAAE,QAAQ,MAAM,KAAK,IAAI;AAAA,IAC1E;AAEA,QAAM,MAAM,SAAU,QAAQ,QAAQ,WAAW;AAC/C,YAAM,QAAQ,OAAO,SAAS;AAC9B,UAAI,UAAU,UAAa,UAAU,MAAM;AACzC,eAAO,KAAK,YAAY,MAAM,gBAAgB,KAAK,CAAC;AAAA,MACtD;AAAA,IACF;AAEA,QAAM,uBAAN,MAA2B;AAAA,MACzB,YAAY,QAAQ;AAElB,iBAAS,OAAO,WAAW,WAAW,MAAM,MAAM,IAAI,UAAU,CAAC;AAIjE,YAAI,OAAO,kBAAkB;AAC3B,mBAAS,OAAO,OAAO,CAAC,GAAG,QAAQ,MAAM,OAAO,gBAAgB,CAAC;AAAA,QACnE;AAEA,aAAK,OAAO,IAAI,QAAQ,MAAM;AAC9B,aAAK,WAAW,IAAI,YAAY,MAAM;AAEtC,YAAI,KAAK,aAAa,QAAW;AAC/B,eAAK,WAAW,KAAK;AAAA,QACvB;AAEA,aAAK,OAAO,SAAS,IAAI,QAAQ,MAAM,GAAG,EAAE;AAC5C,aAAK,OAAO,IAAI,QAAQ,MAAM;AAI9B,eAAO,eAAe,MAAM,YAAY;AAAA,UACtC,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,OAAO,IAAI,YAAY,MAAM;AAAA,QAC/B,CAAC;AAED,aAAK,SAAS,IAAI,UAAU,MAAM;AAClC,aAAK,UAAU,IAAI,WAAW,MAAM;AAEpC,aAAK,MAAM,OAAO,OAAO,QAAQ,cAAc,6BAA6B,IAAI,OAAO;AAEvF,YAAI,OAAO,KAAK,QAAQ,UAAU;AAChC,cAAI,KAAK,QAAQ,QAAQ;AACvB,iBAAK,MAAM;AAAA,UACb;AAAA,QACF;AAEA,YAAI,KAAK,QAAQ,aAAa;AAC5B,eAAK,MAAM,EAAE,oBAAoB,MAAM;AAAA,QACzC;AACA,YAAI,KAAK,OAAO,KAAK,IAAI,KAAK;AAC5B,iBAAO,eAAe,KAAK,KAAK,OAAO;AAAA,YACrC,YAAY;AAAA,UACd,CAAC;AAAA,QACH;AAEA,aAAK,kBAAkB,IAAI,mBAAmB,MAAM;AACpD,aAAK,cAAc,IAAI,eAAe,MAAM;AAE5C,aAAK,iBAAiB,EAAE,KAAK,QAAQ,IAAI,QAAQ,GAAG;AAEpD,aAAK,mBAAmB,IAAI,oBAAoB,QAAQ,WAAW;AACnE,aAAK,4BAA4B,IAAI,6BAA6B,QAAQ,KAAK;AAC/E,aAAK,oBAAoB,IAAI,qBAAqB,QAAQ,KAAK;AAC/D,aAAK,eAAe,IAAI,gBAAgB,QAAQ,KAAK;AACrD,aAAK,sCAAsC,IAAI,uCAAuC,QAAQ,KAAK;AACnG,aAAK,gBAAgB,IAAI,iBAAiB,QAAQ,KAAK;AAEvD,YAAI,OAAO,4BAA4B,QAAW;AAChD,eAAK,kBAAkB,QAAQ,IAAI,qBAAqB;AAAA,QAC1D,OAAO;AACL,eAAK,kBAAkB,KAAK,MAAM,OAAO,0BAA0B,GAAI;AAAA,QACzE;AAEA,YAAI,OAAO,cAAc,OAAO;AAC9B,eAAK,aAAa;AAAA,QACpB,WAAW,OAAO,cAAc,MAAM;AACpC,eAAK,aAAa;AAAA,QACpB;AAEA,YAAI,OAAO,OAAO,gCAAgC,UAAU;AAC1D,eAAK,kBAAkB,KAAK,MAAM,OAAO,8BAA8B,GAAI;AAAA,QAC7E;AAAA,MACF;AAAA,MAEA,yBAAyB,IAAI;AAC3B,cAAM,SAAS,CAAC;AAChB,YAAI,QAAQ,MAAM,MAAM;AACxB,YAAI,QAAQ,MAAM,UAAU;AAC5B,YAAI,QAAQ,MAAM,MAAM;AACxB,YAAI,QAAQ,MAAM,kBAAkB;AACpC,YAAI,QAAQ,MAAM,2BAA2B;AAC7C,YAAI,QAAQ,MAAM,iBAAiB;AACnC,YAAI,QAAQ,MAAM,SAAS;AAE3B,cAAM,MAAM,OAAO,KAAK,QAAQ,WAAW,KAAK,MAAM,KAAK,MAAM,EAAE,SAAS,KAAK,IAAI,IAAI,CAAC;AAC1F,YAAI,QAAQ,KAAK,SAAS;AAC1B,YAAI,QAAQ,KAAK,OAAO;AACxB,YAAI,QAAQ,KAAK,QAAQ;AACzB,YAAI,QAAQ,KAAK,SAAS;AAC1B,YAAI,QAAQ,KAAK,aAAa;AAE9B,YAAI,KAAK,UAAU;AACjB,iBAAO,KAAK,YAAY,gBAAgB,KAAK,QAAQ,CAAC;AAAA,QACxD;AACA,YAAI,KAAK,aAAa;AACpB,iBAAO,KAAK,iBAAiB,gBAAgB,KAAK,WAAW,CAAC;AAAA,QAChE;AACA,YAAI,KAAK,MAAM;AACb,iBAAO,KAAK,UAAU,gBAAgB,KAAK,IAAI,CAAC;AAAA,QAClD;AACA,YAAI,KAAK,gBAAgB;AACvB,iBAAO,GAAG,MAAM,OAAO,KAAK,GAAG,CAAC;AAAA,QAClC;AACA,YAAI,KAAK,iBAAiB;AACxB,iBAAO,KAAK,qBAAqB,gBAAgB,KAAK,eAAe,CAAC;AAAA,QACxE;AACA,YAAI,OAAO,KAAK,MAAM,SAAU,KAAK,SAAS;AAC5C,cAAI,IAAK,QAAO,GAAG,KAAK,IAAI;AAC5B,iBAAO,KAAK,cAAc,gBAAgB,OAAO,CAAC;AAClD,iBAAO,GAAG,MAAM,OAAO,KAAK,GAAG,CAAC;AAAA,QAClC,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtKjB;AAAA;AAAA;AAEA,QAAMC,SAAQ;AAEd,QAAM,cAAc;AAKpB,QAAMC,UAAN,MAAa;AAAA,MACX,YAAY,SAASD,QAAO;AAC1B,aAAK,UAAU;AACf,aAAK,WAAW;AAChB,aAAK,MAAM;AACX,aAAK,OAAO,CAAC;AACb,aAAK,SAAS,CAAC;AACf,aAAK,WAAW;AAChB,aAAK,SAASA;AACd,aAAK,UAAU;AACf,aAAK,aAAa,YAAY;AAC9B,YAAI,KAAK,YAAY;AACnB,eAAK,WAAW,KAAK;AAAA,QACvB;AACA,aAAK,6BAA6B;AAAA,MACpC;AAAA;AAAA,MAGA,mBAAmB,KAAK;AACtB,YAAI;AACJ,YAAI,IAAI,MAAM;AAEZ,kBAAQ,YAAY,KAAK,IAAI,IAAI;AAAA,QACnC,OAAO;AAEL,kBAAQ,YAAY,KAAK,IAAI,OAAO;AAAA,QACtC;AACA,YAAI,OAAO;AACT,eAAK,UAAU,MAAM,CAAC;AACtB,cAAI,MAAM,CAAC,GAAG;AAEZ,iBAAK,MAAM,SAAS,MAAM,CAAC,GAAG,EAAE;AAChC,iBAAK,WAAW,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,UACvC,WAAW,MAAM,CAAC,GAAG;AAEnB,iBAAK,WAAW,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,UACvC;AAAA,QACF;AAAA,MACF;AAAA,MAEA,iBAAiB,SAAS;AACxB,cAAM,MAAM,IAAI,MAAM,QAAQ,MAAM;AACpC,iBAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,gBAAM,WAAW,QAAQ,CAAC;AAC1B,cAAI,aAAa,MAAM;AACrB,gBAAI,CAAC,IAAI,KAAK,SAAS,CAAC,EAAE,QAAQ;AAAA,UACpC,OAAO;AACL,gBAAI,CAAC,IAAI;AAAA,UACX;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MAEA,SAAS,SAAS;AAChB,cAAM,MAAM,EAAE,GAAG,KAAK,2BAA2B;AACjD,iBAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,gBAAM,WAAW,QAAQ,CAAC;AAC1B,gBAAM,QAAQ,KAAK,OAAO,CAAC,EAAE;AAC7B,cAAI,aAAa,MAAM;AACrB,gBAAI,KAAK,IAAI,KAAK,SAAS,CAAC,EAAE,QAAQ;AAAA,UACxC,OAAO;AACL,gBAAI,KAAK,IAAI;AAAA,UACf;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MAEA,OAAO,KAAK;AACV,aAAK,KAAK,KAAK,GAAG;AAAA,MACpB;AAAA,MAEA,UAAU,mBAAmB;AAK3B,aAAK,SAAS;AACd,YAAI,KAAK,OAAO,QAAQ;AACtB,eAAK,WAAW,IAAI,MAAM,kBAAkB,MAAM;AAAA,QACpD;AAEA,cAAM,MAAM,CAAC;AAEb,iBAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,gBAAM,OAAO,kBAAkB,CAAC;AAChC,cAAI,KAAK,IAAI,IAAI;AAEjB,cAAI,KAAK,QAAQ;AACf,iBAAK,SAAS,CAAC,IAAI,KAAK,OAAO,cAAc,KAAK,YAAY,KAAK,UAAU,MAAM;AAAA,UACrF,OAAO;AACL,iBAAK,SAAS,CAAC,IAAIA,OAAM,cAAc,KAAK,YAAY,KAAK,UAAU,MAAM;AAAA,UAC/E;AAAA,QACF;AAEA,aAAK,6BAA6B,EAAE,GAAG,IAAI;AAAA,MAC7C;AAAA,IACF;AAEA,WAAO,UAAUC;AAAA;AAAA;;;AC3GjB;AAAA;AAAA;AAEA,QAAM,EAAE,aAAa,IAAI;AAEzB,QAAMC,UAAS;AACf,QAAM,QAAQ;AAEd,QAAMC,SAAN,cAAoB,aAAa;AAAA,MAC/B,YAAY,QAAQ,QAAQ,UAAU;AACpC,cAAM;AAEN,iBAAS,MAAM,qBAAqB,QAAQ,QAAQ,QAAQ;AAE5D,aAAK,OAAO,OAAO;AACnB,aAAK,SAAS,OAAO;AACrB,aAAK,OAAO,OAAO;AACnB,aAAK,QAAQ,OAAO;AACpB,aAAK,OAAO,OAAO;AACnB,aAAK,YAAY,OAAO;AACxB,aAAK,SAAS,OAAO;AAErB,aAAK,SAAS,OAAO,UAAU;AAC/B,aAAK,WAAW,OAAO;AACvB,aAAK,WAAW,OAAO;AACvB,YAAI,QAAQ,UAAU,OAAO,UAAU;AACrC,eAAK,WAAW,QAAQ,OAAO,KAAK,OAAO,QAAQ;AAAA,QACrD;AACA,aAAK,UAAU,IAAID,QAAO,KAAK,UAAU,KAAK,KAAK;AAGnD,aAAK,WAAW,KAAK;AACrB,aAAK,sBAAsB;AAAA,MAC7B;AAAA,MAEA,sBAAsB;AACpB,YAAI,KAAK,cAAc,YAAY;AACjC,iBAAO;AAAA,QACT;AAGA,YAAI,KAAK,MAAM;AACb,iBAAO;AAAA,QACT;AAGA,YAAI,KAAK,MAAM;AACb,iBAAO;AAAA,QACT;AAEA,YAAI,CAAC,KAAK,MAAM;AACd,iBAAO;AAAA,QACT;AAEA,YAAI,CAAC,KAAK,QAAQ;AAChB,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,OAAO,SAAS;AAAA,MAC9B;AAAA,MAEA,oBAAoB;AAIlB,YAAI,KAAK,QAAQ,SAAS;AACxB,cAAI,CAAC,MAAM,QAAQ,KAAK,QAAQ,GAAG;AACjC,iBAAK,WAAW,CAAC,KAAK,OAAO;AAAA,UAC/B;AACA,eAAK,UAAU,IAAIA,QAAO,KAAK,UAAU,KAAK,QAAQ,MAAM;AAC5D,eAAK,SAAS,KAAK,KAAK,OAAO;AAAA,QACjC;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAKA,qBAAqB,KAAK;AACxB,aAAK,kBAAkB;AACvB,aAAK,QAAQ,UAAU,IAAI,MAAM;AACjC,aAAK,kBAAkB,KAAK,YAAY,CAAC,KAAK,UAAU,KAAK,EAAE;AAAA,MACjE;AAAA,MAEA,cAAc,KAAK;AACjB,YAAI;AAEJ,YAAI,KAAK,qBAAqB;AAC5B;AAAA,QACF;AAEA,YAAI;AACF,gBAAM,KAAK,QAAQ,SAAS,IAAI,MAAM;AAAA,QACxC,SAAS,KAAK;AACZ,eAAK,sBAAsB;AAC3B;AAAA,QACF;AAEA,aAAK,KAAK,OAAO,KAAK,KAAK,OAAO;AAClC,YAAI,KAAK,iBAAiB;AACxB,eAAK,QAAQ,OAAO,GAAG;AAAA,QACzB;AAAA,MACF;AAAA,MAEA,sBAAsB,KAAK,YAAY;AACrC,aAAK,kBAAkB;AACvB,aAAK,QAAQ,mBAAmB,GAAG;AAGnC,YAAI,KAAK,MAAM;AACb,qBAAW,KAAK;AAAA,QAClB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,iBAAiB,YAAY;AAC3B,YAAI,KAAK,MAAM;AACb,qBAAW,KAAK;AAAA,QAClB;AAAA,MACF;AAAA,MAEA,YAAY,KAAK,YAAY;AAE3B,YAAI,KAAK,qBAAqB;AAC5B,gBAAM,KAAK;AACX,eAAK,sBAAsB;AAAA,QAC7B;AAGA,YAAI,KAAK,UAAU;AACjB,iBAAO,KAAK,SAAS,GAAG;AAAA,QAC1B;AACA,aAAK,KAAK,SAAS,GAAG;AAAA,MACxB;AAAA,MAEA,oBAAoB,KAAK;AACvB,YAAI,KAAK,qBAAqB;AAC5B,iBAAO,KAAK,YAAY,KAAK,qBAAqB,GAAG;AAAA,QACvD;AACA,YAAI,KAAK,UAAU;AACjB,cAAI;AACF,iBAAK,SAAS,MAAM,KAAK,QAAQ;AAAA,UACnC,SAAS,KAAK;AACZ,oBAAQ,SAAS,MAAM;AACrB,oBAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AACA,aAAK,KAAK,OAAO,KAAK,QAAQ;AAAA,MAChC;AAAA,MAEA,OAAO,YAAY;AACjB,YAAI,OAAO,KAAK,SAAS,YAAY,OAAO,KAAK,SAAS,UAAU;AAClE,iBAAO,IAAI,MAAM,4EAA4E;AAAA,QAC/F;AACA,cAAM,WAAW,WAAW,iBAAiB,KAAK,IAAI;AACtD,YAAI,KAAK,QAAQ,YAAY,KAAK,SAAS,UAAU;AACnD,iBAAO,IAAI,MAAM,yCAAyC,KAAK,IAAI,sCAAsC;AAAA,QAC3G;AACA,YAAI,KAAK,UAAU,CAAC,MAAM,QAAQ,KAAK,MAAM,GAAG;AAC9C,iBAAO,IAAI,MAAM,+BAA+B;AAAA,QAClD;AACA,YAAI,KAAK,oBAAoB,GAAG;AAQ9B,qBAAW,OAAO,QAAQ,WAAW,OAAO,KAAK;AACjD,cAAI;AACF,iBAAK,QAAQ,UAAU;AAAA,UACzB,UAAE;AAGA,uBAAW,OAAO,UAAU,WAAW,OAAO,OAAO;AAAA,UACvD;AAAA,QACF,OAAO;AACL,qBAAW,MAAM,KAAK,IAAI;AAAA,QAC5B;AACA,eAAO;AAAA,MACT;AAAA,MAEA,cAAc,YAAY;AACxB,eAAO,KAAK,QAAQ,WAAW,iBAAiB,KAAK,IAAI;AAAA,MAC3D;AAAA,MAEA,sBAAsB,YAAY;AAChC,aAAK,SAAS,YAAY,KAAK,IAAI;AAAA,MACrC;AAAA,MAEA,SAAS,YAAY,MAAM;AACzB,mBAAW,QAAQ;AAAA,UACjB,QAAQ,KAAK;AAAA,UACb;AAAA,QACF,CAAC;AAGD,YAAI,CAAC,MAAM;AACT,qBAAW,KAAK;AAAA,QAClB,OAAO;AAEL,qBAAW,MAAM;AAAA,QACnB;AAAA,MACF;AAAA;AAAA,MAGA,QAAQ,YAAY;AAElB,YAAI,CAAC,KAAK,cAAc,UAAU,GAAG;AACnC,qBAAW,MAAM;AAAA,YACf,MAAM,KAAK;AAAA,YACX,MAAM,KAAK;AAAA,YACX,OAAO,KAAK;AAAA,UACd,CAAC;AAAA,QACH;AAKA,YAAI;AACF,qBAAW,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,WAAW,KAAK;AAAA,YAChB,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,aAAa,MAAM;AAAA,UACrB,CAAC;AAAA,QACH,SAAS,KAAK;AACZ,eAAK,YAAY,KAAK,UAAU;AAChC;AAAA,QACF;AAEA,mBAAW,SAAS;AAAA,UAClB,MAAM;AAAA,UACN,MAAM,KAAK,UAAU;AAAA,QACvB,CAAC;AAED,aAAK,SAAS,YAAY,KAAK,IAAI;AAAA,MACrC;AAAA,MAEA,qBAAqB,YAAY;AAC/B,mBAAW,aAAa,0BAA0B;AAAA,MACpD;AAAA,MAEA,eAAe,KAAK,YAAY;AAAA,MAEhC;AAAA,IACF;AAEA,WAAO,UAAUC;AAAA;AAAA;;;;;;;;ACvNJ,YAAA,gBAAgC;MAC3C,MAAM;MACN,QAAQ;;AAGG,YAAA,eAA+B;MAC1C,MAAM;MACN,QAAQ;;AAGG,YAAA,gBAAgC;MAC3C,MAAM;MACN,QAAQ;;AAGG,YAAA,SAAyB;MACpC,MAAM;MACN,QAAQ;;AAGG,YAAA,kBAAkC;MAC7C,MAAM;MACN,QAAQ;;AAGG,YAAA,mBAAmC;MAC9C,MAAM;MACN,QAAQ;;AAGG,YAAA,aAA6B;MACxC,MAAM;MACN,QAAQ;;AAGG,YAAA,WAA2B;MACtC,MAAM;MACN,QAAQ;;AAuBV,QAAaC,iBAAb,cAAmC,MAAK;MAiBtC,YACE,SACgB,QACA,MAAiB;AAEjC,cAAM,OAAO;AAHG,aAAA,SAAA;AACA,aAAA,OAAA;MAGlB;;AAvBF,YAAA,gBAAAA;AA0BA,QAAa,kBAAb,MAA4B;MAE1B,YACkB,QACA,OAAa;AADb,aAAA,SAAA;AACA,aAAA,QAAA;AAHF,aAAA,OAAO;MAIpB;;AALL,YAAA,kBAAA;AAQA,QAAa,eAAb,MAAyB;MAEvB,YACkB,QACA,MACA,QAChB,aAAmB;AAHH,aAAA,SAAA;AACA,aAAA,OAAA;AACA,aAAA,SAAA;AAGhB,aAAK,cAAc,IAAI,MAAM,WAAW;MAC1C;;AATF,YAAA,eAAA;AAYA,QAAa,QAAb,MAAkB;MAChB,YACkB,MACA,SACA,UACA,YACA,cACA,kBACA,QAAY;AANZ,aAAA,OAAA;AACA,aAAA,UAAA;AACA,aAAA,WAAA;AACA,aAAA,aAAA;AACA,aAAA,eAAA;AACA,aAAA,mBAAA;AACA,aAAA,SAAA;MACf;;AATL,YAAA,QAAA;AAYA,QAAa,wBAAb,MAAkC;MAGhC,YACkB,QACA,YAAkB;AADlB,aAAA,SAAA;AACA,aAAA,aAAA;AAJF,aAAA,OAAoB;AAMlC,aAAK,SAAS,IAAI,MAAM,KAAK,UAAU;MACzC;;AARF,YAAA,wBAAA;AAWA,QAAa,8BAAb,MAAwC;MAGtC,YACkB,QACA,gBAAsB;AADtB,aAAA,SAAA;AACA,aAAA,iBAAA;AAJF,aAAA,OAAoB;AAMlC,aAAK,cAAc,IAAI,MAAM,KAAK,cAAc;MAClD;;AARF,YAAA,8BAAA;AAWA,QAAa,yBAAb,MAAmC;MAEjC,YACkB,QACA,eACA,gBAAsB;AAFtB,aAAA,SAAA;AACA,aAAA,gBAAA;AACA,aAAA,iBAAA;AAJF,aAAA,OAAoB;MAKjC;;AANL,YAAA,yBAAA;AASA,QAAa,4BAAb,MAAsC;MAEpC,YACkB,QACA,MAAY;AADZ,aAAA,SAAA;AACA,aAAA,OAAA;AAHF,aAAA,OAAoB;MAIjC;;AALL,YAAA,4BAAA;AAQA,QAAa,wBAAb,MAAkC;MAEhC,YACkB,QACA,WACA,WAAiB;AAFjB,aAAA,SAAA;AACA,aAAA,YAAA;AACA,aAAA,YAAA;AAJF,aAAA,OAAoB;MAKjC;;AANL,YAAA,wBAAA;AASA,QAAa,8BAAb,MAAwC;MAEtC,YACkB,QACA,WACA,SACA,SAAe;AAHf,aAAA,SAAA;AACA,aAAA,YAAA;AACA,aAAA,UAAA;AACA,aAAA,UAAA;AALF,aAAA,OAAoB;MAMjC;;AAPL,YAAA,8BAAA;AAUA,QAAa,uBAAb,MAAiC;MAE/B,YACkB,QACA,QAAc;AADd,aAAA,SAAA;AACA,aAAA,SAAA;AAHF,aAAA,OAAoB;MAIjC;;AALL,YAAA,uBAAA;AAQA,QAAa,yBAAb,MAAmC;MAEjC,YACkB,QACA,MAAY;AADZ,aAAA,SAAA;AACA,aAAA,OAAA;AAHF,aAAA,OAAoB;MAIjC;;AALL,YAAA,yBAAA;AAQA,QAAa,iBAAb,MAA2B;MAGzB,YACS,QACA,QAAa;AADb,aAAA,SAAA;AACA,aAAA,SAAA;AAHO,aAAA,OAAoB;AAKlC,aAAK,aAAa,OAAO;MAC3B;;AARF,YAAA,iBAAA;AAWA,QAAa,gBAAb,MAA0B;MACxB,YACkB,QACA,SAA2B;AAD3B,aAAA,SAAA;AACA,aAAA,UAAA;AAEF,aAAA,OAAO;MADpB;;AAJL,YAAA,gBAAA;;;;;;;;;;AC7OA,QAAa,SAAb,MAAmB;MAIjB,YAAoB,OAAO,KAAG;AAAV,aAAA,OAAA;AAFZ,aAAA,SAAiB;AACjB,aAAA,iBAAyB;AAE/B,aAAK,SAAS,OAAO,YAAY,IAAI;MACvC;MAEQ,OAAO,MAAY;AACzB,cAAM,YAAY,KAAK,OAAO,SAAS,KAAK;AAC5C,YAAI,YAAY,MAAM;AACpB,gBAAM,YAAY,KAAK;AAGvB,gBAAM,UAAU,UAAU,UAAU,UAAU,UAAU,KAAK;AAC7D,eAAK,SAAS,OAAO,YAAY,OAAO;AACxC,oBAAU,KAAK,KAAK,MAAM;;MAE9B;MAEO,SAAS,KAAW;AACzB,aAAK,OAAO,CAAC;AACb,aAAK,OAAO,KAAK,QAAQ,IAAK,QAAQ,KAAM;AAC5C,aAAK,OAAO,KAAK,QAAQ,IAAK,QAAQ,KAAM;AAC5C,aAAK,OAAO,KAAK,QAAQ,IAAK,QAAQ,IAAK;AAC3C,aAAK,OAAO,KAAK,QAAQ,IAAK,QAAQ,IAAK;AAC3C,eAAO;MACT;MAEO,SAAS,KAAW;AACzB,aAAK,OAAO,CAAC;AACb,aAAK,OAAO,KAAK,QAAQ,IAAK,QAAQ,IAAK;AAC3C,aAAK,OAAO,KAAK,QAAQ,IAAK,QAAQ,IAAK;AAC3C,eAAO;MACT;MAEO,WAAW,QAAc;AAC9B,YAAI,CAAC,QAAQ;AACX,eAAK,OAAO,CAAC;eACR;AACL,gBAAM,MAAM,OAAO,WAAW,MAAM;AACpC,eAAK,OAAO,MAAM,CAAC;AACnB,eAAK,OAAO,MAAM,QAAQ,KAAK,QAAQ,OAAO;AAC9C,eAAK,UAAU;;AAGjB,aAAK,OAAO,KAAK,QAAQ,IAAI;AAC7B,eAAO;MACT;MAEO,UAAU,SAAiB,IAAE;AAClC,cAAM,MAAM,OAAO,WAAW,MAAM;AACpC,aAAK,OAAO,GAAG;AACf,aAAK,OAAO,MAAM,QAAQ,KAAK,MAAM;AACrC,aAAK,UAAU;AACf,eAAO;MACT;MAEO,IAAI,aAAmB;AAC5B,aAAK,OAAO,YAAY,MAAM;AAC9B,oBAAY,KAAK,KAAK,QAAQ,KAAK,MAAM;AACzC,aAAK,UAAU,YAAY;AAC3B,eAAO;MACT;MAEQ,KAAK,MAAa;AACxB,YAAI,MAAM;AACR,eAAK,OAAO,KAAK,cAAc,IAAI;AAEnC,gBAAM,SAAS,KAAK,UAAU,KAAK,iBAAiB;AACpD,eAAK,OAAO,aAAa,QAAQ,KAAK,iBAAiB,CAAC;;AAE1D,eAAO,KAAK,OAAO,MAAM,OAAO,IAAI,GAAG,KAAK,MAAM;MACpD;MAEO,MAAM,MAAa;AACxB,cAAM,SAAS,KAAK,KAAK,IAAI;AAC7B,aAAK,SAAS;AACd,aAAK,iBAAiB;AACtB,aAAK,SAAS,OAAO,YAAY,KAAK,IAAI;AAC1C,eAAO;MACT;;AAjFF,YAAA,SAAA;;;;;;;;;;ACFA,QAAA,kBAAA;AAkBA,QAAM,SAAS,IAAI,gBAAA,OAAM;AAEzB,QAAM,UAAU,CAAC,SAAwC;AAEvD,aAAO,SAAS,CAAC,EAAE,SAAS,CAAC;AAC7B,iBAAW,OAAO,OAAO,KAAK,IAAI,GAAG;AACnC,eAAO,WAAW,GAAG,EAAE,WAAW,KAAK,GAAG,CAAC;;AAG7C,aAAO,WAAW,iBAAiB,EAAE,WAAW,MAAM;AAEtD,YAAM,aAAa,OAAO,WAAW,EAAE,EAAE,MAAK;AAG9C,YAAM,SAAS,WAAW,SAAS;AAEnC,aAAO,IAAI,gBAAA,OAAM,EAAG,SAAS,MAAM,EAAE,IAAI,UAAU,EAAE,MAAK;IAC5D;AAEA,QAAM,aAAa,MAAa;AAC9B,YAAM,WAAW,OAAO,YAAY,CAAC;AACrC,eAAS,aAAa,GAAG,CAAC;AAC1B,eAAS,aAAa,UAAU,CAAC;AACjC,aAAO;IACT;AAEA,QAAM,WAAW,CAACC,cAA4B;AAC5C,aAAO,OAAO,WAAWA,SAAQ,EAAE;QAAK;;MAAA;IAC1C;AAEA,QAAM,iCAAiC,SAAU,WAAmB,iBAAuB;AAEzF,aAAO,WAAW,SAAS,EAAE,SAAS,OAAO,WAAW,eAAe,CAAC,EAAE,UAAU,eAAe;AAEnG,aAAO,OAAO;QAAK;;MAAA;IACrB;AAEA,QAAM,8BAA8B,SAAU,gBAAsB;AAClE,aAAO,OAAO,UAAU,cAAc,EAAE;QAAK;;MAAA;IAC/C;AAEA,QAAM,QAAQ,CAAC,SAAwB;AACrC,aAAO,OAAO,WAAW,IAAI,EAAE;QAAK;;MAAA;IACtC;AAQA,QAAM,aAAoB,CAAA;AAE1B,QAAM,QAAQ,CAACC,WAA4B;AAOzC,YAAM,OAAOA,OAAM,QAAQ;AAC3B,UAAI,KAAK,SAAS,IAAI;AACpB,gBAAQ,MAAM,gEAAgE;AAC9E,gBAAQ,MAAM,wBAAwB,MAAM,KAAK,MAAM;AACvD,gBAAQ,MAAM,8DAA8D;;AAG9E,YAAMC,SAAQD,OAAM,SAAS;AAE7B,YAAM,MAAMC,OAAM;AAElB,YAAM,SAAS,OACZ,WAAW,IAAI,EACf,WAAWD,OAAM,IAAI,EACrB,SAAS,GAAG;AAEf,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,eAAO,SAASC,OAAM,CAAC,CAAC;;AAG1B,aAAO,OAAO;QAAK;;MAAA;IACrB;AAaA,QAAM,cAAc,IAAI,gBAAA,OAAM;AAQ9B,QAAM,cAAc,SAAU,QAAe,aAAyB;AACpE,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAM,YAAY,cAAc,YAAY,OAAO,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC;AACpE,YAAI,aAAa,MAAM;AAErB,iBAAO;YAAQ;;UAAA;AAEf,sBAAY,SAAS,EAAE;mBACd,qBAAqB,QAAQ;AAEtC,iBAAO;YAAQ;;UAAA;AAEf,sBAAY,SAAS,UAAU,MAAM;AACrC,sBAAY,IAAI,SAAS;eACpB;AAEL,iBAAO;YAAQ;;UAAA;AACf,sBAAY,SAAS,OAAO,WAAW,SAAS,CAAC;AACjD,sBAAY,UAAU,SAAS;;;IAGrC;AAEA,QAAM,OAAO,CAAC,SAAmB,CAAA,MAAc;AAE7C,YAAM,SAAS,OAAO,UAAU;AAChC,YAAM,YAAY,OAAO,aAAa;AACtC,YAAM,SAAS,OAAO,UAAU;AAChC,YAAM,SAAS,OAAO,UAAU;AAChC,YAAM,MAAM,OAAO;AAEnB,aAAO,WAAW,MAAM,EAAE,WAAW,SAAS;AAC9C,aAAO,SAAS,GAAG;AAEnB,kBAAY,QAAQ,OAAO,WAAW;AAEtC,aAAO,SAAS,GAAG;AACnB,aAAO,IAAI,YAAY,MAAK,CAAE;AAG9B,aAAO;QAAS,SAAQ,IAAmB;;MAAiB;AAC5D,aAAO,OAAO;QAAK;;MAAA;IACrB;AAOA,QAAM,eAAe,OAAO,KAAK,CAAA,IAAe,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,CAAI,CAAC;AAErG,QAAM,UAAU,CAAC,WAA6B;AAE5C,UAAI,CAAC,UAAW,CAAC,OAAO,UAAU,CAAC,OAAO,MAAO;AAC/C,eAAO;;AAGT,YAAM,SAAS,OAAO,UAAU;AAChC,YAAM,OAAO,OAAO,QAAQ;AAE5B,YAAM,eAAe,OAAO,WAAW,MAAM;AAC7C,YAAM,MAAM,IAAI,eAAe,IAAI;AAEnC,YAAM,OAAO,OAAO,YAAY,IAAI,GAAG;AACvC,WAAK,CAAC,IAAC;AACP,WAAK,aAAa,KAAK,CAAC;AACxB,WAAK,MAAM,QAAQ,GAAG,OAAO;AAC7B,WAAK,eAAe,CAAC,IAAI;AACzB,WAAK,cAAc,MAAM,KAAK,SAAS,CAAC;AACxC,aAAO;IACT;AAEA,QAAM,SAAS,CAAC,WAAmB,cAA6B;AAC9D,YAAM,SAAS,OAAO,YAAY,EAAE;AACpC,aAAO,aAAa,IAAI,CAAC;AACzB,aAAO,aAAa,MAAM,CAAC;AAC3B,aAAO,aAAa,MAAM,CAAC;AAC3B,aAAO,aAAa,WAAW,CAAC;AAChC,aAAO,aAAa,WAAW,EAAE;AACjC,aAAO;IACT;AAOA,QAAM,iBAAiB,CAAC,MAAY,WAA0B;AAC5D,YAAM,YAAY,OAAO,WAAW,MAAM;AAC1C,YAAM,MAAM,IAAI,YAAY;AAE5B,YAAM,SAAS,OAAO,YAAY,IAAI,GAAG;AACzC,aAAO,CAAC,IAAI;AACZ,aAAO,aAAa,KAAK,CAAC;AAC1B,aAAO,MAAM,QAAQ,GAAG,OAAO;AAC/B,aAAO,GAAG,IAAI;AACd,aAAO;IACT;AAEA,QAAM,sBAAsB,OAAO,WAAW,GAAG,EAAE;MAAK;;IAAA;AACxD,QAAM,yBAAyB,OAAO,WAAW,GAAG,EAAE;MAAK;;IAAA;AAE3D,QAAM,WAAW,CAAC,QAA2B;AAC3C,aAAO,IAAI,OACP,eAAc,IAAgB,GAAG,IAAI,IAAI,GAAG,IAAI,QAAQ,EAAE,EAAE,IAC5D,IAAI,SAAS,MACb,sBACA;IACN;AAEA,QAAM,QAAQ,CAAC,QAA2B;AACxC,YAAM,OAAO,GAAG,IAAI,IAAI,GAAG,IAAI,QAAQ,EAAE;AACzC,aAAO,eAAc,IAAa,IAAI;IACxC;AAEA,QAAM,WAAW,CAAC,UAAyB;AACzC,aAAO,OAAO,IAAI,KAAK,EAAE;QAAK;;MAAA;IAChC;AAEA,QAAM,WAAW,CAAC,YAA2B;AAC3C,aAAO,eAAc,KAAgB,OAAO;IAC9C;AAEA,QAAM,iBAAiB,CAAC,SAAuB,OAAO,KAAK,CAAC,MAAM,GAAM,GAAM,GAAM,CAAI,CAAC;AAEzF,QAAM,cAAc;MAAc;;IAAA;AAClC,QAAM,aAAa;MAAc;;IAAA;AACjC,QAAM,YAAY;MAAc;;IAAA;AAChC,QAAM,iBAAiB;MAAc;;IAAA;AAErC,QAAM,YAAY;MAChB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,MAAM;MACb,MAAM,MAAM;MACZ,KAAK,MAAM;MACX;MACA,UAAU,MAAM;MAChB;MACA;;AAGO,YAAA,YAAA;;;;;;;;;;AC/QT,QAAM,cAAc,OAAO,YAAY,CAAC;AAExC,QAAa,eAAb,MAAyB;MAMvB,YAAoB,SAAiB,GAAC;AAAlB,aAAA,SAAA;AALZ,aAAA,SAAiB;AAGjB,aAAA,WAAmB;MAEc;MAElC,UAAU,QAAgB,QAAc;AAC7C,aAAK,SAAS;AACd,aAAK,SAAS;MAChB;MAEO,QAAK;AACV,cAAM,SAAS,KAAK,OAAO,YAAY,KAAK,MAAM;AAClD,aAAK,UAAU;AACf,eAAO;MACT;MAEO,OAAI;AACT,cAAM,SAAS,KAAK,OAAO,KAAK,MAAM;AACtC,aAAK;AACL,eAAO;MACT;MAEO,QAAK;AACV,cAAM,SAAS,KAAK,OAAO,YAAY,KAAK,MAAM;AAClD,aAAK,UAAU;AACf,eAAO;MACT;MAEO,SAAM;AACX,cAAM,SAAS,KAAK,OAAO,aAAa,KAAK,MAAM;AACnD,aAAK,UAAU;AACf,eAAO;MACT;MAEO,OAAO,QAAc;AAC1B,cAAM,SAAS,KAAK,OAAO,SAAS,KAAK,UAAU,KAAK,QAAQ,KAAK,SAAS,MAAM;AACpF,aAAK,UAAU;AACf,eAAO;MACT;MAEO,UAAO;AACZ,cAAM,QAAQ,KAAK;AACnB,YAAI,MAAM;AAEV,eAAO,KAAK,OAAO,KAAK,MAAM,GAAG;QAAA;AACjC,aAAK,SAAS;AACd,eAAO,KAAK,OAAO,SAAS,KAAK,UAAU,OAAO,MAAM,CAAC;MAC3D;MAEO,MAAM,QAAc;AACzB,cAAM,SAAS,KAAK,OAAO,MAAM,KAAK,QAAQ,KAAK,SAAS,MAAM;AAClE,aAAK,UAAU;AACf,eAAO;MACT;;AAxDF,YAAA,eAAA;;;;;;;;;;ACDA,QAAA,aAAA;AA2BA,QAAA,kBAAA;AAGA,QAAM,cAAc;AAGpB,QAAM,aAAa;AAEnB,QAAM,gBAAgB,cAAc;AAOpC,QAAM,cAAc,OAAO,YAAY,CAAC;AAiCxC,QAAa,SAAb,MAAmB;MAOjB,YAAY,MAAoB;AANxB,aAAA,SAAiB;AACjB,aAAA,eAAuB;AACvB,aAAA,eAAuB;AACvB,aAAA,SAAS,IAAI,gBAAA,aAAY;AAI/B,aAAI,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,UAAS,UAAU;AAC3B,gBAAM,IAAI,MAAM,+BAA+B;;AAEjD,aAAK,QAAO,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,SAAQ;MAC5B;MAEO,MAAM,QAAgB,UAAyB;AACpD,aAAK,YAAY,MAAM;AACvB,cAAM,mBAAmB,KAAK,eAAe,KAAK;AAClD,YAAI,SAAS,KAAK;AAClB,eAAO,SAAS,iBAAiB,kBAAkB;AAEjD,gBAAM,OAAO,KAAK,OAAO,MAAM;AAE/B,gBAAM,SAAS,KAAK,OAAO,aAAa,SAAS,WAAW;AAC5D,gBAAM,oBAAoB,cAAc;AACxC,cAAI,oBAAoB,UAAU,kBAAkB;AAClD,kBAAM,UAAU,KAAK,aAAa,SAAS,eAAe,MAAM,QAAQ,KAAK,MAAM;AACnF,qBAAS,OAAO;AAChB,sBAAU;iBACL;AACL;;;AAGJ,YAAI,WAAW,kBAAkB;AAE/B,eAAK,SAAS;AACd,eAAK,eAAe;AACpB,eAAK,eAAe;eACf;AAEL,eAAK,eAAe,mBAAmB;AACvC,eAAK,eAAe;;MAExB;MAEQ,YAAY,QAAc;AAChC,YAAI,KAAK,eAAe,GAAG;AACzB,gBAAM,YAAY,KAAK,eAAe,OAAO;AAC7C,gBAAM,gBAAgB,YAAY,KAAK;AACvC,cAAI,gBAAgB,KAAK,OAAO,YAAY;AAE1C,gBAAI;AACJ,gBAAI,aAAa,KAAK,OAAO,cAAc,KAAK,gBAAgB,KAAK,cAAc;AAEjF,0BAAY,KAAK;mBACZ;AAEL,kBAAI,kBAAkB,KAAK,OAAO,aAAa;AAC/C,qBAAO,aAAa,iBAAiB;AACnC,mCAAmB;;AAErB,0BAAY,OAAO,YAAY,eAAe;;AAGhD,iBAAK,OAAO,KAAK,WAAW,GAAG,KAAK,cAAc,KAAK,eAAe,KAAK,YAAY;AACvF,iBAAK,SAAS;AACd,iBAAK,eAAe;;AAGtB,iBAAO,KAAK,KAAK,QAAQ,KAAK,eAAe,KAAK,YAAY;AAC9D,eAAK,eAAe;eACf;AACL,eAAK,SAAS;AACd,eAAK,eAAe;AACpB,eAAK,eAAe,OAAO;;MAE/B;MAEQ,aAAa,QAAgB,MAAc,QAAgB,OAAa;AAC9E,gBAAQ,MAAM;UACZ,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,WAAA;UACT,KAAA;AACE,mBAAO,KAAK,oBAAoB,QAAQ,QAAQ,KAAK;UACvD,KAAA;AACE,mBAAO,KAAK,4BAA4B,QAAQ,QAAQ,KAAK;UAC/D,KAAA;AACE,mBAAO,KAAK,0BAA0B,QAAQ,QAAQ,KAAK;UAC7D,KAAA;AACE,mBAAO,KAAK,yBAAyB,QAAQ,QAAQ,KAAK;UAC5D,KAAA;AACE,mBAAO,KAAK,4BAA4B,QAAQ,QAAQ,KAAK;UAC/D,KAAA;AACE,mBAAO,KAAK,4BAA4B,QAAQ,QAAQ,KAAK;UAC/D,KAAA;AACE,mBAAO,KAAK,oBAAoB,QAAQ,QAAQ,KAAK;UACvD,KAAA;AACE,mBAAO,KAAK,kBAAkB,QAAQ,QAAQ,OAAO,OAAO;UAC9D,KAAA;AACE,mBAAO,KAAK,kBAAkB,QAAQ,QAAQ,OAAO,QAAQ;UAC/D,KAAA;AACE,mBAAO,KAAK,2BAA2B,QAAQ,QAAQ,KAAK;UAC9D,KAAA;AACE,mBAAO,KAAK,iCAAiC,QAAQ,QAAQ,KAAK;UACpE,KAAA;AACE,mBAAO,KAAK,mBAAmB,QAAQ,QAAQ,KAAK;UACtD,KAAA;AACE,mBAAO,KAAK,oBAAoB,QAAQ,QAAQ,KAAK;UACvD,KAAA;AACE,mBAAO,KAAK,cAAc,QAAQ,QAAQ,KAAK;UACjD;AACE,mBAAO,IAAI,WAAA,cAAc,gCAAgC,KAAK,SAAS,EAAE,GAAG,QAAQ,OAAO;;MAEjG;MAEQ,0BAA0B,QAAgB,QAAgB,OAAa;AAC7E,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,SAAS,KAAK,OAAO,OAAO,CAAC;AACnC,eAAO,IAAI,WAAA,qBAAqB,QAAQ,MAAM;MAChD;MAEQ,4BAA4B,QAAgB,QAAgB,OAAa;AAC/E,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,OAAO,KAAK,OAAO,QAAO;AAChC,eAAO,IAAI,WAAA,uBAAuB,QAAQ,IAAI;MAChD;MAEQ,cAAc,QAAgB,QAAgB,OAAa;AACjE,cAAM,QAAQ,MAAM,MAAM,QAAQ,UAAU,SAAS,EAAE;AACvD,eAAO,IAAI,WAAA,gBAAgB,QAAQ,KAAK;MAC1C;MAEQ,mBAAmB,QAAgB,QAAgB,OAAa;AACtE,eAAO,KAAK,iBAAiB,QAAQ,QAAQ,OAAO,gBAAgB;MACtE;MAEQ,oBAAoB,QAAgB,QAAgB,OAAa;AACvE,eAAO,KAAK,iBAAiB,QAAQ,QAAQ,OAAO,iBAAiB;MACvE;MAEQ,iBAAiB,QAAgB,QAAgB,OAAe,aAAwB;AAC9F,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,WAAW,KAAK,OAAO,KAAI,MAAO;AACxC,cAAM,cAAc,KAAK,OAAO,MAAK;AACrC,cAAM,UAAU,IAAI,WAAA,aAAa,QAAQ,aAAa,UAAU,WAAW;AAC3E,iBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,kBAAQ,YAAY,CAAC,IAAI,KAAK,OAAO,MAAK;;AAE5C,eAAO;MACT;MAEQ,yBAAyB,QAAgB,QAAgB,OAAa;AAC5E,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,YAAY,KAAK,OAAO,MAAK;AACnC,cAAM,UAAU,KAAK,OAAO,QAAO;AACnC,cAAM,UAAU,KAAK,OAAO,QAAO;AACnC,eAAO,IAAI,WAAA,4BAA4B,QAAQ,WAAW,SAAS,OAAO;MAC5E;MAEQ,2BAA2B,QAAgB,QAAgB,OAAa;AAC9E,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,aAAa,KAAK,OAAO,MAAK;AACpC,cAAM,UAAU,IAAI,WAAA,sBAAsB,QAAQ,UAAU;AAC5D,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,kBAAQ,OAAO,CAAC,IAAI,KAAK,WAAU;;AAErC,eAAO;MACT;MAEQ,aAAU;AAChB,cAAM,OAAO,KAAK,OAAO,QAAO;AAChC,cAAM,UAAU,KAAK,OAAO,OAAM;AAClC,cAAM,WAAW,KAAK,OAAO,MAAK;AAClC,cAAM,aAAa,KAAK,OAAO,OAAM;AACrC,cAAM,eAAe,KAAK,OAAO,MAAK;AACtC,cAAM,mBAAmB,KAAK,OAAO,MAAK;AAC1C,cAAM,OAAO,KAAK,OAAO,MAAK,MAAO,IAAI,SAAS;AAClD,eAAO,IAAI,WAAA,MAAM,MAAM,SAAS,UAAU,YAAY,cAAc,kBAAkB,IAAI;MAC5F;MAEQ,iCAAiC,QAAgB,QAAgB,OAAa;AACpF,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,iBAAiB,KAAK,OAAO,MAAK;AACxC,cAAM,UAAU,IAAI,WAAA,4BAA4B,QAAQ,cAAc;AACtE,iBAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,kBAAQ,YAAY,CAAC,IAAI,KAAK,OAAO,MAAK;;AAE5C,eAAO;MACT;MAEQ,oBAAoB,QAAgB,QAAgB,OAAa;AACvE,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,aAAa,KAAK,OAAO,MAAK;AACpC,cAAM,SAAgB,IAAI,MAAM,UAAU;AAC1C,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,gBAAM,MAAM,KAAK,OAAO,MAAK;AAE7B,iBAAO,CAAC,IAAI,QAAQ,KAAK,OAAO,KAAK,OAAO,OAAO,GAAG;;AAExD,eAAO,IAAI,WAAA,eAAe,QAAQ,MAAM;MAC1C;MAEQ,4BAA4B,QAAgB,QAAgB,OAAa;AAC/E,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,OAAO,KAAK,OAAO,QAAO;AAChC,cAAM,QAAQ,KAAK,OAAO,QAAO;AACjC,eAAO,IAAI,WAAA,uBAAuB,QAAQ,MAAM,KAAK;MACvD;MAEQ,oBAAoB,QAAgB,QAAgB,OAAa;AACvE,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,YAAY,KAAK,OAAO,MAAK;AACnC,cAAM,YAAY,KAAK,OAAO,MAAK;AACnC,eAAO,IAAI,WAAA,sBAAsB,QAAQ,WAAW,SAAS;MAC/D;MAEO,4BAA4B,QAAgB,QAAgB,OAAa;AAC9E,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,OAAO,KAAK,OAAO,MAAK;AAE9B,cAAM,UAAgC;UACpC,MAAM;UACN;;AAGF,gBAAQ,MAAM;UACZ,KAAK;AACH;UACF,KAAK;AACH,gBAAI,QAAQ,WAAW,GAAG;AACxB,sBAAQ,OAAO;;AAEjB;UACF,KAAK;AACH,gBAAI,QAAQ,WAAW,IAAI;AACzB,sBAAQ,OAAO;AACf,oBAAM,OAAO,KAAK,OAAO,MAAM,CAAC;AAChC,qBAAO,IAAI,WAAA,0BAA0B,QAAQ,IAAI;;AAEnD;UACF,KAAK;AACH;AACE,sBAAQ,OAAO;AACf,sBAAQ,aAAa,CAAA;AACrB,kBAAI;AACJ,iBAAG;AACD,4BAAY,KAAK,OAAO,QAAO;AAC/B,oBAAI,WAAW;AACb,0BAAQ,WAAW,KAAK,SAAS;;uBAE5B;;AAEX;UACF,KAAK;AACH,oBAAQ,OAAO;AACf,oBAAQ,OAAO,KAAK,OAAO,OAAO,SAAS,CAAC;AAC5C;UACF,KAAK;AACH,oBAAQ,OAAO;AACf,oBAAQ,OAAO,KAAK,OAAO,OAAO,SAAS,CAAC;AAC5C;UACF;AACE,kBAAM,IAAI,MAAM,2CAA2C,IAAI;;AAEnE,eAAO;MACT;MAEQ,kBAAkB,QAAgB,QAAgB,OAAe,MAAiB;AACxF,aAAK,OAAO,UAAU,QAAQ,KAAK;AACnC,cAAM,SAAiC,CAAA;AACvC,YAAI,YAAY,KAAK,OAAO,OAAO,CAAC;AACpC,eAAO,cAAc,MAAM;AACzB,iBAAO,SAAS,IAAI,KAAK,OAAO,QAAO;AACvC,sBAAY,KAAK,OAAO,OAAO,CAAC;;AAGlC,cAAM,eAAe,OAAO;AAE5B,cAAM,UACJ,SAAS,WAAW,IAAI,WAAA,cAAc,QAAQ,YAAY,IAAI,IAAI,WAAA,cAAc,cAAc,QAAQ,IAAI;AAE5G,gBAAQ,WAAW,OAAO;AAC1B,gBAAQ,OAAO,OAAO;AACtB,gBAAQ,SAAS,OAAO;AACxB,gBAAQ,OAAO,OAAO;AACtB,gBAAQ,WAAW,OAAO;AAC1B,gBAAQ,mBAAmB,OAAO;AAClC,gBAAQ,gBAAgB,OAAO;AAC/B,gBAAQ,QAAQ,OAAO;AACvB,gBAAQ,SAAS,OAAO;AACxB,gBAAQ,QAAQ,OAAO;AACvB,gBAAQ,SAAS,OAAO;AACxB,gBAAQ,WAAW,OAAO;AAC1B,gBAAQ,aAAa,OAAO;AAC5B,gBAAQ,OAAO,OAAO;AACtB,gBAAQ,OAAO,OAAO;AACtB,gBAAQ,UAAU,OAAO;AACzB,eAAO;MACT;;AAvTF,YAAA,SAAA;;;;;;;;;;AC5EA,QAAA,aAAA;AAUoB,WAAA,eAAA,SAAA,iBAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAVX,WAAA;IAAa,EAAA,CAAA;AACtB,QAAA,eAAA;AASS,WAAA,eAAA,SAAA,aAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aATA,aAAA;IAAS,EAAA,CAAA;AAClB,QAAA,WAAA;AAEA,aAAgB,MAAM,QAA+B,UAAyB;AAC5E,YAAM,SAAS,IAAI,SAAA,OAAM;AACzB,aAAO,GAAG,QAAQ,CAAC,WAAmB,OAAO,MAAM,QAAQ,QAAQ,CAAC;AACpE,aAAO,IAAI,QAAQ,CAAC,YAAY,OAAO,GAAG,OAAO,MAAM,QAAO,CAAE,CAAC;IACnE;AAJA,YAAA,QAAA;;;;;ACJA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,oFAAoF,GAAG,mIAAmI;AAAA,QACzO;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,oFAAoF,GAAG,mIAAmI;AAAA,QACzO;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;;;;;ACTF,YAAA,UAAe,CAAA;;;;;ACFf;AAAA;AAAA,QAAM,EAAE,WAAW,gBAAgB,IAAI,eAAe;AAEtD,WAAO,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA;AAAA,IACF;AAKA,aAAS,uBAAuB;AAC9B,eAASC,WAAU,KAAK;AACtB,cAAM,MAAM;AACZ,eAAO,IAAI,IAAI,OAAO;AAAA,MACxB;AAEA,eAASC,iBAAgB,SAAS;AAChC,cAAM,MAAM;AACZ,eAAO,IAAI,QAAQ,OAAO;AAAA,MAC5B;AACA,aAAO;AAAA,QACL,WAAAD;AAAA,QACA,iBAAAC;AAAA,MACF;AAAA,IACF;AAKA,aAAS,2BAA2B;AAClC,eAASD,WAAU,KAAK;AACtB,cAAM,EAAE,iBAAiB,IAAI;AAC7B,eAAO,IAAI,iBAAiB,GAAG;AAAA,MACjC;AAEA,eAASC,iBAAgB,SAAS;AAChC,gBAAQ,OAAO,SAAS,OAAO;AAC/B,eAAO,QAAQ;AAAA,MACjB;AACA,aAAO;AAAA,QACL,WAAAD;AAAA,QACA,iBAAAC;AAAA,MACF;AAAA,IACF;AAOA,aAAS,sBAAsB;AAI7B,UAAI,OAAO,cAAc,YAAY,cAAc,QAAQ,OAAO,UAAU,cAAc,UAAU;AAElG,eAAO,UAAU,cAAc;AAAA,MACjC;AAEA,UAAI,OAAO,aAAa,YAAY;AAClC,cAAM,OAAO,IAAI,SAAS,MAAM,EAAE,IAAI,EAAE,OAAO,KAAK,EAAE,CAAC;AACvD,YAAI,OAAO,KAAK,OAAO,YAAY,KAAK,OAAO,QAAQ,KAAK,GAAG,OAAO;AACpE,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB;AACxB,UAAI,oBAAoB,GAAG;AACzB,eAAO,yBAAyB;AAAA,MAClC;AACA,aAAO,qBAAqB;AAAA,IAC9B;AAAA;AAAA;;;AClFA;AAAA;AAAA;AAEA,QAAM,eAAe,iBAAkB;AAEvC,QAAM,EAAE,OAAO,UAAU,IAAI;AAC7B,QAAM,EAAE,WAAW,gBAAgB,IAAI;AAEvC,QAAM,cAAc,UAAU,MAAM;AACpC,QAAM,aAAa,UAAU,KAAK;AAClC,QAAM,YAAY,UAAU,IAAI;AAGhC,QAAMC,cAAN,cAAyB,aAAa;AAAA,MACpC,YAAY,QAAQ;AAClB,cAAM;AACN,iBAAS,UAAU,CAAC;AAEpB,aAAK,SAAS,OAAO,UAAU,UAAU,OAAO,GAAG;AACnD,YAAI,OAAO,KAAK,WAAW,YAAY;AACrC,eAAK,SAAS,KAAK,OAAO,MAAM;AAAA,QAClC;AAEA,aAAK,aAAa,OAAO;AACzB,aAAK,+BAA+B,OAAO;AAC3C,aAAK,aAAa;AAClB,aAAK,mBAAmB,CAAC;AACzB,aAAK,MAAM,OAAO,OAAO;AACzB,aAAK,UAAU;AACf,aAAK,eAAe;AACpB,cAAM,OAAO;AACb,aAAK,GAAG,eAAe,SAAU,WAAW;AAC1C,cAAI,cAAc,WAAW;AAC3B,iBAAK,eAAe;AAAA,UACtB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MAEA,QAAQ,MAAM,MAAM;AAClB,cAAM,OAAO;AAEb,aAAK,cAAc;AACnB,aAAK,OAAO,WAAW,IAAI;AAC3B,aAAK,OAAO,QAAQ,MAAM,IAAI;AAE9B,aAAK,OAAO,KAAK,WAAW,WAAY;AACtC,cAAI,KAAK,YAAY;AACnB,iBAAK,OAAO,aAAa,MAAM,KAAK,4BAA4B;AAAA,UAClE;AACA,eAAK,KAAK,SAAS;AAAA,QACrB,CAAC;AAED,cAAM,oBAAoB,SAAU,OAAO;AAEzC,cAAI,KAAK,YAAY,MAAM,SAAS,gBAAgB,MAAM,SAAS,UAAU;AAC3E;AAAA,UACF;AACA,eAAK,KAAK,SAAS,KAAK;AAAA,QAC1B;AACA,aAAK,OAAO,GAAG,SAAS,iBAAiB;AAEzC,aAAK,OAAO,GAAG,SAAS,WAAY;AAClC,eAAK,KAAK,KAAK;AAAA,QACjB,CAAC;AAED,YAAI,CAAC,KAAK,KAAK;AACb,iBAAO,KAAK,gBAAgB,KAAK,MAAM;AAAA,QACzC;AAEA,aAAK,OAAO,KAAK,QAAQ,SAAU,QAAQ;AACzC,gBAAM,eAAe,OAAO,SAAS,MAAM;AAC3C,kBAAQ,cAAc;AAAA,YACpB,KAAK;AACH;AAAA,YACF,KAAK;AACH,mBAAK,OAAO,IAAI;AAChB,qBAAO,KAAK,KAAK,SAAS,IAAI,MAAM,6CAA6C,CAAC;AAAA,YACpF;AAEE,mBAAK,OAAO,IAAI;AAChB,qBAAO,KAAK,KAAK,SAAS,IAAI,MAAM,mDAAmD,CAAC;AAAA,UAC5F;AACA,gBAAM,UAAU;AAAA,YACd,QAAQ,KAAK;AAAA,UACf;AAEA,cAAI,KAAK,QAAQ,MAAM;AACrB,mBAAO,OAAO,SAAS,KAAK,GAAG;AAE/B,gBAAI,SAAS,KAAK,KAAK;AACrB,sBAAQ,MAAM,KAAK,IAAI;AAAA,YACzB;AAAA,UACF;AAEA,gBAAM,MAAM;AACZ,cAAI,IAAI,QAAQ,IAAI,KAAK,IAAI,MAAM,GAAG;AACpC,oBAAQ,aAAa;AAAA,UACvB;AACA,cAAI;AACF,iBAAK,SAAS,gBAAgB,OAAO;AAAA,UACvC,SAAS,KAAK;AACZ,mBAAO,KAAK,KAAK,SAAS,GAAG;AAAA,UAC/B;AACA,eAAK,gBAAgB,KAAK,MAAM;AAChC,eAAK,OAAO,GAAG,SAAS,iBAAiB;AAEzC,eAAK,KAAK,YAAY;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,MAEA,gBAAgB,QAAQ;AACtB,cAAM,QAAQ,CAAC,QAAQ;AACrB,gBAAM,YAAY,IAAI,SAAS,UAAU,iBAAiB,IAAI;AAC9D,cAAI,KAAK,cAAc;AACrB,iBAAK,KAAK,WAAW,GAAG;AAAA,UAC1B;AACA,eAAK,KAAK,WAAW,GAAG;AAAA,QAC1B,CAAC;AAAA,MACH;AAAA,MAEA,aAAa;AACX,aAAK,OAAO,MAAM,UAAU,WAAW,CAAC;AAAA,MAC1C;AAAA,MAEA,QAAQ,QAAQ;AACd,aAAK,OAAO,MAAM,UAAU,QAAQ,MAAM,CAAC;AAAA,MAC7C;AAAA,MAEA,OAAO,WAAW,WAAW;AAC3B,aAAK,MAAM,UAAU,OAAO,WAAW,SAAS,CAAC;AAAA,MACnD;AAAA,MAEA,SAAS,UAAU;AACjB,aAAK,MAAM,UAAU,SAAS,QAAQ,CAAC;AAAA,MACzC;AAAA,MAEA,+BAA+B,WAAW,iBAAiB;AACzD,aAAK,MAAM,UAAU,+BAA+B,WAAW,eAAe,CAAC;AAAA,MACjF;AAAA,MAEA,4BAA4B,gBAAgB;AAC1C,aAAK,MAAM,UAAU,4BAA4B,cAAc,CAAC;AAAA,MAClE;AAAA,MAEA,MAAM,QAAQ;AACZ,YAAI,CAAC,KAAK,OAAO,UAAU;AACzB,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,OAAO,MAAM,MAAM;AAAA,MACjC;AAAA,MAEA,MAAM,MAAM;AACV,aAAK,MAAM,UAAU,MAAM,IAAI,CAAC;AAAA,MAClC;AAAA;AAAA,MAGA,MAAM,OAAO;AACX,aAAK,MAAM,UAAU,MAAM,KAAK,CAAC;AAAA,MACnC;AAAA;AAAA,MAGA,KAAK,QAAQ;AACX,aAAK,MAAM,UAAU,KAAK,MAAM,CAAC;AAAA,MACnC;AAAA;AAAA,MAGA,QAAQ,QAAQ;AACd,aAAK,MAAM,UAAU,QAAQ,MAAM,CAAC;AAAA,MACtC;AAAA,MAEA,QAAQ;AACN,YAAI,KAAK,OAAO,UAAU;AACxB,eAAK,OAAO,MAAM,WAAW;AAAA,QAC/B;AAAA,MACF;AAAA,MAEA,OAAO;AACL,aAAK,UAAU;AACf,aAAK,MAAM,UAAU;AAAA,MACvB;AAAA,MAEA,MAAM;AACJ,aAAK,OAAO,IAAI;AAAA,MAClB;AAAA,MAEA,QAAQ;AACN,aAAK,OAAO,MAAM;AAAA,MACpB;AAAA,MAEA,MAAM;AAEJ,aAAK,UAAU;AACf,YAAI,CAAC,KAAK,eAAe,CAAC,KAAK,OAAO,UAAU;AAC9C,eAAK,OAAO,IAAI;AAChB;AAAA,QACF;AACA,eAAO,KAAK,OAAO,MAAM,WAAW,MAAM;AACxC,eAAK,OAAO,IAAI;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,MAEA,MAAM,KAAK;AACT,aAAK,MAAM,UAAU,MAAM,GAAG,CAAC;AAAA,MACjC;AAAA,MAEA,SAAS,KAAK;AACZ,aAAK,MAAM,UAAU,SAAS,GAAG,CAAC;AAAA,MACpC;AAAA,MAEA,kBAAkB,OAAO;AACvB,aAAK,MAAM,UAAU,SAAS,KAAK,CAAC;AAAA,MACtC;AAAA,MAEA,cAAc;AACZ,aAAK,MAAM,UAAU,SAAS,CAAC;AAAA,MACjC;AAAA,MAEA,aAAa,KAAK;AAChB,aAAK,MAAM,UAAU,SAAS,GAAG,CAAC;AAAA,MACpC;AAAA,IACF;AAEA,WAAO,UAAUA;AAAA;AAAA;;;AC7NjB;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,sFAAsF,GAAG,mIAAmI;AAAA,QAC3O;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF,IAAAC,kBAAA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,0FAA0F,GAAG,mIAAmI;AAAA,QAC/O;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,0GAA0G,GAAG,mIAAmI;AAAA,QAC/P;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA;AAkBA,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,cAAc,IAAI;AAC1B,QAAM,QAAQ,OAAO,MAAM;AAC3B,QAAM,WAAW,OAAO,SAAS;AAEjC,aAAS,UAAW,OAAO,KAAK,IAAI;AAClC,UAAI;AACJ,UAAI,KAAK,UAAU;AACjB,cAAM,MAAM,KAAK,QAAQ,EAAE,MAAM,KAAK;AACtC,eAAO,IAAI,MAAM,KAAK,OAAO;AAE7B,YAAI,KAAK,WAAW,EAAG,QAAO,GAAG;AAGjC,aAAK,MAAM;AACX,aAAK,WAAW;AAAA,MAClB,OAAO;AACL,aAAK,KAAK,KAAK,KAAK,QAAQ,EAAE,MAAM,KAAK;AACzC,eAAO,KAAK,KAAK,EAAE,MAAM,KAAK,OAAO;AAAA,MACvC;AAEA,WAAK,KAAK,IAAI,KAAK,IAAI;AAEvB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAI;AACF,eAAK,MAAM,KAAK,OAAO,KAAK,CAAC,CAAC,CAAC;AAAA,QACjC,SAAS,OAAO;AACd,iBAAO,GAAG,KAAK;AAAA,QACjB;AAAA,MACF;AAEA,WAAK,WAAW,KAAK,KAAK,EAAE,SAAS,KAAK;AAC1C,UAAI,KAAK,YAAY,CAAC,KAAK,cAAc;AACvC,WAAG,IAAI,MAAM,wBAAwB,CAAC;AACtC;AAAA,MACF;AAEA,SAAG;AAAA,IACL;AAEA,aAAS,MAAO,IAAI;AAElB,WAAK,KAAK,KAAK,KAAK,QAAQ,EAAE,IAAI;AAElC,UAAI,KAAK,KAAK,GAAG;AACf,YAAI;AACF,eAAK,MAAM,KAAK,OAAO,KAAK,KAAK,CAAC,CAAC;AAAA,QACrC,SAAS,OAAO;AACd,iBAAO,GAAG,KAAK;AAAA,QACjB;AAAA,MACF;AAEA,SAAG;AAAA,IACL;AAEA,aAAS,KAAM,MAAM,KAAK;AACxB,UAAI,QAAQ,QAAW;AACrB,aAAK,KAAK,GAAG;AAAA,MACf;AAAA,IACF;AAEA,aAAS,KAAM,UAAU;AACvB,aAAO;AAAA,IACT;AAEA,aAAS,MAAO,SAAS,QAAQ,SAAS;AAExC,gBAAU,WAAW;AACrB,eAAS,UAAU;AACnB,gBAAU,WAAW,CAAC;AAGtB,cAAQ,UAAU,QAAQ;AAAA,QACxB,KAAK;AAEH,cAAI,OAAO,YAAY,YAAY;AACjC,qBAAS;AACT,sBAAU;AAAA,UAEZ,WAAW,OAAO,YAAY,YAAY,EAAE,mBAAmB,WAAW,CAAC,QAAQ,OAAO,KAAK,GAAG;AAChG,sBAAU;AACV,sBAAU;AAAA,UACZ;AACA;AAAA,QAEF,KAAK;AAEH,cAAI,OAAO,YAAY,YAAY;AACjC,sBAAU;AACV,qBAAS;AACT,sBAAU;AAAA,UAEZ,WAAW,OAAO,WAAW,UAAU;AACrC,sBAAU;AACV,qBAAS;AAAA,UACX;AAAA,MACJ;AAEA,gBAAU,OAAO,OAAO,CAAC,GAAG,OAAO;AACnC,cAAQ,cAAc;AACtB,cAAQ,YAAY;AACpB,cAAQ,QAAQ;AAChB,cAAQ,qBAAqB;AAE7B,YAAM,SAAS,IAAI,UAAU,OAAO;AAEpC,aAAO,KAAK,IAAI;AAChB,aAAO,QAAQ,IAAI,IAAI,cAAc,MAAM;AAC3C,aAAO,UAAU;AACjB,aAAO,SAAS;AAChB,aAAO,YAAY,QAAQ;AAC3B,aAAO,eAAe,QAAQ,gBAAgB;AAC9C,aAAO,WAAW;AAClB,aAAO,WAAW,SAAU,KAAK,IAAI;AAEnC,aAAK,eAAe,eAAe;AACnC,WAAG,GAAG;AAAA,MACR;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC5IjB;AAAA;AAAA;AAEA,QAAI,OAAO;AAAX,QACI,SAAS,kBAAkB;AAD/B,QAEI,QAAQ;AAFZ,QAGI,OAAO;AAHX,QAII,cAAc;AAJlB,QAKI,QAAS,QAAQ,aAAa;AALlC,QAMI,aAAa,QAAQ;AAIzB,QAAI,UAAU;AAAd,QACI,UAAU;AADd,QAEI,SAAU;AAFd,QAGI,UAAU;AAEd,aAAS,UAAU,MAAM;AACrB,cAAS,OAAO,WAAW;AAAA,IAC/B;AAEA,QAAI,aAAa,CAAE,QAAQ,QAAQ,YAAY,QAAQ,UAAW;AAClE,QAAI,aAAa,WAAW;AAC5B,QAAI,UAAU,WAAY,aAAY,CAAE;AAGxC,aAAS,OAAO;AACZ,UAAI,aACA,sBAAsB,UACpB,SAAS,WAAW;AAG1B,UAAI,YAAY;AACZ,YAAI,OAAO,MAAM,UAAU,MAAM,KAAK,SAAS,EAAE,OAAO,IAAI;AAC5D,mBAAW,MAAO,KAAK,OAAO,MAAM,MAAM,IAAI,CAAE;AAAA,MACpD;AAAA,IACJ;AAGA,WAAO,eAAe,OAAO,SAAS,SAAS;AAAA,MAC3C,KAAM,WAAW;AACb,eAAO;AAAA,MACX;AAAA,MACA,KAAM,SAAS,KAAK;AAChB,gBAAQ;AAAA,MACZ;AAAA,IACJ,CAAC;AAGD,WAAO,QAAQ,SAAS,SAAS,QAAQ;AACrC,UAAI,MAAM;AACV,mBAAa;AACb,aAAO;AAAA,IACX;AAEA,WAAO,QAAQ,cAAc,SAAS,QAAO;AACzC,UAAI,MAAM,UAAU,QAAQ;AAC5B,UAAI,OAAO,IAAI,eACX,QACE,KAAK,KAAM,IAAI,WAAW,MAAO,cAAc,aAAc,IAC7D,KAAK,KAAM,IAAI,QAAQ,MAAM,SAAU;AAE7C,aAAO;AAAA,IACX;AAEA,WAAO,QAAQ,YAAY,SAAS,OAAO,OAAO;AAC9C,UAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,KAAK,YAAY,GAAG;AACjE,eAAO;AAAA,MACX;AAEA,UAAI,OAAO;AACP,eAAO;AAAA,MACX;AAEA,cAAQ,SAAS;AAEjB,UAAI,CAAE,UAAU,MAAM,IAAI,GAAG;AACzB,aAAK,mDAAmD,KAAK;AAC7D,eAAO;AAAA,MACX;AAEA,UAAI,MAAM,QAAQ,UAAU,UAAU;AAElC,aAAK,oGAAoG,KAAK;AAC9G,eAAO;AAAA,MACX;AAEA,aAAO;AAAA,IACX;AAGA,QAAI,UAAU,OAAO,QAAQ,QAAQ,SAAS,UAAU,OAAO;AAC3D,aAAO,WAAW,MAAM,GAAG,EAAE,EAAE,OAAO,SAAS,MAAM,OAAO,KAAI;AAC5D,YAAI,OAAO,GAAG;AAEV,cAAK,OAAQ,SAAS,KAAK,KAAK,WAAY,MAAM,OAAQ,MAAM,KAAK,CAAE,GAAI;AACvE,mBAAO,QAAQ;AAAA,UACnB;AAAA,QACJ;AACA,eAAO,SACH,MAAM,KAAK,MAAM,OACf,MAAM,KAAK,MAAM,SAAS,KAAK;AAAA,MAEzC,GAAG,IAAI;AAAA,IACX;AAGA,WAAO,QAAQ,cAAc,SAAS,UAAU,QAAQ,IAAI;AACxD,UAAI;AACJ,UAAI,aAAa,OAAO,KAAK,MAAM,CAAC;AAEpC,eAAS,OAAO,MAAM;AAClB,YAAI,QAAQ,UAAU,IAAI;AAC1B,YAAI,SAAS,aAAa,KAAK,KAAK,QAAQ,UAAU,KAAK,GAAG;AAC1D,iBAAO,MAAM,OAAO;AACpB,qBAAW,IAAI;AAAA,QACnB;AAAA,MACJ;AAEA,UAAI,QAAQ,WAAW;AACnB,eAAO,QAAQ;AACf,WAAG,IAAI;AAAA,MACX;AAEA,UAAI,QAAQ,SAAS,KAAK;AACtB,eAAO,QAAQ;AACf,aAAK,sCAAsC,GAAG;AAC9C,WAAG,MAAS;AAAA,MAChB;AAEA,aAAO,GAAG,SAAS,KAAK;AACxB,iBACK,GAAG,QAAQ,MAAM,EACjB,GAAG,OAAO,KAAK,EACf,GAAG,SAAS,KAAK;AAAA,IAG1B;AAGA,QAAI,YAAY,OAAO,QAAQ,YAAY,SAAS,MAAM;AACtD,UAAI,KAAK,SAAS,MAAM,KAAK,MAAM,OAAO,GAAG;AACzC,eAAO;AAAA,MACX;AAEA,UAAI,UAAU;AACd,UAAI,WAAW;AACf,UAAI,WAAW;AACf,UAAI,WAAW;AACf,UAAI,SAAS;AACb,UAAI,MAAM,CAAC;AACX,UAAI,cAAc;AAClB,UAAI,WAAW,SAAS,KAAK,IAAI,IAAI;AACjC,YAAI,QAAQ,KAAK,UAAU,IAAI,EAAE;AAEjC,YAAI,CAAE,OAAO,eAAe,KAAK,QAAQ,KAAK,oBAAoB,GAAG;AACjE,kBAAQ,MAAM,QAAQ,cAAc,IAAI;AAAA,QAC5C;AAEA,YAAK,WAAW,GAAG,CAAE,IAAI;AAAA,MAC7B;AAEA,eAAS,IAAI,GAAI,IAAI,KAAK,SAAO,GAAI,KAAK,GAAG;AACzC,kBAAU,KAAK,OAAO,IAAE,CAAC;AACzB,mBAAW,KAAK,OAAO,CAAC;AAExB,sBAAe,YAAY,aAAW;AAEtC,YAAI,aAAa;AACb,mBAAS,UAAU,QAAQ;AAC3B;AAAA,QACJ;AAEA,YAAI,KAAK,KAAK,WAAW,OAAO,aAAa,MAAM;AAC/C,mBAAS,UAAU,UAAU,IAAE,CAAC;AAEhC,qBAAW,IAAE;AACb,sBAAY;AAAA,QAChB;AAAA,MACJ;AAEA,YAAQ,OAAO,KAAK,GAAG,EAAE,WAAW,aAAe,MAAM;AAEzD,aAAO;AAAA,IACX;AAGA,QAAI,eAAe,OAAO,QAAQ,eAAe,SAAS,OAAM;AAC5D,UAAI,QAAQ;AAAA;AAAA,QAER,GAAI,SAAS,GAAE;AACX,iBAAO,EAAE,SAAS;AAAA,QACtB;AAAA;AAAA,QAEA,GAAI,SAAS,GAAE;AACX,cAAI,MAAM,KAAK;AACX,mBAAO;AAAA,UACX;AACA,cAAI,OAAO,CAAC;AACZ,iBACI,SAAS,CAAC,KACR,IAAI,KACJ,IAAI,oBACJ,KAAK,MAAM,CAAC,MAAM;AAAA,QAE5B;AAAA;AAAA,QAEA,GAAI,SAAS,GAAE;AACX,iBAAO,EAAE,SAAS;AAAA,QACtB;AAAA;AAAA,QAEA,GAAI,SAAS,GAAE;AACX,iBAAO,EAAE,SAAS;AAAA,QACtB;AAAA;AAAA,QAEA,GAAI,SAAS,GAAE;AACX,iBAAO,EAAE,SAAS;AAAA,QACtB;AAAA,MACJ;AAEA,eAAS,MAAM,GAAI,MAAM,WAAW,QAAS,OAAO,GAAG;AACnD,YAAI,OAAO,MAAM,GAAG;AACpB,YAAI,QAAQ,MAAO,WAAW,GAAG,CAAE,KAAK;AAExC,YAAI,MAAM,KAAK,KAAK;AACpB,YAAI,CAAC,KAAK;AACN,iBAAO;AAAA,QACX;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACvOA;AAAA;AAAA;AAEA,QAAI,OAAO;AAAX,QACI,KAAK;AADT,QAEI,SAAS;AAIb,WAAO,UAAU,SAAS,UAAU,IAAI;AACpC,UAAI,OAAO,OAAO,YAAY;AAE9B,SAAG,KAAK,MAAM,SAAS,KAAK,MAAK;AAC7B,YAAI,OAAO,CAAC,OAAO,UAAU,MAAM,IAAI,GAAG;AACtC,iBAAO,GAAG,MAAS;AAAA,QACvB;AAEA,YAAI,KAAK,GAAG,iBAAiB,IAAI;AAEjC,eAAO,YAAY,UAAU,IAAI,EAAE;AAAA,MACvC,CAAC;AAAA,IACL;AAEA,WAAO,QAAQ,SAAS,OAAO;AAAA;AAAA;;;ACtB/B;AAAA;AAAA;AAEA,QAAM,eAAe,iBAAkB;AACvC,QAAM,QAAQ;AACd,QAAM,OAAO;AACb,QAAMC,iBAAgB;AAEtB,QAAM,uBAAuB;AAC7B,QAAMC,SAAQ;AACd,QAAMC,YAAW;AACjB,QAAMC,cAAa;AACnB,QAAM,SAAS;AAEf,QAAMC,UAAN,cAAqB,aAAa;AAAA,MAChC,YAAY,QAAQ;AAClB,cAAM;AAEN,aAAK,uBAAuB,IAAI,qBAAqB,MAAM;AAC3D,aAAK,OAAO,KAAK,qBAAqB;AACtC,aAAK,WAAW,KAAK,qBAAqB;AAC1C,aAAK,OAAO,KAAK,qBAAqB;AACtC,aAAK,OAAO,KAAK,qBAAqB;AAItC,eAAO,eAAe,MAAM,YAAY;AAAA,UACtC,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,OAAO,KAAK,qBAAqB;AAAA,QACnC,CAAC;AAED,aAAK,cAAc,KAAK,qBAAqB;AAE7C,cAAM,IAAI,UAAU,CAAC;AAErB,aAAK,WAAW,EAAE,WAAW,OAAO;AACpC,aAAK,SAAS,IAAIJ,eAAc,EAAE,KAAK;AACvC,aAAK,UAAU;AACf,aAAK,SAAS;AACd,aAAK,cAAc;AACnB,aAAK,aAAa;AAClB,aAAK,mBAAmB;AACxB,aAAK,aAAa;AAElB,aAAK,uBAAuB,QAAQ,EAAE,oBAAoB;AAC1D,aAAK,aACH,EAAE,cACF,IAAIG,YAAW;AAAA,UACb,QAAQ,EAAE;AAAA,UACV,KAAK,KAAK,qBAAqB;AAAA,UAC/B,WAAW,EAAE,aAAa;AAAA,UAC1B,6BAA6B,EAAE,+BAA+B;AAAA,UAC9D,UAAU,KAAK,qBAAqB,mBAAmB;AAAA,QACzD,CAAC;AACH,aAAK,aAAa,CAAC;AACnB,aAAK,SAAS,EAAE,UAAUD,UAAS;AACnC,aAAK,YAAY;AACjB,aAAK,YAAY;AACjB,aAAK,MAAM,KAAK,qBAAqB,OAAO;AAI5C,YAAI,KAAK,OAAO,KAAK,IAAI,KAAK;AAC5B,iBAAO,eAAe,KAAK,KAAK,OAAO;AAAA,YACrC,YAAY;AAAA,UACd,CAAC;AAAA,QACH;AAEA,aAAK,2BAA2B,EAAE,2BAA2B;AAAA,MAC/D;AAAA,MAEA,iBAAiB,KAAK;AACpB,cAAM,eAAe,CAAC,UAAU;AAC9B,kBAAQ,SAAS,MAAM;AACrB,kBAAM,YAAY,KAAK,KAAK,UAAU;AAAA,UACxC,CAAC;AAAA,QACH;AAEA,YAAI,KAAK,aAAa;AACpB,uBAAa,KAAK,WAAW;AAC7B,eAAK,cAAc;AAAA,QACrB;AAEA,aAAK,WAAW,QAAQ,YAAY;AACpC,aAAK,WAAW,SAAS;AAAA,MAC3B;AAAA,MAEA,SAAS,UAAU;AACjB,cAAM,OAAO;AACb,cAAM,MAAM,KAAK;AACjB,aAAK,sBAAsB;AAE3B,YAAI,KAAK,eAAe,KAAK,YAAY;AACvC,gBAAM,MAAM,IAAI,MAAM,+DAA+D;AACrF,kBAAQ,SAAS,MAAM;AACrB,qBAAS,GAAG;AAAA,UACd,CAAC;AACD;AAAA,QACF;AACA,aAAK,cAAc;AAEnB,YAAI,KAAK,2BAA2B,GAAG;AACrC,eAAK,0BAA0B,WAAW,MAAM;AAC9C,gBAAI,UAAU;AACd,gBAAI,OAAO,QAAQ,IAAI,MAAM,iBAAiB,CAAC;AAAA,UACjD,GAAG,KAAK,wBAAwB;AAEhC,cAAI,KAAK,wBAAwB,OAAO;AACtC,iBAAK,wBAAwB,MAAM;AAAA,UACrC;AAAA,QACF;AAEA,YAAI,KAAK,QAAQ,KAAK,KAAK,QAAQ,GAAG,MAAM,GAAG;AAC7C,cAAI,QAAQ,KAAK,OAAO,eAAe,KAAK,IAAI;AAAA,QAClD,OAAO;AACL,cAAI,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,QAClC;AAGA,YAAI,GAAG,WAAW,WAAY;AAC5B,cAAI,KAAK,KAAK;AACZ,gBAAI,WAAW;AAAA,UACjB,OAAO;AACL,gBAAI,QAAQ,KAAK,eAAe,CAAC;AAAA,UACnC;AAAA,QACF,CAAC;AAED,YAAI,GAAG,cAAc,WAAY;AAC/B,cAAI,QAAQ,KAAK,eAAe,CAAC;AAAA,QACnC,CAAC;AAED,aAAK,iBAAiB,GAAG;AAEzB,YAAI,KAAK,OAAO,MAAM;AACpB,gBAAM,QAAQ,KAAK,UAAU,IAAI,MAAM,uBAAuB,IAAI,IAAI,MAAM,oCAAoC;AAEhH,uBAAa,KAAK,uBAAuB;AACzC,eAAK,iBAAiB,KAAK;AAC3B,eAAK,SAAS;AAEd,cAAI,CAAC,KAAK,SAAS;AAKjB,gBAAI,KAAK,eAAe,CAAC,KAAK,kBAAkB;AAC9C,kBAAI,KAAK,qBAAqB;AAC5B,qBAAK,oBAAoB,KAAK;AAAA,cAChC,OAAO;AACL,qBAAK,kBAAkB,KAAK;AAAA,cAC9B;AAAA,YACF,WAAW,CAAC,KAAK,kBAAkB;AACjC,mBAAK,kBAAkB,KAAK;AAAA,YAC9B;AAAA,UACF;AAEA,kBAAQ,SAAS,MAAM;AACrB,iBAAK,KAAK,KAAK;AAAA,UACjB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,MAEA,QAAQ,UAAU;AAChB,YAAI,UAAU;AACZ,eAAK,SAAS,QAAQ;AACtB;AAAA,QACF;AAEA,eAAO,IAAI,KAAK,SAAS,CAAC,SAAS,WAAW;AAC5C,eAAK,SAAS,CAAC,UAAU;AACvB,gBAAI,OAAO;AACT,qBAAO,KAAK;AAAA,YACd,OAAO;AACL,sBAAQ;AAAA,YACV;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,MAEA,iBAAiB,KAAK;AAEpB,YAAI,GAAG,mCAAmC,KAAK,6BAA6B,KAAK,IAAI,CAAC;AAEtF,YAAI,GAAG,6BAA6B,KAAK,uBAAuB,KAAK,IAAI,CAAC;AAE1E,YAAI,GAAG,sBAAsB,KAAK,gBAAgB,KAAK,IAAI,CAAC;AAC5D,YAAI,GAAG,8BAA8B,KAAK,wBAAwB,KAAK,IAAI,CAAC;AAC5E,YAAI,GAAG,2BAA2B,KAAK,qBAAqB,KAAK,IAAI,CAAC;AACtE,YAAI,GAAG,kBAAkB,KAAK,sBAAsB,KAAK,IAAI,CAAC;AAC9D,YAAI,GAAG,SAAS,KAAK,kBAAkB,KAAK,IAAI,CAAC;AACjD,YAAI,GAAG,gBAAgB,KAAK,oBAAoB,KAAK,IAAI,CAAC;AAC1D,YAAI,GAAG,iBAAiB,KAAK,qBAAqB,KAAK,IAAI,CAAC;AAC5D,YAAI,GAAG,UAAU,KAAK,cAAc,KAAK,IAAI,CAAC;AAC9C,YAAI,GAAG,kBAAkB,KAAK,sBAAsB,KAAK,IAAI,CAAC;AAC9D,YAAI,GAAG,WAAW,KAAK,eAAe,KAAK,IAAI,CAAC;AAChD,YAAI,GAAG,mBAAmB,KAAK,uBAAuB,KAAK,IAAI,CAAC;AAChE,YAAI,GAAG,cAAc,KAAK,kBAAkB,KAAK,IAAI,CAAC;AACtD,YAAI,GAAG,mBAAmB,KAAK,uBAAuB,KAAK,IAAI,CAAC;AAChE,YAAI,GAAG,iBAAiB,KAAK,qBAAqB,KAAK,IAAI,CAAC;AAC5D,YAAI,GAAG,kBAAkB,KAAK,sBAAsB,KAAK,IAAI,CAAC;AAC9D,YAAI,GAAG,YAAY,KAAK,gBAAgB,KAAK,IAAI,CAAC;AAClD,YAAI,GAAG,gBAAgB,KAAK,oBAAoB,KAAK,IAAI,CAAC;AAAA,MAC5D;AAAA;AAAA;AAAA,MAIA,aAAa,IAAI;AACf,cAAM,MAAM,KAAK;AACjB,YAAI,OAAO,KAAK,aAAa,YAAY;AACvC,eAAK,SACF,QAAQ,EACR,KAAK,MAAM,KAAK,SAAS,CAAC,EAC1B,KAAK,CAAC,SAAS;AACd,gBAAI,SAAS,QAAW;AACtB,kBAAI,OAAO,SAAS,UAAU;AAC5B,oBAAI,KAAK,SAAS,IAAI,UAAU,2BAA2B,CAAC;AAC5D;AAAA,cACF;AACA,mBAAK,qBAAqB,WAAW,KAAK,WAAW;AAAA,YACvD,OAAO;AACL,mBAAK,qBAAqB,WAAW,KAAK,WAAW;AAAA,YACvD;AACA,eAAG;AAAA,UACL,CAAC,EACA,MAAM,CAAC,QAAQ;AACd,gBAAI,KAAK,SAAS,GAAG;AAAA,UACvB,CAAC;AAAA,QACL,WAAW,KAAK,aAAa,MAAM;AACjC,aAAG;AAAA,QACL,OAAO;AACL,cAAI;AACF,kBAAM,SAAS;AACf,mBAAO,KAAK,sBAAsB,CAAC,SAAS;AAC1C,kBAAI,WAAc,MAAM;AACtB,qBAAK,qBAAqB,WAAW,KAAK,WAAW;AAAA,cACvD;AACA,iBAAG;AAAA,YACL,CAAC;AAAA,UACH,SAAS,GAAG;AACV,iBAAK,KAAK,SAAS,CAAC;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAAA,MAEA,6BAA6B,KAAK;AAChC,aAAK,aAAa,MAAM;AACtB,eAAK,WAAW,SAAS,KAAK,QAAQ;AAAA,QACxC,CAAC;AAAA,MACH;AAAA,MAEA,uBAAuB,KAAK;AAC1B,aAAK,aAAa,YAAY;AAC5B,cAAI;AACF,kBAAM,iBAAiB,MAAM,OAAO,wBAAwB,KAAK,MAAM,KAAK,UAAU,IAAI,IAAI;AAC9F,iBAAK,WAAW,SAAS,cAAc;AAAA,UACzC,SAAS,GAAG;AACV,iBAAK,KAAK,SAAS,CAAC;AAAA,UACtB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MAEA,gBAAgB,KAAK;AACnB,aAAK,aAAa,MAAM;AACtB,cAAI;AACF,iBAAK,cAAc,KAAK,aAAa,IAAI,YAAY,KAAK,wBAAwB,KAAK,WAAW,MAAM;AACxG,iBAAK,WAAW,+BAA+B,KAAK,YAAY,WAAW,KAAK,YAAY,QAAQ;AAAA,UACtG,SAAS,KAAK;AACZ,iBAAK,WAAW,KAAK,SAAS,GAAG;AAAA,UACnC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MAEA,MAAM,wBAAwB,KAAK;AACjC,YAAI;AACF,gBAAM,KAAK;AAAA,YACT,KAAK;AAAA,YACL,KAAK;AAAA,YACL,IAAI;AAAA,YACJ,KAAK,wBAAwB,KAAK,WAAW;AAAA,UAC/C;AACA,eAAK,WAAW,4BAA4B,KAAK,YAAY,QAAQ;AAAA,QACvE,SAAS,KAAK;AACZ,eAAK,WAAW,KAAK,SAAS,GAAG;AAAA,QACnC;AAAA,MACF;AAAA,MAEA,qBAAqB,KAAK;AACxB,YAAI;AACF,eAAK,gBAAgB,KAAK,aAAa,IAAI,IAAI;AAC/C,eAAK,cAAc;AAAA,QACrB,SAAS,KAAK;AACZ,eAAK,WAAW,KAAK,SAAS,GAAG;AAAA,QACnC;AAAA,MACF;AAAA,MAEA,sBAAsB,KAAK;AACzB,aAAK,YAAY,IAAI;AACrB,aAAK,YAAY,IAAI;AAAA,MACvB;AAAA,MAEA,qBAAqB,KAAK;AACxB,YAAI,KAAK,aAAa;AACpB,eAAK,cAAc;AACnB,eAAK,aAAa;AAClB,uBAAa,KAAK,uBAAuB;AAGzC,cAAI,KAAK,qBAAqB;AAC5B,iBAAK,oBAAoB,MAAM,IAAI;AAGnC,iBAAK,sBAAsB;AAAA,UAC7B;AACA,eAAK,KAAK,SAAS;AAAA,QACrB;AACA,cAAM,EAAE,YAAY,IAAI;AACxB,aAAK,cAAc;AACnB,aAAK,gBAAgB;AACrB,YAAI,aAAa;AACf,sBAAY,oBAAoB,KAAK,UAAU;AAAA,QACjD;AACA,aAAK,iBAAiB;AAAA,MACxB;AAAA;AAAA;AAAA,MAIA,4BAA4B,KAAK;AAC/B,YAAI,KAAK,kBAAkB;AAEzB;AAAA,QACF;AACA,aAAK,mBAAmB;AACxB,qBAAa,KAAK,uBAAuB;AACzC,YAAI,KAAK,qBAAqB;AAC5B,iBAAO,KAAK,oBAAoB,GAAG;AAAA,QACrC;AACA,aAAK,KAAK,SAAS,GAAG;AAAA,MACxB;AAAA;AAAA;AAAA;AAAA,MAKA,kBAAkB,KAAK;AACrB,YAAI,KAAK,aAAa;AACpB,iBAAO,KAAK,4BAA4B,GAAG;AAAA,QAC7C;AACA,aAAK,aAAa;AAClB,aAAK,iBAAiB,GAAG;AACzB,aAAK,KAAK,SAAS,GAAG;AAAA,MACxB;AAAA;AAAA,MAGA,oBAAoB,KAAK;AACvB,YAAI,KAAK,aAAa;AACpB,iBAAO,KAAK,4BAA4B,GAAG;AAAA,QAC7C;AACA,cAAM,cAAc,KAAK;AAEzB,YAAI,CAAC,aAAa;AAChB,eAAK,kBAAkB,GAAG;AAC1B;AAAA,QACF;AAEA,aAAK,cAAc;AACnB,oBAAY,YAAY,KAAK,KAAK,UAAU;AAAA,MAC9C;AAAA,MAEA,sBAAsB,KAAK;AAEzB,aAAK,YAAY,qBAAqB,GAAG;AAAA,MAC3C;AAAA,MAEA,eAAe,KAAK;AAElB,aAAK,YAAY,cAAc,GAAG;AAAA,MACpC;AAAA,MAEA,uBAAuB,KAAK;AAE1B,aAAK,YAAY,sBAAsB,KAAK,UAAU;AAAA,MACxD;AAAA,MAEA,kBAAkB,KAAK;AAErB,aAAK,YAAY,iBAAiB,KAAK,UAAU;AAAA,MACnD;AAAA,MAEA,uBAAuB,KAAK;AAC1B,YAAI,KAAK,eAAe,MAAM;AAC5B,gBAAM,QAAQ,IAAI,MAAM,2DAA2D;AACnF,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF;AAEA,aAAK,YAAY,sBAAsB,KAAK,KAAK,UAAU;AAAA,MAC7D;AAAA,MAEA,uBAAuB;AACrB,YAAI,KAAK,eAAe,MAAM;AAC5B,gBAAM,QAAQ,IAAI,MAAM,yDAAyD;AACjF,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF;AAIA,YAAI,KAAK,YAAY,MAAM;AACzB,eAAK,WAAW,iBAAiB,KAAK,YAAY,IAAI,IAAI,KAAK,YAAY;AAAA,QAC7E;AAAA,MACF;AAAA,MAEA,sBAAsB,KAAK;AACzB,aAAK,YAAY,qBAAqB,KAAK,UAAU;AAAA,MACvD;AAAA,MAEA,gBAAgB,KAAK;AACnB,aAAK,YAAY,eAAe,KAAK,KAAK,UAAU;AAAA,MACtD;AAAA,MAEA,oBAAoB,KAAK;AACvB,aAAK,KAAK,gBAAgB,GAAG;AAAA,MAC/B;AAAA,MAEA,cAAc,KAAK;AACjB,aAAK,KAAK,UAAU,GAAG;AAAA,MACzB;AAAA,MAEA,iBAAiB;AACf,cAAM,SAAS,KAAK;AAEpB,cAAM,OAAO;AAAA,UACX,MAAM,OAAO;AAAA,UACb,UAAU,OAAO;AAAA,QACnB;AAEA,cAAM,UAAU,OAAO,oBAAoB,OAAO;AAClD,YAAI,SAAS;AACX,eAAK,mBAAmB;AAAA,QAC1B;AACA,YAAI,OAAO,aAAa;AACtB,eAAK,cAAc,KAAK,OAAO;AAAA,QACjC;AACA,YAAI,OAAO,mBAAmB;AAC5B,eAAK,oBAAoB,OAAO,SAAS,OAAO,mBAAmB,EAAE,CAAC;AAAA,QACxE;AACA,YAAI,OAAO,cAAc;AACvB,eAAK,eAAe,OAAO,SAAS,OAAO,cAAc,EAAE,CAAC;AAAA,QAC9D;AACA,YAAI,OAAO,qCAAqC;AAC9C,eAAK,sCAAsC,OAAO,SAAS,OAAO,qCAAqC,EAAE,CAAC;AAAA,QAC5G;AACA,YAAI,OAAO,SAAS;AAClB,eAAK,UAAU,OAAO;AAAA,QACxB;AAEA,eAAO;AAAA,MACT;AAAA,MAEA,OAAO,QAAQ,OAAO;AACpB,YAAI,OAAO,gBAAgB,OAAO;AAChC,gBAAM,MAAM,KAAK;AAEjB,cAAI,KAAK,QAAQ,KAAK,KAAK,QAAQ,GAAG,MAAM,GAAG;AAC7C,gBAAI,QAAQ,KAAK,OAAO,eAAe,KAAK,IAAI;AAAA,UAClD,OAAO;AACL,gBAAI,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,UAClC;AAGA,cAAI,GAAG,WAAW,WAAY;AAC5B,gBAAI,OAAO,OAAO,WAAW,OAAO,SAAS;AAAA,UAC/C,CAAC;AAAA,QACH,WAAW,OAAO,WAAW,QAAQ,KAAK,MAAM,IAAI;AAClD,iBAAO,WAAW,OAAO,OAAO,WAAW,QAAQ,KAAK,GAAG,CAAC;AAAA,QAC9D;AAAA,MACF;AAAA,MAEA,cAAc,KAAK,QAAQ,SAAS;AAClC,eAAO,KAAK,OAAO,cAAc,KAAK,QAAQ,OAAO;AAAA,MACvD;AAAA,MAEA,cAAc,KAAK,QAAQ;AACzB,eAAO,KAAK,OAAO,cAAc,KAAK,MAAM;AAAA,MAC9C;AAAA;AAAA;AAAA;AAAA,MAKA,iBAAiB,KAAK;AACpB,eAAO,MAAM,iBAAiB,GAAG;AAAA,MACnC;AAAA,MAEA,cAAc,KAAK;AACjB,eAAO,MAAM,cAAc,GAAG;AAAA,MAChC;AAAA,MAEA,mBAAmB;AACjB,YAAI,KAAK,kBAAkB,MAAM;AAC/B,eAAK,cAAc,KAAK,WAAW,MAAM;AACzC,cAAI,KAAK,aAAa;AACpB,iBAAK,gBAAgB;AACrB,iBAAK,cAAc;AAEnB,kBAAM,aAAa,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,gBAAI,YAAY;AACd,sBAAQ,SAAS,MAAM;AACrB,qBAAK,YAAY,YAAY,YAAY,KAAK,UAAU;AACxD,qBAAK,gBAAgB;AACrB,qBAAK,iBAAiB;AAAA,cACxB,CAAC;AAAA,YACH;AAAA,UACF,WAAW,KAAK,aAAa;AAC3B,iBAAK,cAAc;AACnB,iBAAK,KAAK,OAAO;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AAAA,MAEA,MAAM,QAAQ,QAAQ,UAAU;AAE9B,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AAEJ,YAAI,WAAW,QAAQ,WAAW,QAAW;AAC3C,gBAAM,IAAI,UAAU,6CAA6C;AAAA,QACnE,WAAW,OAAO,OAAO,WAAW,YAAY;AAC9C,wBAAc,OAAO,iBAAiB,KAAK,qBAAqB;AAChE,mBAAS,QAAQ;AACjB,cAAI,OAAO,WAAW,YAAY;AAChC,kBAAM,WAAW,MAAM,YAAY;AAAA,UACrC;AAAA,QACF,OAAO;AACL,wBAAc,OAAO,iBAAiB,KAAK,qBAAqB;AAChE,kBAAQ,IAAID,OAAM,QAAQ,QAAQ,QAAQ;AAC1C,cAAI,CAAC,MAAM,UAAU;AACnB,qBAAS,IAAI,KAAK,SAAS,CAAC,SAAS,WAAW;AAC9C,oBAAM,WAAW,CAAC,KAAK,QAAS,MAAM,OAAO,GAAG,IAAI,QAAQ,GAAG;AAAA,YACjE,CAAC,EAAE,MAAM,CAAC,QAAQ;AAGhB,oBAAM,kBAAkB,GAAG;AAC3B,oBAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAEA,YAAI,aAAa;AACf,0BAAgB,MAAM;AAEtB,6BAAmB,WAAW,MAAM;AAClC,kBAAM,QAAQ,IAAI,MAAM,oBAAoB;AAE5C,oBAAQ,SAAS,MAAM;AACrB,oBAAM,YAAY,OAAO,KAAK,UAAU;AAAA,YAC1C,CAAC;AAED,0BAAc,KAAK;AAInB,kBAAM,WAAW,MAAM;AAAA,YAAC;AAGxB,kBAAM,QAAQ,KAAK,WAAW,QAAQ,KAAK;AAC3C,gBAAI,QAAQ,IAAI;AACd,mBAAK,WAAW,OAAO,OAAO,CAAC;AAAA,YACjC;AAEA,iBAAK,iBAAiB;AAAA,UACxB,GAAG,WAAW;AAEd,gBAAM,WAAW,CAAC,KAAK,QAAQ;AAC7B,yBAAa,gBAAgB;AAC7B,0BAAc,KAAK,GAAG;AAAA,UACxB;AAAA,QACF;AAEA,YAAI,KAAK,UAAU,CAAC,MAAM,QAAQ;AAChC,gBAAM,SAAS;AAAA,QACjB;AAEA,YAAI,MAAM,WAAW,CAAC,MAAM,QAAQ,QAAQ;AAC1C,gBAAM,QAAQ,SAAS,KAAK;AAAA,QAC9B;AAEA,YAAI,CAAC,KAAK,YAAY;AACpB,kBAAQ,SAAS,MAAM;AACrB,kBAAM,YAAY,IAAI,MAAM,gEAAgE,GAAG,KAAK,UAAU;AAAA,UAChH,CAAC;AACD,iBAAO;AAAA,QACT;AAEA,YAAI,KAAK,SAAS;AAChB,kBAAQ,SAAS,MAAM;AACrB,kBAAM,YAAY,IAAI,MAAM,wCAAwC,GAAG,KAAK,UAAU;AAAA,UACxF,CAAC;AACD,iBAAO;AAAA,QACT;AAEA,aAAK,WAAW,KAAK,KAAK;AAC1B,aAAK,iBAAiB;AACtB,eAAO;AAAA,MACT;AAAA,MAEA,MAAM;AACJ,aAAK,WAAW,IAAI;AAAA,MACtB;AAAA,MAEA,QAAQ;AACN,aAAK,WAAW,MAAM;AAAA,MACxB;AAAA,MAEA,IAAI,IAAI;AACN,aAAK,UAAU;AAGf,YAAI,CAAC,KAAK,WAAW,eAAe,KAAK,QAAQ;AAC/C,cAAI,IAAI;AACN,eAAG;AAAA,UACL,OAAO;AACL,mBAAO,KAAK,SAAS,QAAQ;AAAA,UAC/B;AAAA,QACF;AAEA,YAAI,KAAK,eAAe,CAAC,KAAK,YAAY;AAGxC,eAAK,WAAW,OAAO,QAAQ;AAAA,QACjC,OAAO;AACL,eAAK,WAAW,IAAI;AAAA,QACtB;AAEA,YAAI,IAAI;AACN,eAAK,WAAW,KAAK,OAAO,EAAE;AAAA,QAChC,OAAO;AACL,iBAAO,IAAI,KAAK,SAAS,CAAC,YAAY;AACpC,iBAAK,WAAW,KAAK,OAAO,OAAO;AAAA,UACrC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAGA,IAAAG,QAAO,QAAQH;AAEf,WAAO,UAAUG;AAAA;AAAA;;;ACzoBjB;AAAA;AAAA;AACA,QAAM,eAAe,iBAAkB;AAEvC,QAAM,OAAO,WAAY;AAAA,IAAC;AAE1B,QAAM,cAAc,CAAC,MAAM,cAAc;AACvC,YAAM,IAAI,KAAK,UAAU,SAAS;AAElC,aAAO,MAAM,KAAK,SAAY,KAAK,OAAO,GAAG,CAAC,EAAE,CAAC;AAAA,IACnD;AAEA,QAAM,WAAN,MAAe;AAAA,MACb,YAAY,QAAQ,cAAc,WAAW;AAC3C,aAAK,SAAS;AACd,aAAK,eAAe;AACpB,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AAEA,QAAM,cAAN,MAAkB;AAAA,MAChB,YAAY,UAAU;AACpB,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAEA,aAAS,uBAAuB;AAC9B,YAAM,IAAI,MAAM,uEAAuE;AAAA,IACzF;AAEA,aAAS,UAAUC,UAAS,UAAU;AACpC,UAAI,UAAU;AACZ,eAAO,EAAE,UAAoB,QAAQ,OAAU;AAAA,MACjD;AACA,UAAI;AACJ,UAAI;AACJ,YAAM,KAAK,SAAU,KAAK,QAAQ;AAChC,cAAM,IAAI,GAAG,IAAI,IAAI,MAAM;AAAA,MAC7B;AACA,YAAM,SAAS,IAAIA,SAAQ,SAAU,SAAS,QAAQ;AACpD,cAAM;AACN,cAAM;AAAA,MACR,CAAC,EAAE,MAAM,CAAC,QAAQ;AAGhB,cAAM,kBAAkB,GAAG;AAC3B,cAAM;AAAA,MACR,CAAC;AACD,aAAO,EAAE,UAAU,IAAI,OAAe;AAAA,IACxC;AAEA,aAAS,iBAAiB,MAAM,QAAQ;AACtC,aAAO,SAAS,aAAa,KAAK;AAChC,YAAI,SAAS;AAEb,eAAO,eAAe,SAAS,YAAY;AAC3C,eAAO,GAAG,SAAS,MAAM;AACvB,eAAK,IAAI,4DAA4D,GAAG;AAAA,QAC1E,CAAC;AACD,aAAK,QAAQ,MAAM;AAGnB,aAAK,KAAK,SAAS,KAAK,MAAM;AAAA,MAChC;AAAA,IACF;AAEA,QAAMC,QAAN,cAAmB,aAAa;AAAA,MAC9B,YAAY,SAASC,SAAQ;AAC3B,cAAM;AACN,aAAK,UAAU,OAAO,OAAO,CAAC,GAAG,OAAO;AAExC,YAAI,WAAW,QAAQ,cAAc,SAAS;AAG5C,iBAAO,eAAe,KAAK,SAAS,YAAY;AAAA,YAC9C,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,OAAO,QAAQ;AAAA,UACjB,CAAC;AAAA,QACH;AACA,YAAI,WAAW,QAAQ,QAAQ,OAAO,QAAQ,IAAI,KAAK;AAGrD,iBAAO,eAAe,KAAK,QAAQ,KAAK,OAAO;AAAA,YAC7C,YAAY;AAAA,UACd,CAAC;AAAA,QACH;AAEA,aAAK,QAAQ,MAAM,KAAK,QAAQ,OAAO,KAAK,QAAQ,YAAY;AAChE,aAAK,QAAQ,MAAM,KAAK,QAAQ,OAAO;AACvC,aAAK,QAAQ,UAAU,KAAK,QAAQ,WAAW;AAC/C,aAAK,QAAQ,kBAAkB,KAAK,QAAQ,mBAAmB;AAC/D,aAAK,QAAQ,qBAAqB,KAAK,QAAQ,sBAAsB;AACrE,aAAK,MAAM,KAAK,QAAQ,OAAO,WAAY;AAAA,QAAC;AAC5C,aAAK,SAAS,KAAK,QAAQ,UAAUA,WAAU,eAAc;AAC7D,aAAK,UAAU,KAAK,QAAQ,WAAW,OAAO;AAE9C,YAAI,OAAO,KAAK,QAAQ,sBAAsB,aAAa;AACzD,eAAK,QAAQ,oBAAoB;AAAA,QACnC;AAEA,aAAK,WAAW,CAAC;AACjB,aAAK,QAAQ,CAAC;AACd,aAAK,WAAW,oBAAI,QAAQ;AAC5B,aAAK,gBAAgB,CAAC;AACtB,aAAK,eAAe;AACpB,aAAK,SAAS;AACd,aAAK,QAAQ;AAAA,MACf;AAAA,MAEA,UAAU;AACR,eAAO,KAAK,SAAS,UAAU,KAAK,QAAQ;AAAA,MAC9C;AAAA,MAEA,cAAc;AACZ,eAAO,KAAK,SAAS,SAAS,KAAK,QAAQ;AAAA,MAC7C;AAAA,MAEA,cAAc;AACZ,aAAK,IAAI,aAAa;AACtB,YAAI,KAAK,OAAO;AACd,eAAK,IAAI,mBAAmB;AAC5B;AAAA,QACF;AACA,YAAI,KAAK,QAAQ;AACf,eAAK,IAAI,uBAAuB;AAChC,cAAI,KAAK,MAAM,QAAQ;AACrB,iBAAK,MAAM,MAAM,EAAE,IAAI,CAAC,SAAS;AAC/B,mBAAK,QAAQ,KAAK,MAAM;AAAA,YAC1B,CAAC;AAAA,UACH;AACA,cAAI,CAAC,KAAK,SAAS,QAAQ;AACzB,iBAAK,QAAQ;AACb,iBAAK,aAAa;AAAA,UACpB;AACA;AAAA,QACF;AAGA,YAAI,CAAC,KAAK,cAAc,QAAQ;AAC9B,eAAK,IAAI,oBAAoB;AAC7B;AAAA,QACF;AAEA,YAAI,CAAC,KAAK,MAAM,UAAU,KAAK,QAAQ,GAAG;AACxC;AAAA,QACF;AACA,cAAM,cAAc,KAAK,cAAc,MAAM;AAC7C,YAAI,KAAK,MAAM,QAAQ;AACrB,gBAAM,WAAW,KAAK,MAAM,IAAI;AAChC,uBAAa,SAAS,SAAS;AAC/B,gBAAM,SAAS,SAAS;AACxB,iBAAO,OAAO,OAAO,IAAI;AACzB,gBAAM,eAAe,SAAS;AAE9B,iBAAO,KAAK,eAAe,QAAQ,aAAa,cAAc,KAAK;AAAA,QACrE;AACA,YAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,iBAAO,KAAK,UAAU,WAAW;AAAA,QACnC;AACA,cAAM,IAAI,MAAM,sBAAsB;AAAA,MACxC;AAAA,MAEA,QAAQ,QAAQ,UAAU;AACxB,cAAM,UAAU,YAAY,KAAK,OAAO,CAAC,SAAS,KAAK,WAAW,MAAM;AAExE,YAAI,YAAY,QAAW;AACzB,uBAAa,QAAQ,SAAS;AAAA,QAChC;AAEA,aAAK,WAAW,KAAK,SAAS,OAAO,CAAC,MAAM,MAAM,MAAM;AACxD,cAAM,UAAU;AAChB,eAAO,IAAI,MAAM;AACf,kBAAQ,KAAK,UAAU,MAAM;AAE7B,cAAI,OAAO,aAAa,YAAY;AAClC,qBAAS;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MAEA,QAAQ,IAAI;AACV,YAAI,KAAK,QAAQ;AACf,gBAAM,MAAM,IAAI,MAAM,iDAAiD;AACvE,iBAAO,KAAK,GAAG,GAAG,IAAI,KAAK,QAAQ,OAAO,GAAG;AAAA,QAC/C;AAEA,cAAM,WAAW,UAAU,KAAK,SAAS,EAAE;AAC3C,cAAM,SAAS,SAAS;AAGxB,YAAI,KAAK,QAAQ,KAAK,KAAK,MAAM,QAAQ;AAEvC,cAAI,KAAK,MAAM,QAAQ;AACrB,oBAAQ,SAAS,MAAM,KAAK,YAAY,CAAC;AAAA,UAC3C;AAEA,cAAI,CAAC,KAAK,QAAQ,yBAAyB;AACzC,iBAAK,cAAc,KAAK,IAAI,YAAY,SAAS,QAAQ,CAAC;AAC1D,mBAAO;AAAA,UACT;AAEA,gBAAM,gBAAgB,CAAC,KAAK,KAAK,SAAS;AACxC,yBAAa,GAAG;AAChB,qBAAS,SAAS,KAAK,KAAK,IAAI;AAAA,UAClC;AAEA,gBAAM,cAAc,IAAI,YAAY,aAAa;AAGjD,gBAAM,MAAM,WAAW,MAAM;AAG3B,wBAAY,KAAK,eAAe,CAAC,MAAM,EAAE,aAAa,aAAa;AACnE,wBAAY,WAAW;AACvB,qBAAS,SAAS,IAAI,MAAM,yCAAyC,CAAC;AAAA,UACxE,GAAG,KAAK,QAAQ,uBAAuB;AAEvC,cAAI,IAAI,OAAO;AACb,gBAAI,MAAM;AAAA,UACZ;AAEA,eAAK,cAAc,KAAK,WAAW;AACnC,iBAAO;AAAA,QACT;AAEA,aAAK,UAAU,IAAI,YAAY,SAAS,QAAQ,CAAC;AAEjD,eAAO;AAAA,MACT;AAAA,MAEA,UAAU,aAAa;AACrB,cAAM,SAAS,IAAI,KAAK,OAAO,KAAK,OAAO;AAC3C,aAAK,SAAS,KAAK,MAAM;AACzB,cAAM,eAAe,iBAAiB,MAAM,MAAM;AAElD,aAAK,IAAI,yBAAyB;AAGlC,YAAI;AACJ,YAAI,aAAa;AACjB,YAAI,KAAK,QAAQ,yBAAyB;AACxC,gBAAM,WAAW,MAAM;AACrB,iBAAK,IAAI,8BAA8B;AACvC,yBAAa;AAEb,mBAAO,aAAa,OAAO,WAAW,OAAO,QAAQ,IAAI,OAAO,IAAI;AAAA,UACtE,GAAG,KAAK,QAAQ,uBAAuB;AAAA,QACzC;AAEA,aAAK,IAAI,uBAAuB;AAChC,eAAO,QAAQ,CAAC,QAAQ;AACtB,cAAI,KAAK;AACP,yBAAa,GAAG;AAAA,UAClB;AACA,iBAAO,GAAG,SAAS,YAAY;AAC/B,cAAI,KAAK;AACP,iBAAK,IAAI,4BAA4B,GAAG;AAExC,iBAAK,WAAW,KAAK,SAAS,OAAO,CAAC,MAAM,MAAM,MAAM;AACxD,gBAAI,YAAY;AACd,oBAAM,IAAI,MAAM,mDAAmD,EAAE,OAAO,IAAI,CAAC;AAAA,YACnF;AAGA,iBAAK,YAAY;AAEjB,gBAAI,CAAC,YAAY,UAAU;AACzB,0BAAY,SAAS,KAAK,QAAW,IAAI;AAAA,YAC3C;AAAA,UACF,OAAO;AACL,iBAAK,IAAI,sBAAsB;AAE/B,gBAAI,KAAK,QAAQ,uBAAuB,GAAG;AACzC,oBAAM,qBAAqB,WAAW,MAAM;AAC1C,qBAAK,IAAI,uCAAuC;AAChD,qBAAK,SAAS,IAAI,MAAM;AACxB,sBAAM,YAAY,KAAK,MAAM,UAAU,CAAC,aAAa,SAAS,WAAW,MAAM;AAC/E,oBAAI,cAAc,IAAI;AACpB,uBAAK;AAAA,oBACH;AAAA,oBACA,IAAI,YAAY,CAACC,MAAKC,SAAQ,kBAAkB,cAAc,CAAC;AAAA,oBAC/D;AAAA,oBACA;AAAA,kBACF;AAAA,gBACF;AAAA,cACF,GAAG,KAAK,QAAQ,qBAAqB,GAAI;AAEzC,iCAAmB,MAAM;AACzB,qBAAO,KAAK,OAAO,MAAM,aAAa,kBAAkB,CAAC;AAAA,YAC3D;AAEA,mBAAO,KAAK,eAAe,QAAQ,aAAa,cAAc,IAAI;AAAA,UACpE;AAAA,QACF,CAAC;AAAA,MACH;AAAA;AAAA,MAGA,eAAe,QAAQ,aAAa,cAAc,OAAO;AACvD,YAAI,OAAO;AACT,eAAK,KAAK,WAAW,MAAM;AAAA,QAC7B;AAEA,aAAK,KAAK,WAAW,MAAM;AAE3B,eAAO,UAAU,KAAK,aAAa,QAAQ,YAAY;AAEvD,eAAO,eAAe,SAAS,YAAY;AAE3C,YAAI,CAAC,YAAY,UAAU;AACzB,cAAI,SAAS,KAAK,QAAQ,QAAQ;AAChC,iBAAK,QAAQ,OAAO,QAAQ,CAAC,QAAQ;AACnC,kBAAI,KAAK;AACP,uBAAO,QAAQ,GAAG;AAClB,uBAAO,YAAY,SAAS,KAAK,QAAW,IAAI;AAAA,cAClD;AAEA,0BAAY,SAAS,QAAW,QAAQ,OAAO,OAAO;AAAA,YACxD,CAAC;AAAA,UACH,OAAO;AACL,wBAAY,SAAS,QAAW,QAAQ,OAAO,OAAO;AAAA,UACxD;AAAA,QACF,OAAO;AACL,cAAI,SAAS,KAAK,QAAQ,QAAQ;AAChC,iBAAK,QAAQ,OAAO,QAAQ,OAAO,OAAO;AAAA,UAC5C,OAAO;AACL,mBAAO,QAAQ;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA,aAAa,QAAQ,cAAc;AACjC,YAAI,WAAW;AAEf,eAAO,CAAC,QAAQ;AACd,cAAI,UAAU;AACZ,iCAAqB;AAAA,UACvB;AAEA,qBAAW;AACX,eAAK,SAAS,QAAQ,cAAc,GAAG;AAAA,QACzC;AAAA,MACF;AAAA;AAAA;AAAA,MAIA,SAAS,QAAQ,cAAc,KAAK;AAClC,eAAO,GAAG,SAAS,YAAY;AAE/B,eAAO,iBAAiB,OAAO,iBAAiB,KAAK;AAErD,aAAK,KAAK,WAAW,KAAK,MAAM;AAGhC,YAAI,OAAO,KAAK,UAAU,CAAC,OAAO,cAAc,OAAO,WAAW,OAAO,iBAAiB,KAAK,QAAQ,SAAS;AAC9G,cAAI,OAAO,iBAAiB,KAAK,QAAQ,SAAS;AAChD,iBAAK,IAAI,wBAAwB;AAAA,UACnC;AAEA,iBAAO,KAAK,QAAQ,QAAQ,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA,QACzD;AAEA,cAAM,YAAY,KAAK,SAAS,IAAI,MAAM;AAC1C,YAAI,WAAW;AACb,eAAK,IAAI,uBAAuB;AAChC,eAAK,SAAS,OAAO,MAAM;AAC3B,iBAAO,KAAK,QAAQ,QAAQ,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA,QACzD;AAGA,YAAI;AACJ,YAAI,KAAK,QAAQ,qBAAqB,KAAK,YAAY,GAAG;AACxD,gBAAM,WAAW,MAAM;AACrB,iBAAK,IAAI,oBAAoB;AAC7B,iBAAK,QAAQ,QAAQ,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA,UAClD,GAAG,KAAK,QAAQ,iBAAiB;AAEjC,cAAI,KAAK,QAAQ,iBAAiB;AAEhC,gBAAI,MAAM;AAAA,UACZ;AAAA,QACF;AAEA,YAAI,KAAK,QAAQ,iBAAiB;AAChC,iBAAO,MAAM;AAAA,QACf;AAEA,aAAK,MAAM,KAAK,IAAI,SAAS,QAAQ,cAAc,GAAG,CAAC;AACvD,aAAK,YAAY;AAAA,MACnB;AAAA,MAEA,MAAM,MAAM,QAAQ,IAAI;AAEtB,YAAI,OAAO,SAAS,YAAY;AAC9B,gBAAMC,YAAW,UAAU,KAAK,SAAS,IAAI;AAC7C,uBAAa,WAAY;AACvB,mBAAOA,UAAS,SAAS,IAAI,MAAM,0EAA0E,CAAC;AAAA,UAChH,CAAC;AACD,iBAAOA,UAAS;AAAA,QAClB;AAGA,YAAI,OAAO,WAAW,YAAY;AAChC,eAAK;AACL,mBAAS;AAAA,QACX;AACA,cAAM,WAAW,UAAU,KAAK,SAAS,EAAE;AAC3C,aAAK,SAAS;AAEd,aAAK,QAAQ,CAAC,KAAK,WAAW;AAC5B,cAAI,KAAK;AACP,mBAAO,GAAG,GAAG;AAAA,UACf;AAEA,cAAI,iBAAiB;AACrB,gBAAM,UAAU,CAACF,SAAQ;AACvB,gBAAI,gBAAgB;AAClB;AAAA,YACF;AACA,6BAAiB;AACjB,mBAAO,QAAQA,IAAG;AAClB,eAAGA,IAAG;AAAA,UACR;AAEA,iBAAO,KAAK,SAAS,OAAO;AAC5B,eAAK,IAAI,mBAAmB;AAC5B,cAAI;AACF,mBAAO,MAAM,MAAM,QAAQ,CAACA,MAAK,QAAQ;AACvC,mBAAK,IAAI,kBAAkB;AAC3B,qBAAO,eAAe,SAAS,OAAO;AACtC,kBAAI,gBAAgB;AAClB;AAAA,cACF;AACA,+BAAiB;AACjB,qBAAO,QAAQA,IAAG;AAClB,kBAAIA,MAAK;AACP,uBAAO,GAAGA,IAAG;AAAA,cACf;AACA,qBAAO,GAAG,QAAW,GAAG;AAAA,YAC1B,CAAC;AAAA,UACH,SAASA,MAAK;AACZ,mBAAO,QAAQA,IAAG;AAClB,mBAAO,GAAGA,IAAG;AAAA,UACf;AAAA,QACF,CAAC;AACD,eAAO,SAAS;AAAA,MAClB;AAAA,MAEA,IAAI,IAAI;AACN,aAAK,IAAI,QAAQ;AACjB,YAAI,KAAK,QAAQ;AACf,gBAAM,MAAM,IAAI,MAAM,mCAAmC;AACzD,iBAAO,KAAK,GAAG,GAAG,IAAI,KAAK,QAAQ,OAAO,GAAG;AAAA,QAC/C;AACA,aAAK,SAAS;AACd,cAAM,WAAW,UAAU,KAAK,SAAS,EAAE;AAC3C,aAAK,eAAe,SAAS;AAC7B,aAAK,YAAY;AACjB,eAAO,SAAS;AAAA,MAClB;AAAA,MAEA,IAAI,eAAe;AACjB,eAAO,KAAK,cAAc;AAAA,MAC5B;AAAA,MAEA,IAAI,YAAY;AACd,eAAO,KAAK,MAAM;AAAA,MACpB;AAAA,MAEA,IAAI,eAAe;AACjB,eAAO,KAAK,SAAS,OAAO,CAAC,KAAK,WAAW,OAAO,KAAK,SAAS,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC;AAAA,MAC3F;AAAA,MAEA,IAAI,aAAa;AACf,eAAO,KAAK,SAAS;AAAA,MACvB;AAAA,IACF;AACA,WAAO,UAAUF;AAAA;AAAA;;;AC9djB;AAAA;AAAA,UAAM,IAAI,MAAM,kEAAkE;AAAA;AAAA;;;ACAlF,IAAAK,iBAAA;AAAA;AAAA;AAEA,QAAM,eAAe,iBAAkB;AACvC,QAAM,OAAO;AACb,QAAM,QAAQ;AAEd,QAAM,cAAe,OAAO,UAAU,SAAU,QAAQ,QAAQ,UAAU;AACxE,mBAAa,KAAK,IAAI;AACtB,eAAS,MAAM,qBAAqB,QAAQ,QAAQ,QAAQ;AAC5D,WAAK,OAAO,OAAO;AACnB,WAAK,SAAS,OAAO;AACrB,WAAK,OAAO,OAAO;AACnB,WAAK,YAAY,OAAO;AACxB,WAAK,WAAW,OAAO;AACvB,WAAK,QAAQ;AACb,WAAK,aAAa,OAAO,YAAY;AAOrC,WAAK,iBAAiB;AACtB,WAAK;AAAA,QACH;AAAA,SACA,SAAU,OAAO;AACf,cAAI,UAAU,MAAO,MAAK,iBAAiB;AAAA,QAC7C,GAAE,KAAK,IAAI;AAAA,MACb;AAAA,IACF;AAEA,SAAK,SAAS,aAAa,YAAY;AAEvC,QAAM,gBAAgB;AAAA,MACpB,UAAU;AAAA,MACV,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,gBAAgB;AAAA,IAClB;AAEA,gBAAY,UAAU,cAAc,SAAU,KAAK;AAEjD,YAAM,SAAS,KAAK,OAAO,GAAG,kBAAkB;AAChD,UAAI,QAAQ;AACV,mBAAW,OAAO,QAAQ;AACxB,gBAAM,sBAAsB,cAAc,GAAG,KAAK;AAClD,cAAI,mBAAmB,IAAI,OAAO,GAAG;AAAA,QACvC;AAAA,MACF;AACA,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,GAAG;AAAA,MACnB,OAAO;AACL,aAAK,KAAK,SAAS,GAAG;AAAA,MACxB;AACA,WAAK,QAAQ;AAAA,IACf;AAEA,gBAAY,UAAU,OAAO,SAAU,WAAW,WAAW;AAC3D,aAAO,KAAK,YAAY,EAAE,KAAK,WAAW,SAAS;AAAA,IACrD;AAEA,gBAAY,UAAU,QAAQ,SAAU,UAAU;AAChD,aAAO,KAAK,YAAY,EAAE,MAAM,QAAQ;AAAA,IAC1C;AAEA,gBAAY,UAAU,cAAc,WAAY;AAC9C,UAAI,KAAK,SAAU,QAAO,KAAK;AAC/B,WAAK,WAAW,IAAI;AAAA,SAClB,SAAU,SAAS,QAAQ;AACzB,eAAK,MAAM,OAAO,OAAO;AACzB,eAAK,MAAM,SAAS,MAAM;AAAA,QAC5B,GAAE,KAAK,IAAI;AAAA,MACb;AACA,aAAO,KAAK;AAAA,IACd;AAEA,gBAAY,UAAU,SAAS,SAAU,QAAQ;AAC/C,WAAK,QAAQ;AACb,YAAM,OAAO;AACb,WAAK,SAAS,OAAO;AACrB,aAAO,OAAO,YAAY,KAAK;AAE/B,UAAI,QAAQ,SAAU,KAAK,MAAM,SAAS;AACxC,eAAO,OAAO,YAAY;AAC1B,qBAAa,WAAY;AACvB,eAAK,KAAK,OAAO;AAAA,QACnB,CAAC;AAGD,YAAI,KAAK;AACP,iBAAO,KAAK,YAAY,GAAG;AAAA,QAC7B;AAGA,YAAI,KAAK,gBAAgB;AACvB,cAAI,QAAQ,SAAS,GAAG;AACtB,iBAAK,QAAQ,CAAC,WAAW,MAAM;AAC7B,wBAAU,QAAQ,CAAC,QAAQ;AACzB,qBAAK,KAAK,OAAO,KAAK,QAAQ,CAAC,CAAC;AAAA,cAClC,CAAC;AAAA,YACH,CAAC;AAAA,UACH,OAAO;AACL,iBAAK,QAAQ,SAAU,KAAK;AAC1B,mBAAK,KAAK,OAAO,KAAK,OAAO;AAAA,YAC/B,CAAC;AAAA,UACH;AAAA,QACF;AAGA,aAAK,QAAQ;AACb,aAAK,KAAK,OAAO,OAAO;AACxB,YAAI,KAAK,UAAU;AACjB,eAAK,SAAS,MAAM,OAAO;AAAA,QAC7B;AAAA,MACF;AAEA,UAAI,QAAQ,QAAQ;AAClB,gBAAQ,QAAQ,OAAO,KAAK,KAAK;AAAA,MACnC;AAGA,UAAI,KAAK,MAAM;AACb,YAAI,KAAK,KAAK,SAAS,IAAI;AACzB,kBAAQ,MAAM,gEAAgE;AAC9E,kBAAQ,MAAM,wBAAwB,KAAK,MAAM,KAAK,KAAK,MAAM;AACjE,kBAAQ,MAAM,8DAA8D;AAAA,QAC9E;AACA,cAAM,UAAU,KAAK,UAAU,CAAC,GAAG,IAAI,MAAM,YAAY;AAIzD,YAAI,OAAO,aAAa,KAAK,IAAI,GAAG;AAClC,cAAI,KAAK,QAAQ,OAAO,aAAa,KAAK,IAAI,MAAM,KAAK,MAAM;AAC7D,kBAAM,MAAM,IAAI,MAAM,yCAAyC,KAAK,IAAI,sCAAsC;AAC9G,mBAAO,MAAM,GAAG;AAAA,UAClB;AACA,iBAAO,OAAO,OAAO,QAAQ,KAAK,MAAM,QAAQ,KAAK;AAAA,QACvD;AAEA,eAAO,OAAO,OAAO,QAAQ,KAAK,MAAM,KAAK,MAAM,OAAO,QAAQ,SAAU,KAAK;AAC/E,cAAI,IAAK,QAAO,MAAM,GAAG;AACzB,iBAAO,aAAa,KAAK,IAAI,IAAI,KAAK;AACtC,iBAAO,KAAK,OAAO,QAAQ,KAAK,MAAM,QAAQ,KAAK;AAAA,QACrD,CAAC;AAAA,MACH,WAAW,KAAK,QAAQ;AACtB,YAAI,CAAC,MAAM,QAAQ,KAAK,MAAM,GAAG;AAC/B,gBAAM,MAAM,IAAI,MAAM,+BAA+B;AACrD,iBAAO,MAAM,GAAG;AAAA,QAClB;AACA,cAAM,OAAO,KAAK,OAAO,IAAI,MAAM,YAAY;AAC/C,eAAO,OAAO,MAAM,KAAK,MAAM,MAAM,KAAK;AAAA,MAC5C,WAAW,KAAK,cAAc,YAAY;AACxC,eAAO,OAAO,MAAM,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,MAC1C,OAAO;AACL,eAAO,OAAO,MAAM,KAAK,MAAM,KAAK;AAAA,MACtC;AAAA,IACF;AAAA;AAAA;;;ACpKA,IAAAC,kBAAA;AAAA;AAAA;AAGA,QAAI;AAEJ,QAAI;AAEF,eAAS;AAAA,IACX,SAAS,GAAG;AACV,YAAM;AAAA,IACR;AACA,QAAMC,iBAAgB;AACtB,QAAM,eAAe,iBAAkB;AACvC,QAAM,OAAO;AACb,QAAM,uBAAuB;AAE7B,QAAM,cAAc;AAEpB,QAAMC,UAAU,OAAO,UAAU,SAAU,QAAQ;AACjD,mBAAa,KAAK,IAAI;AACtB,eAAS,UAAU,CAAC;AAEpB,WAAK,WAAW,OAAO,WAAW,OAAO;AACzC,WAAK,SAAS,IAAID,eAAc,OAAO,KAAK;AAE5C,WAAK,SAAS,IAAI,OAAO;AAAA,QACvB,OAAO,KAAK;AAAA,MACd,CAAC;AAED,WAAK,cAAc,CAAC;AACpB,WAAK,UAAU;AACf,WAAK,cAAc;AACnB,WAAK,aAAa;AAClB,WAAK,aAAa;AAIlB,YAAM,KAAM,KAAK,uBAAuB,IAAI,qBAAqB,MAAM;AACvE,UAAI,OAAO,uBAAwB,IAAG,yBAAyB,OAAO;AACtE,WAAK,OAAO,GAAG;AAIf,aAAO,eAAe,MAAM,YAAY;AAAA,QACtC,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO,GAAG;AAAA,MACZ,CAAC;AACD,WAAK,WAAW,GAAG;AACnB,WAAK,OAAO,GAAG;AACf,WAAK,OAAO,GAAG;AAGf,WAAK,eAAe,CAAC;AAAA,IACvB;AAEA,IAAAC,QAAO,QAAQ;AAEf,SAAK,SAASA,SAAQ,YAAY;AAElC,IAAAA,QAAO,UAAU,mBAAmB,SAAU,KAAK;AACjD,YAAM,eAAe,CAAC,UAAU;AAC9B,gBAAQ,SAAS,MAAM;AACrB,gBAAM,SAAS,KAAK;AACpB,gBAAM,YAAY,GAAG;AAAA,QACvB,CAAC;AAAA,MACH;AAEA,UAAI,KAAK,gBAAgB,GAAG;AAC1B,qBAAa,KAAK,YAAY;AAC9B,aAAK,eAAe;AAAA,MACtB;AAEA,WAAK,YAAY,QAAQ,YAAY;AACrC,WAAK,YAAY,SAAS;AAAA,IAC5B;AAKA,IAAAA,QAAO,UAAU,WAAW,SAAU,IAAI;AACxC,YAAM,OAAO;AAEb,UAAI,KAAK,aAAa;AACpB,gBAAQ,SAAS,MAAM,GAAG,IAAI,MAAM,+DAA+D,CAAC,CAAC;AACrG;AAAA,MACF;AAEA,WAAK,cAAc;AAEnB,WAAK,qBAAqB,yBAAyB,SAAU,KAAK,WAAW;AAC3E,YAAI,KAAK,qBAAqB,uBAAwB,aAAY,KAAK,qBAAqB;AAC5F,YAAI,IAAK,QAAO,GAAG,GAAG;AACtB,aAAK,OAAO,QAAQ,WAAW,SAAUC,MAAK;AAC5C,cAAIA,MAAK;AACP,iBAAK,OAAO,IAAI;AAChB,mBAAO,GAAGA,IAAG;AAAA,UACf;AAGA,eAAK,aAAa;AAGlB,eAAK,OAAO,GAAG,SAAS,SAAUA,MAAK;AACrC,iBAAK,aAAa;AAClB,iBAAK,iBAAiBA,IAAG;AACzB,iBAAK,KAAK,SAASA,IAAG;AAAA,UACxB,CAAC;AAED,eAAK,OAAO,GAAG,gBAAgB,SAAU,KAAK;AAC5C,iBAAK,KAAK,gBAAgB;AAAA,cACxB,SAAS,IAAI;AAAA,cACb,SAAS,IAAI;AAAA,YACf,CAAC;AAAA,UACH,CAAC;AAGD,eAAK,KAAK,SAAS;AACnB,eAAK,iBAAiB,IAAI;AAE1B,aAAG;AAAA,QACL,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,IAAAD,QAAO,UAAU,UAAU,SAAU,UAAU;AAC7C,UAAI,UAAU;AACZ,aAAK,SAAS,QAAQ;AACtB;AAAA,MACF;AAEA,aAAO,IAAI,KAAK,SAAS,CAAC,SAAS,WAAW;AAC5C,aAAK,SAAS,CAAC,UAAU;AACvB,cAAI,OAAO;AACT,mBAAO,KAAK;AAAA,UACd,OAAO;AACL,oBAAQ;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAYA,IAAAA,QAAO,UAAU,QAAQ,SAAU,QAAQ,QAAQ,UAAU;AAC3D,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,WAAW,QAAQ,WAAW,QAAW;AAC3C,cAAM,IAAI,UAAU,6CAA6C;AAAA,MACnE,WAAW,OAAO,OAAO,WAAW,YAAY;AAC9C,sBAAc,OAAO,iBAAiB,KAAK,qBAAqB;AAChE,iBAAS,QAAQ;AAEjB,YAAI,OAAO,WAAW,YAAY;AAChC,iBAAO,WAAW;AAAA,QACpB;AAAA,MACF,OAAO;AACL,sBAAc,OAAO,iBAAiB,KAAK,qBAAqB;AAChE,gBAAQ,IAAI,YAAY,QAAQ,QAAQ,QAAQ;AAChD,YAAI,CAAC,MAAM,UAAU;AACnB,cAAI,YAAY;AAChB,mBAAS,IAAI,KAAK,SAAS,CAAC,SAAS,WAAW;AAC9C,yBAAa;AACb,wBAAY;AAAA,UACd,CAAC,EAAE,MAAM,CAAC,QAAQ;AAChB,kBAAM,kBAAkB,GAAG;AAC3B,kBAAM;AAAA,UACR,CAAC;AACD,gBAAM,WAAW,CAAC,KAAK,QAAS,MAAM,UAAU,GAAG,IAAI,WAAW,GAAG;AAAA,QACvE;AAAA,MACF;AAEA,UAAI,aAAa;AACf,wBAAgB,MAAM;AAEtB,2BAAmB,WAAW,MAAM;AAClC,gBAAM,QAAQ,IAAI,MAAM,oBAAoB;AAE5C,kBAAQ,SAAS,MAAM;AACrB,kBAAM,YAAY,OAAO,KAAK,UAAU;AAAA,UAC1C,CAAC;AAED,wBAAc,KAAK;AAInB,gBAAM,WAAW,MAAM;AAAA,UAAC;AAGxB,gBAAM,QAAQ,KAAK,YAAY,QAAQ,KAAK;AAC5C,cAAI,QAAQ,IAAI;AACd,iBAAK,YAAY,OAAO,OAAO,CAAC;AAAA,UAClC;AAEA,eAAK,iBAAiB;AAAA,QACxB,GAAG,WAAW;AAEd,cAAM,WAAW,CAAC,KAAK,QAAQ;AAC7B,uBAAa,gBAAgB;AAC7B,wBAAc,KAAK,GAAG;AAAA,QACxB;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,YAAY;AACpB,cAAM,SAAS,KAAK;AACpB,gBAAQ,SAAS,MAAM;AACrB,gBAAM,YAAY,IAAI,MAAM,gEAAgE,CAAC;AAAA,QAC/F,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,SAAS;AAChB,cAAM,SAAS,KAAK;AACpB,gBAAQ,SAAS,MAAM;AACrB,gBAAM,YAAY,IAAI,MAAM,wCAAwC,CAAC;AAAA,QACvE,CAAC;AACD,eAAO;AAAA,MACT;AAEA,WAAK,YAAY,KAAK,KAAK;AAC3B,WAAK,iBAAiB;AACtB,aAAO;AAAA,IACT;AAGA,IAAAA,QAAO,UAAU,MAAM,SAAU,IAAI;AACnC,YAAM,OAAO;AAEb,WAAK,UAAU;AAEf,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,KAAK,WAAW,KAAK,IAAI,KAAK,MAAM,EAAE,CAAC;AAAA,MAC9C;AACA,UAAI;AACJ,UAAI,CAAC,IAAI;AACP,iBAAS,IAAI,KAAK,SAAS,SAAU,SAAS,QAAQ;AACpD,eAAK,CAAC,QAAS,MAAM,OAAO,GAAG,IAAI,QAAQ;AAAA,QAC7C,CAAC;AAAA,MACH;AACA,WAAK,OAAO,IAAI,WAAY;AAC1B,aAAK,iBAAiB,IAAI,MAAM,uBAAuB,CAAC;AAExD,gBAAQ,SAAS,MAAM;AACrB,eAAK,KAAK,KAAK;AACf,cAAI,GAAI,IAAG;AAAA,QACb,CAAC;AAAA,MACH,CAAC;AACD,aAAO;AAAA,IACT;AAEA,IAAAA,QAAO,UAAU,kBAAkB,WAAY;AAC7C,aAAO,KAAK,gBAAgB,KAAK,aAAa,UAAU,WAAW,KAAK,aAAa,UAAU;AAAA,IACjG;AAEA,IAAAA,QAAO,UAAU,mBAAmB,SAAU,mBAAmB;AAC/D,UAAI,CAAC,KAAK,YAAY;AACpB;AAAA,MACF;AACA,UAAI,KAAK,gBAAgB,GAAG;AAC1B;AAAA,MACF;AACA,YAAM,QAAQ,KAAK,YAAY,MAAM;AACrC,UAAI,CAAC,OAAO;AACV,YAAI,CAAC,mBAAmB;AACtB,eAAK,KAAK,OAAO;AAAA,QACnB;AACA;AAAA,MACF;AACA,WAAK,eAAe;AACpB,YAAM,OAAO,IAAI;AACjB,YAAM,OAAO;AACb,YAAM,KAAK,SAAS,WAAY;AAC9B,aAAK,iBAAiB;AAAA,MACxB,CAAC;AAAA,IACH;AAGA,IAAAA,QAAO,UAAU,SAAS,SAAU,OAAO;AACzC,UAAI,KAAK,iBAAiB,OAAO;AAC/B,aAAK,OAAO,OAAO,WAAY;AAAA,QAAC,CAAC;AAAA,MACnC,WAAW,KAAK,YAAY,QAAQ,KAAK,MAAM,IAAI;AACjD,aAAK,YAAY,OAAO,KAAK,YAAY,QAAQ,KAAK,GAAG,CAAC;AAAA,MAC5D;AAAA,IACF;AAEA,IAAAA,QAAO,UAAU,MAAM,WAAY;AAAA,IAAC;AACpC,IAAAA,QAAO,UAAU,QAAQ,WAAY;AAAA,IAAC;AAEtC,IAAAA,QAAO,UAAU,gBAAgB,SAAU,KAAK,QAAQ,SAAS;AAC/D,aAAO,KAAK,OAAO,cAAc,KAAK,QAAQ,OAAO;AAAA,IACvD;AAEA,IAAAA,QAAO,UAAU,gBAAgB,SAAU,KAAK,QAAQ;AACtD,aAAO,KAAK,OAAO,cAAc,KAAK,MAAM;AAAA,IAC9C;AAAA;AAAA;;;ACnTA;AAAA;AAAA;AACA,WAAO,UAAU;AAAA;AAAA;;;ACDjB,IAAAE,eAAA;AAAA;AAAA;AAEA,QAAMC,UAAS;AACf,QAAMC,YAAW;AACjB,QAAMC,cAAa;AACnB,QAAMC,UAAS;AACf,QAAM,QAAQ;AACd,QAAMC,QAAO;AACb,QAAMC,iBAAgB;AACtB,QAAM,EAAE,eAAAC,eAAc,IAAI;AAC1B,QAAM,EAAE,kBAAAC,mBAAkB,eAAAC,eAAc,IAAI;AAE5C,QAAM,cAAc,CAACR,YAAW;AAC9B,aAAO,MAAM,kBAAkBI,MAAK;AAAA,QAClC,YAAY,SAAS;AACnB,gBAAM,SAASJ,OAAM;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAEA,QAAM,KAAK,SAAU,mBAAmB;AACtC,WAAK,WAAWC;AAChB,WAAK,SAAS;AACd,WAAK,QAAQ,KAAK,OAAO;AACzB,WAAK,OAAO,YAAY,KAAK,MAAM;AACnC,WAAK,SAAS,CAAC;AACf,WAAK,aAAaC;AAClB,WAAK,QAAQ;AACb,WAAK,gBAAgBI;AACrB,WAAK,gBAAgBD;AACrB,WAAK,mBAAmBE;AACxB,WAAK,gBAAgBC;AACrB,WAAK,SAASL;AACd,WAAK,QAAQ;AAAA,IACf;AAEA,QAAI,OAAO,QAAQ,IAAI,yBAAyB,aAAa;AAC3D,aAAO,UAAU,IAAI,GAAG,gBAAmB;AAAA,IAC7C,OAAO;AACL,aAAO,UAAU,IAAI,GAAGH,OAAM;AAG9B,aAAO,eAAe,OAAO,SAAS,UAAU;AAAA,QAC9C,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,MAAM;AACJ,cAAI,SAAS;AACb,cAAI;AACF,qBAAS,IAAI,GAAG,gBAAmB;AAAA,UACrC,SAAS,KAAK;AACZ,gBAAI,IAAI,SAAS,oBAAoB;AACnC,oBAAM;AAAA,YACR;AAAA,UACF;AAGA,iBAAO,eAAe,OAAO,SAAS,UAAU;AAAA,YAC9C,OAAO;AAAA,UACT,CAAC;AAED,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;;;AC9DA,iBAAe;AAGR,IAAM,SAAS,WAAAS,QAAG;AAClB,IAAM,OAAO,WAAAA,QAAG;AAChB,IAAM,aAAa,WAAAA,QAAG;AACtB,IAAM,QAAQ,WAAAA,QAAG;AACjB,IAAM,QAAQ,WAAAA,QAAG;AACjB,IAAM,gBAAgB,WAAAA,QAAG;AACzB,IAAM,mBAAmB,WAAAA,QAAG;AAC5B,IAAM,gBAAgB,WAAAA,QAAG;AACzB,IAAM,SAAS,WAAAA,QAAG;AAClB,IAAM,gBAAgB,WAAAA,QAAG;AAGzB,IAAM,WAAW,WAAAA,QAAG;AAG3B,IAAO,cAAQ,WAAAA;", "names": ["bits", "value", "elementType", "i", "defaults", "escapeIdentifier", "escapeLiteral", "require_utils", "types", "TypeOverrides", "config", "defaults", "types", "Result", "Result", "Query", "DatabaseError", "password", "query", "types", "getStream", "getSecureStream", "Connection", "require_stream", "TypeOverrides", "Query", "defaults", "Connection", "Client", "Promise", "Pool", "Client", "err", "client", "response", "require_query", "require_client", "TypeOverrides", "Client", "err", "require_lib", "Client", "defaults", "Connection", "Result", "Pool", "TypeOverrides", "DatabaseError", "escapeIdentifier", "escapeLiteral", "pg"]}